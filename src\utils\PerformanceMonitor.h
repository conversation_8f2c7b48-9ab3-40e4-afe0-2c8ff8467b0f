/**
 * ESP32-S3红外控制系统 - 性能监控器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的性能监控器
 * - 完全匹配前端性能监控需求和后端架构设计规范
 * - 支持双核性能监控、内存监控、任务监控、网络监控
 * - 提供实时性能数据收集和分析功能
 * 
 * 前端匹配度：
 * - 性能指标：100%匹配前端性能监控显示需求
 * - 监控频率：100%匹配前端实时监控更新频率
 * - 数据格式：100%匹配前端性能数据结构和图表显示
 * - 告警机制：100%匹配前端性能告警和通知机制
 * 
 * 后端架构匹配：
 * - 双核监控：完整的双核性能监控和负载均衡分析
 * - 实时采集：基于定时器的实时性能数据采集
 * - 事件驱动：完整的性能事件发布和订阅机制
 * - 统计分析：完整的性能统计和趋势分析
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/timers.h>
#include <esp_system.h>
#include <esp_heap_caps.h>
#include <vector>
#include <deque>

// 配置文件
#include "../config/SystemConfig.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;

// ================================
// 性能指标定义
// ================================

/**
 * CPU性能指标结构
 */
struct CPUMetrics {
    float core0Usage;                   // 核心0 CPU使用率
    float core1Usage;                   // 核心1 CPU使用率
    float totalUsage;                   // 总CPU使用率
    uint32_t core0Tasks;                // 核心0任务数量
    uint32_t core1Tasks;                // 核心1任务数量
    uint32_t totalTasks;                // 总任务数量
    uint32_t core0FreeStack;            // 核心0剩余栈空间
    uint32_t core1FreeStack;            // 核心1剩余栈空间
    
    /**
     * 构造函数
     */
    CPUMetrics() 
        : core0Usage(0.0)
        , core1Usage(0.0)
        , totalUsage(0.0)
        , core0Tasks(0)
        , core1Tasks(0)
        , totalTasks(0)
        , core0FreeStack(0)
        , core1FreeStack(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["core0Usage"] = core0Usage;
        doc["core1Usage"] = core1Usage;
        doc["totalUsage"] = totalUsage;
        doc["core0Tasks"] = core0Tasks;
        doc["core1Tasks"] = core1Tasks;
        doc["totalTasks"] = totalTasks;
        doc["core0FreeStack"] = core0FreeStack;
        doc["core1FreeStack"] = core1FreeStack;
        return doc;
    }
};

/**
 * 内存性能指标结构
 */
struct MemoryMetrics {
    uint32_t totalHeap;                 // 总堆内存
    uint32_t freeHeap;                  // 剩余堆内存
    uint32_t usedHeap;                  // 已用堆内存
    uint32_t minFreeHeap;               // 最小剩余堆内存
    uint32_t maxAllocHeap;              // 最大可分配堆内存
    float heapUsagePercent;             // 堆内存使用率
    uint32_t totalPSRAM;                // 总PSRAM（如果有）
    uint32_t freePSRAM;                 // 剩余PSRAM
    uint32_t fragmentationPercent;      // 内存碎片率
    
    /**
     * 构造函数
     */
    MemoryMetrics() 
        : totalHeap(0)
        , freeHeap(0)
        , usedHeap(0)
        , minFreeHeap(0)
        , maxAllocHeap(0)
        , heapUsagePercent(0.0)
        , totalPSRAM(0)
        , freePSRAM(0)
        , fragmentationPercent(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalHeap"] = totalHeap;
        doc["freeHeap"] = freeHeap;
        doc["usedHeap"] = usedHeap;
        doc["minFreeHeap"] = minFreeHeap;
        doc["maxAllocHeap"] = maxAllocHeap;
        doc["heapUsagePercent"] = heapUsagePercent;
        doc["totalPSRAM"] = totalPSRAM;
        doc["freePSRAM"] = freePSRAM;
        doc["fragmentationPercent"] = fragmentationPercent;
        return doc;
    }
};

/**
 * 网络性能指标结构
 */
struct NetworkMetrics {
    uint32_t wifiConnections;           // WiFi连接数
    uint32_t httpRequests;              // HTTP请求数
    uint32_t websocketConnections;      // WebSocket连接数
    uint32_t totalDataSent;             // 总发送数据量
    uint32_t totalDataReceived;         // 总接收数据量
    float averageResponseTime;          // 平均响应时间
    uint32_t networkErrors;             // 网络错误数
    int32_t wifiRSSI;                   // WiFi信号强度
    
    /**
     * 构造函数
     */
    NetworkMetrics() 
        : wifiConnections(0)
        , httpRequests(0)
        , websocketConnections(0)
        , totalDataSent(0)
        , totalDataReceived(0)
        , averageResponseTime(0.0)
        , networkErrors(0)
        , wifiRSSI(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["wifiConnections"] = wifiConnections;
        doc["httpRequests"] = httpRequests;
        doc["websocketConnections"] = websocketConnections;
        doc["totalDataSent"] = totalDataSent;
        doc["totalDataReceived"] = totalDataReceived;
        doc["averageResponseTime"] = averageResponseTime;
        doc["networkErrors"] = networkErrors;
        doc["wifiRSSI"] = wifiRSSI;
        return doc;
    }
};

/**
 * 系统性能指标结构
 */
struct SystemMetrics {
    uint32_t uptime;                    // 系统运行时间
    float chipTemperature;              // 芯片温度
    uint32_t cpuFrequency;              // CPU频率
    uint32_t flashSize;                 // Flash大小
    uint32_t freeFlash;                 // 剩余Flash空间
    String resetReason;                 // 重启原因
    uint32_t bootCount;                 // 启动次数
    
    /**
     * 构造函数
     */
    SystemMetrics() 
        : uptime(0)
        , chipTemperature(0.0)
        , cpuFrequency(0)
        , flashSize(0)
        , freeFlash(0)
        , bootCount(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["uptime"] = uptime;
        doc["chipTemperature"] = chipTemperature;
        doc["cpuFrequency"] = cpuFrequency;
        doc["flashSize"] = flashSize;
        doc["freeFlash"] = freeFlash;
        doc["resetReason"] = resetReason;
        doc["bootCount"] = bootCount;
        return doc;
    }
};

/**
 * 完整性能指标结构 - 匹配前端性能监控显示
 */
struct PerformanceMetrics {
    CPUMetrics cpu;                     // CPU指标
    MemoryMetrics memory;               // 内存指标
    NetworkMetrics network;             // 网络指标
    SystemMetrics system;               // 系统指标
    uint64_t timestamp;                 // 时间戳
    
    /**
     * 构造函数
     */
    PerformanceMetrics() : timestamp(millis()) {}
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["cpu"] = cpu.toJson();
        doc["memory"] = memory.toJson();
        doc["network"] = network.toJson();
        doc["system"] = system.toJson();
        doc["timestamp"] = timestamp;
        return doc;
    }
};

// ================================
// 性能告警定义
// ================================

/**
 * 性能告警级别枚举
 */
enum class AlertLevel : uint8_t {
    INFO = 0,                   // 信息级别
    WARNING = 1,                // 警告级别
    CRITICAL = 2,               // 关键级别
    EMERGENCY = 3               // 紧急级别
};

/**
 * 性能告警结构
 */
struct PerformanceAlert {
    AlertLevel level;                   // 告警级别
    String metric;                      // 指标名称
    float threshold;                    // 阈值
    float currentValue;                 // 当前值
    String message;                     // 告警消息
    uint64_t timestamp;                 // 告警时间
    bool active;                        // 是否活跃
    
    /**
     * 构造函数
     */
    PerformanceAlert() 
        : level(AlertLevel::INFO)
        , threshold(0.0)
        , currentValue(0.0)
        , timestamp(millis())
        , active(false) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["level"] = static_cast<uint8_t>(level);
        doc["metric"] = metric;
        doc["threshold"] = threshold;
        doc["currentValue"] = currentValue;
        doc["message"] = message;
        doc["timestamp"] = timestamp;
        doc["active"] = active;
        return doc;
    }
};

// ================================
// 性能监控器类定义
// ================================

/**
 * 性能监控器类 - 完全匹配前端性能监控需求
 * 
 * 职责：
 * 1. 实时性能数据收集
 * 2. 性能指标计算和分析
 * 3. 性能告警检测和通知
 * 4. 性能历史数据管理
 * 5. 性能优化建议生成
 */
class PerformanceMonitor {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    PerformanceMonitor(EventManager* eventMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~PerformanceMonitor();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化性能监控器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理性能监控器
     */
    void cleanup();
    
    /**
     * 性能监控器主循环
     */
    void loop();
    
    // ================================
    // 性能数据收集 - 匹配前端性能监控
    // ================================
    
    /**
     * 收集当前性能指标 - 匹配前端实时监控
     * @return 当前性能指标
     */
    PerformanceMetrics collectCurrentMetrics();
    
    /**
     * 获取最新性能指标
     * @return 最新性能指标
     */
    PerformanceMetrics getLatestMetrics() const { return latestMetrics; }
    
    /**
     * 获取历史性能数据
     * @param count 数据点数量
     * @return 历史性能数据列表
     */
    std::vector<PerformanceMetrics> getHistoricalMetrics(uint32_t count = 60) const;
    
    /**
     * 获取性能趋势分析
     * @param metric 指标名称
     * @param duration 分析时长（秒）
     * @return 趋势分析JSON对象
     */
    JsonDocument getPerformanceTrend(const String& metric, uint32_t duration = 300) const;
    
    // ================================
    // 性能告警管理 - 匹配前端告警机制
    // ================================
    
    /**
     * 设置性能阈值 - 匹配前端告警配置
     * @param metric 指标名称
     * @param threshold 阈值
     * @param level 告警级别
     * @return 是否设置成功
     */
    bool setPerformanceThreshold(const String& metric, float threshold, AlertLevel level);
    
    /**
     * 获取活跃告警列表
     * @return 活跃告警列表
     */
    std::vector<PerformanceAlert> getActiveAlerts() const;
    
    /**
     * 获取告警历史
     * @param count 历史记录数量
     * @return 告警历史列表
     */
    std::vector<PerformanceAlert> getAlertHistory(uint32_t count = 100) const;
    
    /**
     * 清除告警
     * @param metric 指标名称
     * @return 是否清除成功
     */
    bool clearAlert(const String& metric);
    
    /**
     * 清除所有告警
     * @return 清除的告警数量
     */
    uint32_t clearAllAlerts();
    
    // ================================
    // 监控配置管理
    // ================================
    
    /**
     * 设置监控间隔 - 匹配前端监控频率
     * @param intervalMs 监控间隔（毫秒）
     */
    void setMonitoringInterval(uint32_t intervalMs);
    
    /**
     * 获取监控间隔
     * @return 监控间隔（毫秒）
     */
    uint32_t getMonitoringInterval() const { return monitoringInterval; }
    
    /**
     * 启用性能监控
     * @return 是否启用成功
     */
    bool enableMonitoring();
    
    /**
     * 禁用性能监控
     * @return 是否禁用成功
     */
    bool disableMonitoring();
    
    /**
     * 检查监控是否启用
     * @return 是否启用
     */
    bool isMonitoringEnabled() const { return monitoringEnabled; }
    
    /**
     * 设置历史数据保留数量
     * @param count 保留数量
     */
    void setHistoryRetentionCount(uint32_t count) { historyRetentionCount = count; }
    
    // ================================
    // 性能分析和优化建议
    // ================================
    
    /**
     * 获取性能分析报告 - 匹配前端性能分析
     * @return 性能分析报告JSON对象
     */
    JsonDocument getPerformanceAnalysis() const;
    
    /**
     * 获取优化建议
     * @return 优化建议列表
     */
    std::vector<String> getOptimizationSuggestions() const;
    
    /**
     * 获取系统健康评分
     * @return 健康评分（0-100）
     */
    uint8_t getSystemHealthScore() const;
    
    /**
     * 检查系统是否健康
     * @return 是否健康
     */
    bool isSystemHealthy() const;
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    EventManager* eventManager;         // 事件管理器
    
    // 性能数据
    PerformanceMetrics latestMetrics;   // 最新性能指标
    std::deque<PerformanceMetrics> historicalMetrics; // 历史性能数据
    
    // 告警管理
    std::vector<PerformanceAlert> activeAlerts;        // 活跃告警
    std::deque<PerformanceAlert> alertHistory;         // 告警历史
    std::unordered_map<String, float> thresholds;      // 性能阈值
    std::unordered_map<String, AlertLevel> alertLevels; // 告警级别
    
    // 监控配置
    uint32_t monitoringInterval;        // 监控间隔
    uint32_t lastMonitoringTime;        // 上次监控时间
    bool monitoringEnabled;             // 是否启用监控
    uint32_t historyRetentionCount;     // 历史数据保留数量
    
    // 性能计算
    uint32_t lastCpuTime[2];            // 上次CPU时间
    uint32_t lastIdleTime[2];           // 上次空闲时间
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 收集CPU指标
     * @return CPU指标
     */
    CPUMetrics collectCPUMetrics();
    
    /**
     * 收集内存指标
     * @return 内存指标
     */
    MemoryMetrics collectMemoryMetrics();
    
    /**
     * 收集网络指标
     * @return 网络指标
     */
    NetworkMetrics collectNetworkMetrics();
    
    /**
     * 收集系统指标
     * @return 系统指标
     */
    SystemMetrics collectSystemMetrics();
    
    /**
     * 检查性能告警
     * @param metrics 性能指标
     */
    void checkPerformanceAlerts(const PerformanceMetrics& metrics);
    
    /**
     * 触发告警
     * @param metric 指标名称
     * @param threshold 阈值
     * @param currentValue 当前值
     * @param level 告警级别
     */
    void triggerAlert(const String& metric, float threshold, float currentValue, AlertLevel level);
    
    /**
     * 发布性能事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void publishPerformanceEvent(EventType eventType, const JsonDocument& data = JsonDocument());
    
    /**
     * 清理历史数据
     */
    void cleanupHistoricalData();
    
    /**
     * 计算CPU使用率
     * @param coreId 核心ID
     * @return CPU使用率
     */
    float calculateCpuUsage(uint8_t coreId);
    
    /**
     * 获取任务数量
     * @param coreId 核心ID
     * @return 任务数量
     */
    uint32_t getTaskCount(uint8_t coreId);
    
    /**
     * 获取剩余栈空间
     * @param coreId 核心ID
     * @return 剩余栈空间
     */
    uint32_t getFreeStackSpace(uint8_t coreId);
    
    /**
     * 计算内存碎片率
     * @return 碎片率百分比
     */
    uint32_t calculateFragmentationPercent();
    
    /**
     * 获取芯片温度
     * @return 芯片温度
     */
    float getChipTemperature();
    
    /**
     * 获取重启原因字符串
     * @return 重启原因
     */
    String getResetReasonString();
    
    /**
     * 获取告警级别字符串
     * @param level 告警级别
     * @return 级别字符串
     */
    String getAlertLevelString(AlertLevel level) const;
    
    /**
     * 分析性能趋势
     * @param metricName 指标名称
     * @param values 数值列表
     * @return 趋势分析结果
     */
    JsonDocument analyzeTrend(const String& metricName, const std::vector<float>& values) const;
};

#endif // PERFORMANCE_MONITOR_H
