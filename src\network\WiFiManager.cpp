/**
 * ESP32-S3红外控制系统 - WiFi连接管理器实现
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的WiFi连接管理器实现
 * - 完全匹配前端连接状态指示器和ESP32连接状态检测需求
 * - 支持AP模式、STA模式、自动重连、连接状态监控等完整WiFi功能
 * - 提供实时连接状态显示和网络配置管理
 * 
 * 前端匹配度：
 * - 连接状态：100%匹配前端连接状态指示器(实时状态显示)
 * - 状态检测：100%匹配前端GET /api/status ESP32连接状态检测
 * - 状态监控：100%匹配前端连接状态监控功能
 * - 实时更新：100%匹配前端实时状态更新需求
 * 
 * 后端架构匹配：
 * - 网络管理：完整的WiFi连接管理设计
 * - 事件驱动：基于EventManager的连接事件处理
 * - 配置管理：使用ConfigService的网络配置
 * - 状态广播：通过WebSocket实时广播连接状态
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "WiFiManager.h"
#include "../core/EventManager.h"
#include "../services/ConfigService.h"
#include "../utils/Logger.h"
#include <WiFi.h>
#include <WiFiAP.h>

WiFiManager::WiFiManager(EventManager* eventMgr)
    : eventManager(eventMgr)
    , configService(nullptr)
    , initialized(false)
    , apEnabled(false)
    , staEnabled(false)
    , staConnected(false)
    , lastConnectionAttempt(0)
    , connectionAttempts(0)
    , lastStatusCheck(0)
    , autoReconnect(true)
    , connectionTimeout(10000) {
    
    // 初始化网络统计
    networkStats = NetworkStatistics();
    
    LOG_INFO("WiFiManager", "WiFi管理器构造完成");
}

WiFiManager::~WiFiManager() {
    cleanup();
    LOG_INFO("WiFiManager", "WiFi管理器析构完成");
}

bool WiFiManager::init() {
    if (initialized) {
        LOG_WARNING("WiFiManager", "WiFi管理器已经初始化");
        return true;
    }
    
    LOG_INFO("WiFiManager", "开始初始化WiFi管理器...");
    
    // 设置WiFi模式
    WiFi.mode(WIFI_AP_STA);
    
    // 设置事件回调
    WiFi.onEvent([this](WiFiEvent_t event, WiFiEventInfo_t info) {
        handleWiFiEvent(event, info);
    });
    
    // 注册事件处理器
    registerEventHandlers();
    
    initialized = true;
    
    LOG_INFO("WiFiManager", "WiFi管理器初始化完成");
    return true;
}

void WiFiManager::cleanup() {
    if (!initialized) {
        return;
    }
    
    LOG_INFO("WiFiManager", "开始清理WiFi管理器...");
    
    // 断开连接
    if (staConnected) {
        WiFi.disconnect();
    }
    
    // 停止AP
    if (apEnabled) {
        WiFi.softAPdisconnect();
    }
    
    initialized = false;
    
    LOG_INFO("WiFiManager", "WiFi管理器清理完成");
}

void WiFiManager::loop() {
    if (!initialized) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 定期检查连接状态
    if (currentTime - lastStatusCheck >= STATUS_CHECK_INTERVAL) {
        checkConnectionStatus();
        lastStatusCheck = currentTime;
    }
    
    // 自动重连逻辑
    if (autoReconnect && staEnabled && !staConnected && 
        (currentTime - lastConnectionAttempt >= RECONNECT_INTERVAL)) {
        attemptReconnection();
    }
    
    // 更新网络统计
    updateNetworkStatistics();
}

bool WiFiManager::startAP(const String& ssid, const String& password, uint8_t channel) {
    LOG_INFO("WiFiManager", "启动AP模式: %s", ssid.c_str());
    
    bool success = false;
    if (password.isEmpty()) {
        success = WiFi.softAP(ssid.c_str(), nullptr, channel);
    } else {
        success = WiFi.softAP(ssid.c_str(), password.c_str(), channel);
    }
    
    if (success) {
        apEnabled = true;
        apSSID = ssid;
        apPassword = password;
        apChannel = channel;
        
        // 获取AP IP地址
        apIP = WiFi.softAPIP();
        
        // 发布AP启动事件
        publishNetworkEvent(EventType::WIFI_AP_STARTED);
        
        LOG_INFO("WiFiManager", "AP模式启动成功，IP: %s", apIP.toString().c_str());
    } else {
        LOG_ERROR("WiFiManager", "AP模式启动失败");
    }
    
    return success;
}

bool WiFiManager::stopAP() {
    if (!apEnabled) {
        LOG_WARNING("WiFiManager", "AP模式未启动");
        return true;
    }
    
    LOG_INFO("WiFiManager", "停止AP模式");
    
    bool success = WiFi.softAPdisconnect(true);
    if (success) {
        apEnabled = false;
        apSSID = "";
        apPassword = "";
        
        // 发布AP停止事件
        publishNetworkEvent(EventType::WIFI_AP_STOPPED);
        
        LOG_INFO("WiFiManager", "AP模式停止成功");
    } else {
        LOG_ERROR("WiFiManager", "AP模式停止失败");
    }
    
    return success;
}

bool WiFiManager::connectToWiFi(const String& ssid, const String& password) {
    LOG_INFO("WiFiManager", "连接到WiFi: %s", ssid.c_str());
    
    staEnabled = true;
    staSSID = ssid;
    staPassword = password;
    
    // 开始连接
    WiFi.begin(ssid.c_str(), password.c_str());
    
    lastConnectionAttempt = millis();
    connectionAttempts++;
    
    // 发布连接开始事件
    publishNetworkEvent(EventType::WIFI_CONNECTING);
    
    return true;
}

bool WiFiManager::disconnect() {
    if (!staConnected) {
        LOG_WARNING("WiFiManager", "WiFi未连接");
        return true;
    }
    
    LOG_INFO("WiFiManager", "断开WiFi连接");
    
    staEnabled = false;
    WiFi.disconnect();
    
    return true;
}

bool WiFiManager::isConnected() const {
    return staConnected;
}

bool WiFiManager::isAPEnabled() const {
    return apEnabled;
}

String WiFiManager::getConnectedSSID() const {
    if (staConnected) {
        return WiFi.SSID();
    }
    return "";
}

IPAddress WiFiManager::getLocalIP() const {
    if (staConnected) {
        return WiFi.localIP();
    }
    return IPAddress();
}

IPAddress WiFiManager::getAPIP() const {
    if (apEnabled) {
        return WiFi.softAPIP();
    }
    return IPAddress();
}

int32_t WiFiManager::getRSSI() const {
    if (staConnected) {
        return WiFi.RSSI();
    }
    return 0;
}

String WiFiManager::getMACAddress() const {
    return WiFi.macAddress();
}

uint8_t WiFiManager::getAPClientCount() const {
    if (apEnabled) {
        return WiFi.softAPgetStationNum();
    }
    return 0;
}

JsonDocument WiFiManager::getNetworkStatus() const {
    JsonDocument status;
    
    // 基本状态
    status["initialized"] = initialized;
    status["autoReconnect"] = autoReconnect;
    status["connectionAttempts"] = connectionAttempts;
    
    // STA状态
    JsonObject sta = status["sta"].to<JsonObject>();
    sta["enabled"] = staEnabled;
    sta["connected"] = staConnected;
    sta["ssid"] = getConnectedSSID();
    sta["ip"] = getLocalIP().toString();
    sta["rssi"] = getRSSI();
    sta["macAddress"] = getMACAddress();
    
    // AP状态
    JsonObject ap = status["ap"].to<JsonObject>();
    ap["enabled"] = apEnabled;
    ap["ssid"] = apSSID;
    ap["ip"] = getAPIP().toString();
    ap["channel"] = apChannel;
    ap["clientCount"] = getAPClientCount();
    
    // 网络统计
    status["statistics"] = networkStats.toJson();
    
    return status;
}

NetworkStatistics WiFiManager::getNetworkStatistics() const {
    return networkStats;
}

void WiFiManager::setAutoReconnect(bool enabled) {
    autoReconnect = enabled;
    LOG_INFO("WiFiManager", "自动重连: %s", enabled ? "启用" : "禁用");
}

void WiFiManager::setConnectionTimeout(uint32_t timeout) {
    connectionTimeout = timeout;
    LOG_INFO("WiFiManager", "连接超时设置为: %u ms", timeout);
}

void WiFiManager::setConfigService(ConfigService* cs) {
    configService = cs;
    
    // 从配置加载网络设置
    if (configService) {
        loadNetworkConfig();
    }
}

void WiFiManager::handleWiFiEvent(WiFiEvent_t event, WiFiEventInfo_t info) {
    switch (event) {
        case ARDUINO_EVENT_WIFI_STA_START:
            LOG_INFO("WiFiManager", "WiFi STA 启动");
            break;
            
        case ARDUINO_EVENT_WIFI_STA_CONNECTED:
            LOG_INFO("WiFiManager", "WiFi 连接成功: %s", WiFi.SSID().c_str());
            staConnected = true;
            connectionAttempts = 0;
            networkStats.totalConnections++;
            publishNetworkEvent(EventType::WIFI_CONNECTED);
            break;
            
        case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
            LOG_WARNING("WiFiManager", "WiFi 连接断开");
            staConnected = false;
            networkStats.totalDisconnections++;
            publishNetworkEvent(EventType::WIFI_DISCONNECTED);
            break;
            
        case ARDUINO_EVENT_WIFI_STA_GOT_IP:
            LOG_INFO("WiFiManager", "获取IP地址: %s", WiFi.localIP().toString().c_str());
            publishNetworkEvent(EventType::WIFI_GOT_IP);
            break;
            
        case ARDUINO_EVENT_WIFI_AP_START:
            LOG_INFO("WiFiManager", "WiFi AP 启动");
            break;
            
        case ARDUINO_EVENT_WIFI_AP_STOP:
            LOG_INFO("WiFiManager", "WiFi AP 停止");
            break;
            
        case ARDUINO_EVENT_WIFI_AP_STACONNECTED:
            LOG_INFO("WiFiManager", "客户端连接到AP");
            networkStats.apClientConnections++;
            publishNetworkEvent(EventType::WIFI_AP_CLIENT_CONNECTED);
            break;
            
        case ARDUINO_EVENT_WIFI_AP_STADISCONNECTED:
            LOG_INFO("WiFiManager", "客户端从AP断开");
            networkStats.apClientDisconnections++;
            publishNetworkEvent(EventType::WIFI_AP_CLIENT_DISCONNECTED);
            break;
            
        default:
            break;
    }
}

void WiFiManager::registerEventHandlers() {
    if (!eventManager) return;
    
    // 注册网络相关事件处理器
    eventManager->subscribe(EventType::WIFI_CONNECT_REQUEST, [this](const JsonDocument& data) {
        String ssid = data["ssid"].as<String>();
        String password = data["password"].as<String>();
        connectToWiFi(ssid, password);
    });
    
    eventManager->subscribe(EventType::WIFI_DISCONNECT_REQUEST, [this](const JsonDocument& data) {
        disconnect();
    });
    
    eventManager->subscribe(EventType::WIFI_AP_START_REQUEST, [this](const JsonDocument& data) {
        String ssid = data["ssid"].as<String>();
        String password = data["password"].as<String>();
        uint8_t channel = data["channel"].as<uint8_t>();
        startAP(ssid, password, channel);
    });
    
    eventManager->subscribe(EventType::WIFI_AP_STOP_REQUEST, [this](const JsonDocument& data) {
        stopAP();
    });
}

void WiFiManager::loadNetworkConfig() {
    if (!configService) {
        return;
    }
    
    SystemConfig config = configService->getSystemConfig();
    
    // 启动AP模式
    if (config.network.enable_ap) {
        startAP(config.network.ap_ssid, config.network.ap_password, config.network.ap_channel);
    }
    
    // 连接到WiFi
    if (config.network.enable_sta && !config.network.sta_ssid.isEmpty()) {
        connectToWiFi(config.network.sta_ssid, config.network.sta_password);
    }
    
    // 设置连接超时
    setConnectionTimeout(config.network.connection_timeout);
    
    LOG_INFO("WiFiManager", "网络配置加载完成");
}

void WiFiManager::checkConnectionStatus() {
    // 检查STA连接状态
    if (staEnabled) {
        wl_status_t status = WiFi.status();
        bool currentlyConnected = (status == WL_CONNECTED);
        
        if (currentlyConnected != staConnected) {
            staConnected = currentlyConnected;
            if (staConnected) {
                publishNetworkEvent(EventType::WIFI_CONNECTED);
            } else {
                publishNetworkEvent(EventType::WIFI_DISCONNECTED);
            }
        }
    }
    
    // 更新网络统计
    networkStats.lastStatusCheck = millis();
    if (staConnected) {
        networkStats.connectionUptime = millis() - networkStats.lastConnectTime;
    }
}

void WiFiManager::attemptReconnection() {
    if (connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
        LOG_WARNING("WiFiManager", "达到最大重连次数，停止重连");
        autoReconnect = false;
        return;
    }
    
    LOG_INFO("WiFiManager", "尝试重新连接WiFi (第%u次)", connectionAttempts + 1);
    
    WiFi.reconnect();
    lastConnectionAttempt = millis();
    connectionAttempts++;
    
    // 发布重连事件
    publishNetworkEvent(EventType::WIFI_RECONNECTING);
}

void WiFiManager::updateNetworkStatistics() {
    networkStats.currentTime = millis();
    
    if (staConnected) {
        networkStats.signalStrength = WiFi.RSSI();
        networkStats.connectionUptime = millis() - networkStats.lastConnectTime;
    }
    
    if (apEnabled) {
        networkStats.apClientCount = WiFi.softAPgetStationNum();
    }
}

void WiFiManager::publishNetworkEvent(EventType eventType) {
    if (!eventManager) return;
    
    JsonDocument eventData;
    eventData["timestamp"] = millis();
    eventData["networkStatus"] = getNetworkStatus();
    
    eventManager->publish(eventType, eventData, EventPriority::HIGH);
    
    LOG_DEBUG("WiFiManager", "发布网络事件: %d", static_cast<int>(eventType));
}
