/**
 * ESP32-S3红外控制系统 - 信号数据结构定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档的SignalData结构100%匹配实现
 * - 基于后端架构设计的双核并行优化数据结构
 * - 完整的12个字段定义，确保前后端数据格式完全一致
 * - 支持JSON序列化/反序列化，匹配前端API响应格式
 * 
 * 前端匹配度：
 * - 数据字段：12个字段100%匹配前端SignalData定义
 * - 数据类型：完全匹配前端验证器的类型检查
 * - ID格式：signal_12345678格式，匹配前端ID生成规则
 * - 时间戳：13位毫秒时间戳，匹配前端时间处理
 * 
 * 后端架构匹配：
 * - 二级存储：支持L1缓存和L2存储的数据结构优化
 * - 内存优化：无PSRAM版本的内存使用优化
 * - 性能优化：支持批量处理和高速访问
 * - 数据验证：完整的数据完整性检查
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SIGNAL_DATA_H
#define SIGNAL_DATA_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <memory>

// ================================
// 信号数据结构定义
// ================================

/**
 * 信号数据结构 - 100%匹配前端SignalData格式
 * 
 * 基于前端完整数据文档分析，前端SignalData包含12个核心字段：
 * 1. id: signal_12345678格式的唯一标识符
 * 2. name: 信号名称，用户可编辑
 * 3. type: 信号类型 (tv/ac/fan/light/other)
 * 4. description: 信号描述，用户可编辑
 * 5. signalCode: 信号代码，十六进制格式
 * 6. protocol: 协议类型 (NEC/RC5/SONY/RAW)
 * 7. frequency: 载波频率，字符串格式存储
 * 8. data: 红外数据，十六进制字符串
 * 9. isLearned: 是否已学习标志
 * 10. created: 创建时间，13位毫秒时间戳
 * 11. lastSent: 最后发送时间，13位毫秒时间戳
 * 12. sentCount: 发送次数统计
 */
struct SignalData {
    // ================================
    // 核心字段 - 完全匹配前端12个字段
    // ================================
    
    String id;              // 信号ID - signal_12345678格式
    String name;            // 信号名称 - 用户可编辑，1-64字符
    String type;            // 信号类型 - tv/ac/fan/light/other
    String description;     // 信号描述 - 用户可编辑，最大128字符
    String signalCode;      // 信号代码 - 0x开头的十六进制格式
    String protocol;        // 协议类型 - NEC/RC5/SONY/RAW等
    String frequency;       // 载波频率 - 字符串格式，匹配前端处理
    String data;            // 红外数据 - 十六进制字符串格式
    bool isLearned;         // 是否已学习 - 区分学习信号和导入信号
    uint64_t created;       // 创建时间 - 13位毫秒时间戳
    uint64_t lastSent;      // 最后发送时间 - 13位毫秒时间戳
    uint32_t sentCount;     // 发送次数 - 统计使用频率
    
    // ================================
    // 内部处理字段 - 后端优化使用
    // ================================
    
    std::vector<uint16_t> rawData;  // 原始时序数据 - 内部处理使用
    uint8_t bits;                   // 数据位数 - 协议解析使用
    uint8_t quality;                // 信号质量 - 学习时评估，0-100
    
    // ================================
    // 构造函数
    // ================================
    
    /**
     * 默认构造函数
     */
    SignalData() 
        : isLearned(false)
        , created(0)
        , lastSent(0)
        , sentCount(0)
        , bits(0)
        , quality(0) {
    }
    
    /**
     * 完整构造函数
     */
    SignalData(const String& id, const String& name, const String& type,
               const String& description, const String& signalCode, 
               const String& protocol, const String& frequency, const String& data)
        : id(id)
        , name(name)
        , type(type)
        , description(description)
        , signalCode(signalCode)
        , protocol(protocol)
        , frequency(frequency)
        , data(data)
        , isLearned(true)
        , created(millis())
        , lastSent(0)
        , sentCount(0)
        , bits(0)
        , quality(100) {
    }
    
    // ================================
    // 辅助方法
    // ================================
    
    /**
     * 检查信号数据是否有效
     * @return 是否有效
     */
    bool isValid() const {
        return !id.isEmpty() && 
               !name.isEmpty() && 
               !type.isEmpty() && 
               !data.isEmpty() &&
               created > 0;
    }
    
    /**
     * 获取频率的整数值
     * @return 频率整数值
     */
    uint16_t getFrequencyAsInt() const {
        return frequency.toInt();
    }
    
    /**
     * 从整数设置频率
     * @param freq 频率值
     */
    void setFrequencyFromInt(uint16_t freq) {
        frequency = String(freq);
    }
    
    /**
     * 更新最后发送时间和计数
     */
    void updateSentStats() {
        lastSent = millis();
        sentCount++;
    }
    
    /**
     * 检查是否为有效的信号类型
     * @return 是否为有效类型
     */
    bool isValidType() const {
        return type == "tv" || type == "ac" || type == "fan" || 
               type == "light" || type == "other";
    }
    
    /**
     * 检查是否为有效的协议类型
     * @return 是否为有效协议
     */
    bool isValidProtocol() const {
        return protocol == "NEC" || protocol == "RC5" || protocol == "RC6" ||
               protocol == "SONY" || protocol == "SAMSUNG" || protocol == "LG" ||
               protocol == "PANASONIC" || protocol == "RAW";
    }
    
    /**
     * 获取信号数据大小（字节）
     * @return 数据大小
     */
    size_t getDataSize() const {
        return data.length() / 2; // 十六进制字符串，每2个字符代表1字节
    }
    
    /**
     * 检查是否为热点信号（高频使用）
     * @return 是否为热点信号
     */
    bool isHotSignal() const {
        return sentCount > 5 && (millis() - lastSent) < 86400000; // 24小时内使用过且使用次数>5
    }
    
    // ================================
    // JSON转换方法 - 严格匹配前端格式
    // ================================
    
    /**
     * 转换为JSON对象 - 完全匹配前端API响应格式
     * @return JSON文档对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        // 核心字段 - 按前端期望的顺序和格式
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["description"] = description;
        doc["signalCode"] = signalCode;
        doc["protocol"] = protocol;
        doc["frequency"] = frequency.toInt(); // 前端期望数字格式
        doc["data"] = data;
        doc["isLearned"] = isLearned;
        doc["created"] = created;
        doc["lastSent"] = lastSent;
        doc["sentCount"] = sentCount;
        
        return doc;
    }
    
    /**
     * 从JSON对象创建信号数据 - 匹配前端数据验证器格式
     * @param json JSON对象
     * @return 信号数据对象
     */
    static SignalData fromJson(const JsonObject& json) {
        SignalData signal;
        
        // 核心字段解析 - 严格按照前端数据验证器的字段要求
        signal.id = json["id"].as<String>();
        signal.name = json["name"].as<String>();
        signal.type = json["type"].as<String>();
        signal.description = json["description"].as<String>();
        signal.signalCode = json["signalCode"].as<String>();
        signal.protocol = json["protocol"].as<String>();
        
        // 频率处理 - 兼容前端的数字和字符串格式
        if (json["frequency"].is<int>()) {
            signal.frequency = String(json["frequency"].as<int>());
        } else {
            signal.frequency = json["frequency"].as<String>();
        }
        
        signal.data = json["data"].as<String>();
        signal.isLearned = json["isLearned"].as<bool>();
        signal.created = json["created"].as<uint64_t>();
        signal.lastSent = json["lastSent"].as<uint64_t>();
        signal.sentCount = json["sentCount"].as<uint32_t>();
        
        return signal;
    }
    
    /**
     * 转换为JSON字符串
     * @return JSON字符串
     */
    String toJsonString() const {
        JsonDocument doc = toJson();
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    /**
     * 从JSON字符串创建信号数据
     * @param jsonString JSON字符串
     * @return 信号数据对象
     */
    static SignalData fromJsonString(const String& jsonString) {
        JsonDocument doc;
        deserializeJson(doc, jsonString);
        return fromJson(doc.as<JsonObject>());
    }
    
    // ================================
    // 比较操作符
    // ================================
    
    /**
     * 相等比较操作符
     */
    bool operator==(const SignalData& other) const {
        return id == other.id;
    }
    
    /**
     * 不等比较操作符
     */
    bool operator!=(const SignalData& other) const {
        return !(*this == other);
    }
    
    /**
     * 小于比较操作符 - 用于排序
     */
    bool operator<(const SignalData& other) const {
        return name < other.name;
    }
};

// ================================
// 信号数据工具函数
// ================================

/**
 * 生成信号ID - 匹配前端ID生成规则
 * @return 格式为signal_12345678的ID
 */
inline String generateSignalId() {
    uint32_t timestamp = millis();
    return "signal_" + String(timestamp % 100000000); // 8位数字
}

/**
 * 验证信号ID格式 - 匹配前端数据验证器
 * @param id 信号ID
 * @return 是否为有效格式
 */
inline bool isValidSignalId(const String& id) {
    if (id.length() != 16) return false; // signal_ + 8位数字 = 16字符
    if (!id.startsWith("signal_")) return false;
    
    String numberPart = id.substring(7);
    for (char c : numberPart) {
        if (!isDigit(c)) return false;
    }
    
    return true;
}

/**
 * 验证信号代码格式 - 匹配前端验证规则
 * @param code 信号代码
 * @return 是否为有效格式
 */
inline bool isValidSignalCode(const String& code) {
    if (code.length() < 3) return false;
    if (!code.startsWith("0x") && !code.startsWith("0X")) return false;
    
    String hexPart = code.substring(2);
    for (char c : hexPart) {
        if (!isHexadecimalDigit(c)) return false;
    }
    
    return true;
}

/**
 * 创建标准测试信号 - 匹配前端测试数据格式
 * @param name 信号名称
 * @param type 信号类型
 * @param code 信号代码
 * @return 标准格式的信号数据
 */
inline SignalData createStandardSignal(const String& name, const String& type, const String& code) {
    SignalData signal;
    signal.id = generateSignalId();
    signal.name = name;
    signal.type = type;
    signal.description = name + "的红外控制信号";
    signal.signalCode = code;
    signal.protocol = "NEC";
    signal.frequency = "38000";
    signal.data = code;
    signal.isLearned = true;
    signal.created = millis();
    signal.lastSent = 0;
    signal.sentCount = 0;
    signal.quality = 100;
    
    return signal;
}

#endif // SIGNAL_DATA_H
