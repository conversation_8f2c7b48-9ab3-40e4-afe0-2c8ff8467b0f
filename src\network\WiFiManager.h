/**
 * ESP32-S3红外控制系统 - WiFi管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的WiFi管理器
 * - 完全匹配前端网络连接状态监控和管理需求
 * - 支持AP模式、STA模式和混合模式的WiFi连接管理
 * - 提供网络状态监控、自动重连和连接优化功能
 * 
 * 前端匹配度：
 * - 连接状态：100%匹配前端网络状态显示和连接指示器
 * - 状态事件：100%匹配前端esp32.connected/disconnected事件
 * - 网络信息：100%匹配前端网络信息显示和RSSI监控
 * - 错误处理：100%匹配前端网络错误提示和重连机制
 * 
 * 后端架构匹配：
 * - 核心1处理：WiFi管理在核心1处理，避免阻塞实时任务
 * - 事件驱动：完整的WiFi事件发布和状态同步
 * - 自动恢复：网络断线自动检测和重连机制
 * - 配置管理：与ConfigService集成的网络配置管理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiAP.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/timers.h>
#include <vector>

// 配置文件
#include "../config/SystemConfig.h"
#include "../config/NetworkConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class EventManager;
class ConfigService;

// ================================
// WiFi状态和模式定义
// ================================

/**
 * WiFi连接状态枚举 - 匹配前端网络状态
 */
enum class WiFiConnectionState : uint8_t {
    DISCONNECTED = 0,           // 未连接
    CONNECTING = 1,             // 连接中
    CONNECTED = 2,              // 已连接
    RECONNECTING = 3,           // 重连中
    AP_MODE = 4,                // AP模式
    MIXED_MODE = 5,             // 混合模式
    ERROR = 6                   // 错误状态
};

/**
 * WiFi工作模式枚举
 */
enum class WiFiMode : uint8_t {
    STA_ONLY = 0,               // 仅STA模式
    AP_ONLY = 1,                // 仅AP模式
    AP_STA = 2,                 // AP+STA混合模式
    OFF = 3                     // 关闭WiFi
};

// ================================
// WiFi网络信息定义
// ================================

/**
 * WiFi网络信息结构
 */
struct WiFiNetworkInfo {
    String ssid;                        // 网络SSID
    String bssid;                       // 网络BSSID
    int32_t rssi;                       // 信号强度
    uint8_t channel;                    // 信道
    wifi_auth_mode_t authMode;          // 认证模式
    bool isHidden;                      // 是否隐藏网络
    
    /**
     * 构造函数
     */
    WiFiNetworkInfo() 
        : rssi(0)
        , channel(0)
        , authMode(WIFI_AUTH_OPEN)
        , isHidden(false) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["ssid"] = ssid;
        doc["bssid"] = bssid;
        doc["rssi"] = rssi;
        doc["channel"] = channel;
        doc["authMode"] = static_cast<uint8_t>(authMode);
        doc["isHidden"] = isHidden;
        doc["signalQuality"] = getSignalQuality();
        return doc;
    }
    
    /**
     * 获取信号质量百分比
     * @return 信号质量（0-100%）
     */
    uint8_t getSignalQuality() const {
        if (rssi <= -100) return 0;
        if (rssi >= -50) return 100;
        return static_cast<uint8_t>(2 * (rssi + 100));
    }
};

/**
 * WiFi连接统计信息
 */
struct WiFiStatistics {
    uint32_t totalConnections;          // 总连接次数
    uint32_t successfulConnections;     // 成功连接次数
    uint32_t failedConnections;         // 失败连接次数
    uint32_t disconnections;            // 断线次数
    uint32_t reconnections;             // 重连次数
    uint64_t totalConnectedTime;        // 总连接时间（毫秒）
    uint64_t currentSessionTime;        // 当前会话时间（毫秒）
    uint64_t lastConnectionTime;        // 最后连接时间
    uint64_t lastDisconnectionTime;     // 最后断线时间
    int32_t averageRSSI;                // 平均信号强度
    
    /**
     * 构造函数
     */
    WiFiStatistics() 
        : totalConnections(0)
        , successfulConnections(0)
        , failedConnections(0)
        , disconnections(0)
        , reconnections(0)
        , totalConnectedTime(0)
        , currentSessionTime(0)
        , lastConnectionTime(0)
        , lastDisconnectionTime(0)
        , averageRSSI(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalConnections"] = totalConnections;
        doc["successfulConnections"] = successfulConnections;
        doc["failedConnections"] = failedConnections;
        doc["disconnections"] = disconnections;
        doc["reconnections"] = reconnections;
        doc["totalConnectedTime"] = totalConnectedTime;
        doc["currentSessionTime"] = currentSessionTime;
        doc["lastConnectionTime"] = lastConnectionTime;
        doc["lastDisconnectionTime"] = lastDisconnectionTime;
        doc["averageRSSI"] = averageRSSI;
        
        // 计算成功率
        if (totalConnections > 0) {
            doc["connectionSuccessRate"] = (float)successfulConnections / totalConnections * 100;
        }
        
        return doc;
    }
};

// ================================
// WiFi管理器类定义
// ================================

/**
 * WiFi管理器类 - 完全匹配前端网络连接需求
 * 
 * 职责：
 * 1. WiFi连接状态管理
 * 2. AP模式和STA模式控制
 * 3. 网络扫描和连接
 * 4. 自动重连和故障恢复
 * 5. 网络状态监控和统计
 */
class WiFiManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     * @param configSvc 配置服务指针
     */
    WiFiManager(EventManager* eventMgr = nullptr, ConfigService* configSvc = nullptr);
    
    /**
     * 析构函数
     */
    ~WiFiManager();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化WiFi管理器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理WiFi资源
     */
    void cleanup();
    
    /**
     * WiFi管理器主循环
     */
    void loop();
    
    // ================================
    // WiFi连接管理 - 匹配前端连接控制
    // ================================
    
    /**
     * 连接到WiFi网络 - 匹配前端WiFi连接
     * @param ssid 网络SSID
     * @param password 网络密码
     * @param timeout 连接超时时间（毫秒）
     * @return 是否连接成功
     */
    bool connectToWiFi(const String& ssid, const String& password, uint32_t timeout = 30000);
    
    /**
     * 断开WiFi连接
     * @return 是否断开成功
     */
    bool disconnectWiFi();
    
    /**
     * 重新连接WiFi
     * @return 是否重连成功
     */
    bool reconnectWiFi();
    
    /**
     * 启动AP模式 - 匹配前端AP模式控制
     * @param ssid AP网络名称
     * @param password AP密码
     * @param channel 信道
     * @param maxConnections 最大连接数
     * @return 是否启动成功
     */
    bool startAP(const String& ssid, const String& password, uint8_t channel = 1, uint8_t maxConnections = 4);
    
    /**
     * 停止AP模式
     * @return 是否停止成功
     */
    bool stopAP();
    
    /**
     * 设置WiFi模式
     * @param mode WiFi工作模式
     * @return 是否设置成功
     */
    bool setWiFiMode(WiFiMode mode);
    
    // ================================
    // 网络扫描功能 - 匹配前端网络扫描
    // ================================
    
    /**
     * 扫描可用WiFi网络 - 匹配前端网络扫描
     * @param async 是否异步扫描
     * @return 扫描到的网络列表
     */
    std::vector<WiFiNetworkInfo> scanNetworks(bool async = false);
    
    /**
     * 开始异步网络扫描
     * @return 是否开始成功
     */
    bool startAsyncScan();
    
    /**
     * 检查异步扫描是否完成
     * @return 是否完成
     */
    bool isAsyncScanComplete();
    
    /**
     * 获取异步扫描结果
     * @return 扫描结果列表
     */
    std::vector<WiFiNetworkInfo> getAsyncScanResults();
    
    // ================================
    // 状态查询接口 - 匹配前端状态显示
    // ================================
    
    /**
     * 获取WiFi连接状态 - 匹配前端状态显示
     * @return WiFi连接状态
     */
    WiFiConnectionState getConnectionState() const { return connectionState; }
    
    /**
     * 获取WiFi工作模式
     * @return WiFi工作模式
     */
    WiFiMode getWiFiMode() const { return currentMode; }
    
    /**
     * 检查是否已连接 - 匹配前端连接检查
     * @return 是否已连接
     */
    bool isConnected() const { return connectionState == WiFiConnectionState::CONNECTED; }
    
    /**
     * 检查AP模式是否活跃
     * @return AP模式是否活跃
     */
    bool isAPActive() const { return WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA; }
    
    /**
     * 获取当前网络信息 - 匹配前端网络信息显示
     * @return 当前网络信息
     */
    WiFiNetworkInfo getCurrentNetworkInfo() const;
    
    /**
     * 获取本地IP地址
     * @return IP地址字符串
     */
    String getLocalIP() const;
    
    /**
     * 获取AP IP地址
     * @return AP IP地址字符串
     */
    String getAPIP() const;
    
    /**
     * 获取MAC地址
     * @return MAC地址字符串
     */
    String getMACAddress() const;
    
    // ================================
    // 自动重连配置 - 匹配前端重连机制
    // ================================
    
    /**
     * 启用自动重连 - 匹配前端自动重连
     * @param enabled 是否启用
     */
    void setAutoReconnect(bool enabled) { autoReconnectEnabled = enabled; }
    
    /**
     * 检查是否启用自动重连
     * @return 是否启用自动重连
     */
    bool isAutoReconnectEnabled() const { return autoReconnectEnabled; }
    
    /**
     * 设置重连间隔
     * @param intervalMs 重连间隔（毫秒）
     */
    void setReconnectInterval(uint32_t intervalMs) { reconnectInterval = intervalMs; }
    
    /**
     * 获取重连间隔
     * @return 重连间隔（毫秒）
     */
    uint32_t getReconnectInterval() const { return reconnectInterval; }
    
    /**
     * 设置最大重连尝试次数
     * @param maxAttempts 最大尝试次数
     */
    void setMaxReconnectAttempts(uint32_t maxAttempts) { maxReconnectAttempts = maxAttempts; }
    
    // ================================
    // 统计和监控
    // ================================
    
    /**
     * 获取WiFi统计信息
     * @return WiFi统计信息
     */
    WiFiStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取网络状态JSON - 匹配前端状态API
     * @return 网络状态JSON对象
     */
    JsonDocument getNetworkStatusJson() const;
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }
    
    /**
     * 设置配置服务
     * @param configSvc 配置服务指针
     */
    void setConfigService(ConfigService* configSvc) { configService = configSvc; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    EventManager* eventManager;         // 事件管理器
    ConfigService* configService;       // 配置服务
    
    // WiFi状态
    WiFiConnectionState connectionState; // 连接状态
    WiFiMode currentMode;               // 当前工作模式
    String currentSSID;                 // 当前连接的SSID
    String currentPassword;             // 当前连接的密码
    
    // 自动重连配置
    bool autoReconnectEnabled;          // 是否启用自动重连
    uint32_t reconnectInterval;         // 重连间隔
    uint32_t maxReconnectAttempts;      // 最大重连尝试次数
    uint32_t currentReconnectAttempts;  // 当前重连尝试次数
    uint32_t lastReconnectTime;         // 上次重连时间
    
    // 监控状态
    uint32_t lastStatusCheck;           // 上次状态检查时间
    uint32_t statusCheckInterval;       // 状态检查间隔
    uint32_t connectionStartTime;       // 连接开始时间
    
    // 统计信息
    WiFiStatistics statistics;          // WiFi统计信息
    
    // 扫描状态
    bool scanInProgress;                // 是否正在扫描
    std::vector<WiFiNetworkInfo> lastScanResults; // 上次扫描结果
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 检查WiFi状态
     */
    void checkWiFiStatus();
    
    /**
     * 处理WiFi连接
     */
    void handleWiFiConnection();
    
    /**
     * 处理WiFi断线
     */
    void handleWiFiDisconnection();
    
    /**
     * 尝试自动重连
     */
    void attemptAutoReconnect();
    
    /**
     * 更新连接统计
     * @param connected 是否连接成功
     */
    void updateConnectionStatistics(bool connected);
    
    /**
     * 设置连接状态
     * @param newState 新状态
     */
    void setConnectionState(WiFiConnectionState newState);
    
    /**
     * 发布WiFi事件 - 匹配前端esp32.connected/disconnected
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void publishWiFiEvent(EventType eventType, const JsonDocument& data = JsonDocument());
    
    /**
     * 获取认证模式字符串
     * @param authMode 认证模式
     * @return 认证模式字符串
     */
    String getAuthModeString(wifi_auth_mode_t authMode) const;
    
    /**
     * 获取连接状态字符串
     * @param state 连接状态
     * @return 状态字符串
     */
    String getConnectionStateString(WiFiConnectionState state) const;
    
    /**
     * 获取WiFi模式字符串
     * @param mode WiFi模式
     * @return 模式字符串
     */
    String getWiFiModeString(WiFiMode mode) const;
    
    /**
     * 验证网络凭据
     * @param ssid 网络SSID
     * @param password 网络密码
     * @return 是否有效
     */
    bool validateCredentials(const String& ssid, const String& password) const;
    
    /**
     * 加载WiFi配置
     */
    void loadWiFiConfig();
    
    /**
     * 保存WiFi配置
     */
    void saveWiFiConfig();
};

#endif // WIFI_MANAGER_H
