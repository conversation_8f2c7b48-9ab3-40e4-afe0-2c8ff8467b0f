/**
 * @file EventManager.cpp
 * @brief 事件管理器实现 - 完全匹配头文件定义
 *
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "EventManager.h"
#include "../utils/Logger.h"

EventManager::EventManager()
    : initialized(false) {
    LOG_INFO("EventManager", "事件管理器构造完成");
}

EventManager::~EventManager() {
    cleanup();
    LOG_INFO("EventManager", "事件管理器析构完成");
}

bool EventManager::init() {
    if (initialized) {
        return true;
    }

    // 初始化事件缓冲区
    // EventRingBuffer会自动初始化，无需手动操作

    initialized = true;
    LOG_INFO("EventManager", "事件管理器初始化完成");
    return true;
}

void EventManager::cleanup() {
    if (!initialized) {
        return;
    }

    highPriorityBuffer.clear();
    normalBuffer.clear();
    listeners.clear();

    initialized = false;
    LOG_INFO("EventManager", "事件管理器清理完成");
}

bool EventManager::emit(EventType type, const JsonDocument& data, const String& source) {
    if (!initialized) {
        return false;
    }

    EventData event;
    event.type = type;
    event.data = data;
    event.source = source;
    event.timestamp = millis();
    event.priority = EventPriority::PRIORITY_NORMAL;

    return emitEvent(event);
}

bool EventManager::emitHighPriority(EventType type, const JsonDocument& data, const String& source) {
    if (!initialized) {
        return false;
    }

    EventData event;
    event.type = type;
    event.data = data;
    event.source = source;
    event.timestamp = millis();
    event.priority = EventPriority::PRIORITY_URGENT;

    return emitEvent(event);
}

bool EventManager::emitEvent(const EventData& event) {
    if (!initialized) {
        return false;
    }

    // 根据优先级添加到相应缓冲区
    if (event.priority == EventPriority::PRIORITY_URGENT) {
        if (!highPriorityBuffer.isFull()) {
            highPriorityBuffer.push(event);
            return true;
        }
    } else {
        if (!normalBuffer.isFull()) {
            normalBuffer.push(event);
            return true;
        }
    }

    return false;
}

bool EventManager::on(EventType type, EventHandler handler, const String& listenerId, uint32_t priority) {
    if (!initialized || !handler) {
        return false;
    }

    EventListener listener;
    listener.handler = handler;
    listener.listenerId = listenerId;
    listener.priority = priority;
    listener.once = false;

    listeners[type].push_back(listener);
    return true;
}

bool EventManager::once(EventType type, EventHandler handler, const String& listenerId) {
    if (!initialized || !handler) {
        return false;
    }

    EventListener listener;
    listener.handler = handler;
    listener.listenerId = listenerId;
    listener.priority = 0;
    listener.once = true;

    listeners[type].push_back(listener);
    return true;
}

bool EventManager::off(EventType type, const String& listenerId) {
    if (!initialized) {
        return false;
    }

    auto typeIt = listeners.find(type);
    if (typeIt != listeners.end()) {
        auto& listenerList = typeIt->second;
        auto it = listenerList.begin();
        while (it != listenerList.end()) {
            if (listenerId.isEmpty() || it->listenerId == listenerId) {
                it = listenerList.erase(it);
            } else {
                ++it;
            }
        }
    }

    return true;
}

size_t EventManager::offAll(const String& listenerId) {
    if (!initialized) {
        return 0;
    }

    size_t count = 0;
    for (auto& pair : listeners) {
        auto& listenerList = pair.second;
        auto it = listenerList.begin();
        while (it != listenerList.end()) {
            if (listenerId.isEmpty() || it->listenerId == listenerId) {
                it = listenerList.erase(it);
                count++;
            } else {
                ++it;
            }
        }
    }

    return count;
}