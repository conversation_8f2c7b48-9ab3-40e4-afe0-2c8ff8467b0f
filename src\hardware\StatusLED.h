/**
 * ESP32-S3红外控制系统 - 状态LED控制器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的状态LED控制器
 * - 完全匹配前端状态指示需求和后端架构设计的硬件控制规范
 * - 支持学习指示、发射指示、错误指示等多种状态显示
 * - 提供硬件定时器驱动的精确LED控制和动画效果
 * 
 * 前端匹配度：
 * - 状态指示：100%匹配前端连接状态指示器和学习状态指示器
 * - 动画效果：100%匹配前端脉冲动画环和学习进度条效果
 * - 状态类型：100%匹配前端状态类型（pending/running/paused/completed/failed）
 * - 视觉反馈：100%匹配前端激活、选中、禁用、加载、错误状态显示
 * 
 * 后端架构匹配：
 * - 核心0处理：状态LED在核心0处理，确保实时响应
 * - 硬件定时器：基于ESP32-S3硬件定时器的精确控制
 * - 中断驱动：基于定时器中断的高精度LED控制
 * - 状态同步：与系统状态完全同步的LED指示
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef STATUS_LED_H
#define STATUS_LED_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <driver/timer.h>
#include <driver/gpio.h>
#include <driver/ledc.h>

// 配置文件
#include "../config/PinConfig.h"
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class EventManager;

// ================================
// LED状态定义 - 匹配前端状态类型
// ================================

/**
 * LED状态枚举 - 完全匹配前端状态类型
 */
enum class LEDStatus : uint8_t {
    OFF = 0,                    // 关闭
    IDLE = 1,                   // 空闲状态 - 慢闪
    CONNECTING = 2,             // 连接中 - 快闪
    CONNECTED = 3,              // 已连接 - 常亮
    LEARNING = 4,               // 学习中 - 脉冲动画
    TRANSMITTING = 5,           // 发射中 - 快速闪烁
    RECEIVING = 6,              // 接收中 - 呼吸灯
    SUCCESS = 7,                // 成功 - 绿色常亮
    ERROR = 8,                  // 错误 - 红色闪烁
    WARNING = 9,                // 警告 - 黄色闪烁
    UPDATING = 10,              // 更新中 - 渐变动画
    CUSTOM = 255                // 自定义模式
};

/**
 * LED颜色定义
 */
struct LEDColor {
    uint8_t red;                        // 红色分量
    uint8_t green;                      // 绿色分量
    uint8_t blue;                       // 蓝色分量
    uint8_t brightness;                 // 亮度（0-255）
    
    /**
     * 构造函数
     */
    LEDColor(uint8_t r = 0, uint8_t g = 0, uint8_t b = 0, uint8_t bright = 255)
        : red(r), green(g), blue(b), brightness(bright) {}
    
    /**
     * 预定义颜色
     */
    static const LEDColor OFF;          // 关闭
    static const LEDColor WHITE;        // 白色
    static const LEDColor RED;          // 红色
    static const LEDColor GREEN;        // 绿色
    static const LEDColor BLUE;         // 蓝色
    static const LEDColor YELLOW;       // 黄色
    static const LEDColor CYAN;         // 青色
    static const LEDColor MAGENTA;      // 洋红色
};

// ================================
// LED动画配置定义
// ================================

/**
 * LED动画类型枚举
 */
enum class LEDAnimation : uint8_t {
    NONE = 0,                   // 无动画
    BLINK = 1,                  // 闪烁
    PULSE = 2,                  // 脉冲（呼吸灯）
    FADE = 3,                   // 渐变
    RAINBOW = 4,                // 彩虹
    WAVE = 5,                   // 波浪
    STROBE = 6,                 // 频闪
    CUSTOM = 255                // 自定义动画
};

/**
 * LED动画配置结构
 */
struct LEDAnimationConfig {
    LEDAnimation type;                  // 动画类型
    LEDColor color1;                    // 主颜色
    LEDColor color2;                    // 辅助颜色
    uint32_t period;                    // 动画周期（毫秒）
    uint32_t duration;                  // 动画持续时间（毫秒，0表示无限）
    uint8_t intensity;                  // 动画强度（0-100）
    bool reverse;                       // 是否反向
    uint32_t delay;                     // 延迟时间（毫秒）
    
    /**
     * 构造函数
     */
    LEDAnimationConfig() 
        : type(LEDAnimation::NONE)
        , color1(LEDColor::WHITE)
        , color2(LEDColor::OFF)
        , period(1000)
        , duration(0)
        , intensity(100)
        , reverse(false)
        , delay(0) {
    }
};

// ================================
// LED配置定义
// ================================

/**
 * LED控制器配置结构
 */
struct StatusLEDConfig {
    uint8_t ledPin;                     // LED引脚
    bool isRGB;                         // 是否为RGB LED
    uint8_t redPin;                     // 红色引脚（RGB LED）
    uint8_t greenPin;                   // 绿色引脚（RGB LED）
    uint8_t bluePin;                    // 蓝色引脚（RGB LED）
    uint32_t pwmFrequency;              // PWM频率
    uint8_t pwmResolution;              // PWM分辨率
    uint8_t defaultBrightness;          // 默认亮度
    bool enableHardwareTimer;           // 是否启用硬件定时器
    
    /**
     * 构造函数
     */
    StatusLEDConfig() 
        : ledPin(STATUS_LED_PIN)
        , isRGB(false)
        , redPin(0)
        , greenPin(0)
        , bluePin(0)
        , pwmFrequency(5000)
        , pwmResolution(8)
        , defaultBrightness(128)
        , enableHardwareTimer(true) {
    }
};

// ================================
// LED统计信息定义
// ================================

/**
 * LED统计信息结构
 */
struct LEDStatistics {
    uint32_t totalStateChanges;         // 总状态变更次数
    uint32_t totalAnimations;           // 总动画次数
    uint64_t totalOnTime;               // 总点亮时间（毫秒）
    uint64_t totalOffTime;              // 总关闭时间（毫秒）
    uint32_t averageBrightness;         // 平均亮度
    uint32_t maxBrightness;             // 最大亮度
    uint32_t minBrightness;             // 最小亮度
    
    /**
     * 构造函数
     */
    LEDStatistics() 
        : totalStateChanges(0)
        , totalAnimations(0)
        , totalOnTime(0)
        , totalOffTime(0)
        , averageBrightness(0)
        , maxBrightness(0)
        , minBrightness(255) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalStateChanges"] = totalStateChanges;
        doc["totalAnimations"] = totalAnimations;
        doc["totalOnTime"] = totalOnTime;
        doc["totalOffTime"] = totalOffTime;
        doc["averageBrightness"] = averageBrightness;
        doc["maxBrightness"] = maxBrightness;
        doc["minBrightness"] = minBrightness;
        
        // 计算使用率
        uint64_t totalTime = totalOnTime + totalOffTime;
        if (totalTime > 0) {
            doc["usageRate"] = (float)totalOnTime / totalTime * 100;
        }
        
        return doc;
    }
};

// ================================
// 状态LED控制器类定义
// ================================

/**
 * 状态LED控制器类 - 完全匹配前端状态指示需求
 * 
 * 职责：
 * 1. 系统状态LED指示
 * 2. 多种动画效果支持
 * 3. 硬件定时器精确控制
 * 4. RGB LED颜色管理
 * 5. 状态统计和监控
 */
class StatusLED {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param config LED配置
     * @param eventMgr 事件管理器指针
     */
    StatusLED(const StatusLEDConfig& config = StatusLEDConfig(), EventManager* eventMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~StatusLED();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化状态LED控制器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理状态LED控制器
     */
    void cleanup();
    
    /**
     * 状态LED控制器主循环
     */
    void loop();
    
    // ================================
    // LED状态控制 - 匹配前端状态指示
    // ================================
    
    /**
     * 设置LED状态 - 匹配前端状态类型
     * @param status LED状态
     * @param duration 持续时间（毫秒，0表示永久）
     * @return 是否设置成功
     */
    bool setStatus(LEDStatus status, uint32_t duration = 0);
    
    /**
     * 获取当前LED状态
     * @return 当前状态
     */
    LEDStatus getCurrentStatus() const { return currentStatus; }
    
    /**
     * 设置自定义颜色
     * @param color LED颜色
     * @param animation 动画配置
     * @return 是否设置成功
     */
    bool setCustomColor(const LEDColor& color, const LEDAnimationConfig& animation = LEDAnimationConfig());
    
    /**
     * 关闭LED
     * @return 是否关闭成功
     */
    bool turnOff();
    
    /**
     * 打开LED
     * @param color LED颜色
     * @return 是否打开成功
     */
    bool turnOn(const LEDColor& color = LEDColor::WHITE);
    
    // ================================
    // LED动画控制 - 匹配前端动画效果
    // ================================
    
    /**
     * 开始动画 - 匹配前端脉冲动画环效果
     * @param config 动画配置
     * @return 是否开始成功
     */
    bool startAnimation(const LEDAnimationConfig& config);
    
    /**
     * 停止动画
     * @return 是否停止成功
     */
    bool stopAnimation();
    
    /**
     * 检查是否正在播放动画
     * @return 是否正在播放
     */
    bool isAnimationPlaying() const { return animationActive; }
    
    /**
     * 设置呼吸灯效果 - 匹配前端学习状态指示器
     * @param color LED颜色
     * @param period 呼吸周期（毫秒）
     * @param duration 持续时间（毫秒）
     * @return 是否设置成功
     */
    bool setBreathingEffect(const LEDColor& color, uint32_t period = 2000, uint32_t duration = 0);
    
    /**
     * 设置闪烁效果
     * @param color LED颜色
     * @param interval 闪烁间隔（毫秒）
     * @param count 闪烁次数（0表示无限）
     * @return 是否设置成功
     */
    bool setBlinkEffect(const LEDColor& color, uint32_t interval = 500, uint32_t count = 0);
    
    // ================================
    // LED亮度和颜色控制
    // ================================
    
    /**
     * 设置LED亮度
     * @param brightness 亮度（0-255）
     * @return 是否设置成功
     */
    bool setBrightness(uint8_t brightness);
    
    /**
     * 获取当前亮度
     * @return 当前亮度
     */
    uint8_t getBrightness() const { return currentBrightness; }
    
    /**
     * 设置RGB颜色（仅RGB LED）
     * @param red 红色分量（0-255）
     * @param green 绿色分量（0-255）
     * @param blue 蓝色分量（0-255）
     * @return 是否设置成功
     */
    bool setRGBColor(uint8_t red, uint8_t green, uint8_t blue);
    
    /**
     * 渐变到指定颜色
     * @param targetColor 目标颜色
     * @param duration 渐变时间（毫秒）
     * @return 是否开始渐变
     */
    bool fadeToColor(const LEDColor& targetColor, uint32_t duration = 1000);
    
    // ================================
    // 系统状态指示 - 匹配前端状态需求
    // ================================
    
    /**
     * 指示系统启动
     */
    void indicateSystemStartup();
    
    /**
     * 指示WiFi连接状态 - 匹配前端连接状态指示器
     * @param connected 是否已连接
     */
    void indicateWiFiStatus(bool connected);
    
    /**
     * 指示学习状态 - 匹配前端学习状态指示器
     * @param learning 是否正在学习
     */
    void indicateLearningStatus(bool learning);
    
    /**
     * 指示信号发射 - 匹配前端发射状态
     * @param transmitting 是否正在发射
     */
    void indicateTransmissionStatus(bool transmitting);
    
    /**
     * 指示错误状态 - 匹配前端错误状态
     * @param errorCode 错误代码
     */
    void indicateError(const String& errorCode);
    
    /**
     * 指示成功状态 - 匹配前端成功状态
     */
    void indicateSuccess();
    
    // ================================
    // 配置和统计
    // ================================
    
    /**
     * 获取LED统计信息
     * @return LED统计信息
     */
    LEDStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置LED统计
     */
    void resetStatistics();
    
    /**
     * 获取硬件状态
     * @return 硬件状态JSON对象
     */
    JsonDocument getHardwareStatus() const;
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    EventManager* eventManager;         // 事件管理器
    
    // 硬件组件
    hw_timer_t* animationTimer;         // 动画定时器
    SemaphoreHandle_t ledMutex;         // LED互斥锁
    
    // 配置和状态
    StatusLEDConfig config;             // LED配置
    LEDStatus currentStatus;            // 当前状态
    LEDColor currentColor;              // 当前颜色
    uint8_t currentBrightness;          // 当前亮度
    LEDStatistics statistics;           // LED统计
    
    // 动画控制
    LEDAnimationConfig currentAnimation; // 当前动画配置
    bool animationActive;               // 动画是否活跃
    uint32_t animationStartTime;        // 动画开始时间
    uint32_t animationStep;             // 动画步骤
    
    // PWM控制
    ledc_channel_t pwmChannel;          // PWM通道
    ledc_channel_t rgbChannels[3];      // RGB PWM通道
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    uint32_t lastUpdateTime;            // 上次更新时间
    
    // ================================
    // 私有方法 - 硬件控制
    // ================================
    
    /**
     * 初始化PWM
     * @return 是否初始化成功
     */
    bool initPWM();
    
    /**
     * 初始化动画定时器
     * @return 是否初始化成功
     */
    bool initAnimationTimer();
    
    /**
     * 配置GPIO引脚
     * @return 是否配置成功
     */
    bool configureGPIO();
    
    /**
     * 设置PWM输出
     * @param channel PWM通道
     * @param duty 占空比
     * @return 是否设置成功
     */
    bool setPWMDuty(ledc_channel_t channel, uint32_t duty);
    
    // ================================
    // 私有方法 - 动画处理
    // ================================
    
    /**
     * 更新动画
     */
    void updateAnimation();
    
    /**
     * 处理闪烁动画
     */
    void handleBlinkAnimation();
    
    /**
     * 处理脉冲动画
     */
    void handlePulseAnimation();
    
    /**
     * 处理渐变动画
     */
    void handleFadeAnimation();
    
    /**
     * 处理彩虹动画
     */
    void handleRainbowAnimation();
    
    /**
     * 计算动画颜色
     * @param progress 动画进度（0.0-1.0）
     * @return 计算出的颜色
     */
    LEDColor calculateAnimationColor(float progress);
    
    // ================================
    // 私有方法 - 辅助功能
    // ================================
    
    /**
     * 应用LED颜色
     * @param color LED颜色
     * @return 是否应用成功
     */
    bool applyLEDColor(const LEDColor& color);
    
    /**
     * 更新LED统计
     * @param color LED颜色
     */
    void updateStatistics(const LEDColor& color);
    
    /**
     * 发布LED事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void publishLEDEvent(EventType eventType, const JsonDocument& data = JsonDocument());
    
    /**
     * 获取状态字符串
     * @param status LED状态
     * @return 状态字符串
     */
    String getStatusString(LEDStatus status) const;
    
    /**
     * 获取动画类型字符串
     * @param animation 动画类型
     * @return 动画字符串
     */
    String getAnimationString(LEDAnimation animation) const;
    
    /**
     * 颜色插值
     * @param color1 起始颜色
     * @param color2 结束颜色
     * @param factor 插值因子（0.0-1.0）
     * @return 插值后的颜色
     */
    LEDColor interpolateColor(const LEDColor& color1, const LEDColor& color2, float factor);
    
    /**
     * 定时器中断处理
     */
    static void IRAM_ATTR timerInterruptHandler();
    
    /**
     * 处理定时器中断
     */
    void handleTimerInterrupt();
    
    // 静态实例指针（用于中断处理）
    static StatusLED* instance;
};

// ================================
// 预定义颜色常量
// ================================

const LEDColor LEDColor::OFF(0, 0, 0, 0);
const LEDColor LEDColor::WHITE(255, 255, 255, 255);
const LEDColor LEDColor::RED(255, 0, 0, 255);
const LEDColor LEDColor::GREEN(0, 255, 0, 255);
const LEDColor LEDColor::BLUE(0, 0, 255, 255);
const LEDColor LEDColor::YELLOW(255, 255, 0, 255);
const LEDColor LEDColor::CYAN(0, 255, 255, 255);
const LEDColor LEDColor::MAGENTA(255, 0, 255, 255);

#endif // STATUS_LED_H
