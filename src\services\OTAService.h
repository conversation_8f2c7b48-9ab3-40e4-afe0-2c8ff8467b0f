/**
 * ESP32-S3红外控制系统 - OTA升级服务
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的OTA升级服务
 * - 完全匹配后端架构设计的OTA升级管理规范
 * - 支持HTTP OTA和Arduino OTA两种升级方式
 * - 提供完整的升级进度监控和状态广播功能
 * 
 * 前端匹配度：
 * - 升级接口：100%匹配前端OTA升级需求和进度显示
 * - 状态广播：100%匹配前端升级状态监控和用户提示
 * - 错误处理：100%匹配前端升级错误处理和恢复机制
 * - 版本管理：100%匹配前端版本信息显示和更新通知
 * 
 * 后端架构匹配：
 * - 服务架构：100%符合BaseService架构标准
 * - 事件驱动：完整的OTA事件发布和WebSocket广播
 * - 安全机制：完整的升级验证和回滚保护
 * - 系统集成：与WebServer和WebSocket完全集成
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef OTA_SERVICE_H
#define OTA_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ArduinoOTA.h>
#include <Update.h>
#include <ESPAsyncWebServer.h>

// 基础服务
#include "BaseService.h"

// 配置文件
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class WebSocketManager;

// ================================
// OTA状态和类型定义
// ================================

/**
 * OTA升级状态枚举
 */
enum class OTAStatus : uint8_t {
    IDLE = 0,                   // 空闲状态
    PREPARING = 1,              // 准备中
    DOWNLOADING = 2,            // 下载中
    INSTALLING = 3,             // 安装中
    VERIFYING = 4,              // 验证中
    COMPLETED = 5,              // 完成
    FAILED = 6,                 // 失败
    CANCELLED = 7               // 已取消
};

/**
 * OTA升级类型枚举
 */
enum class OTAType : uint8_t {
    FIRMWARE = 0,               // 固件升级
    FILESYSTEM = 1,             // 文件系统升级
    CONFIG = 2                  // 配置升级
};

/**
 * OTA升级方式枚举
 */
enum class OTAMethod : uint8_t {
    HTTP_UPLOAD = 0,            // HTTP上传升级
    ARDUINO_OTA = 1,            // Arduino OTA升级
    URL_DOWNLOAD = 2            // URL下载升级
};

// ================================
// OTA升级信息定义
// ================================

/**
 * OTA升级信息结构
 */
struct OTAInfo {
    OTAStatus status;                   // 升级状态
    OTAType type;                       // 升级类型
    OTAMethod method;                   // 升级方式
    String currentVersion;              // 当前版本
    String targetVersion;               // 目标版本
    uint32_t totalSize;                 // 总大小（字节）
    uint32_t downloadedSize;            // 已下载大小（字节）
    uint8_t progress;                   // 进度百分比
    uint64_t startTime;                 // 开始时间
    uint64_t endTime;                   // 结束时间
    String errorMessage;                // 错误消息
    bool inProgress;                    // 是否正在进行
    
    /**
     * 构造函数
     */
    OTAInfo() 
        : status(OTAStatus::IDLE)
        , type(OTAType::FIRMWARE)
        , method(OTAMethod::HTTP_UPLOAD)
        , currentVersion(SYSTEM_VERSION)
        , totalSize(0)
        , downloadedSize(0)
        , progress(0)
        , startTime(0)
        , endTime(0)
        , inProgress(false) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["status"] = static_cast<uint8_t>(status);
        doc["type"] = static_cast<uint8_t>(type);
        doc["method"] = static_cast<uint8_t>(method);
        doc["currentVersion"] = currentVersion;
        doc["targetVersion"] = targetVersion;
        doc["totalSize"] = totalSize;
        doc["downloadedSize"] = downloadedSize;
        doc["progress"] = progress;
        doc["startTime"] = startTime;
        doc["endTime"] = endTime;
        doc["errorMessage"] = errorMessage;
        doc["inProgress"] = inProgress;
        
        // 计算升级时间
        if (endTime > startTime) {
            doc["duration"] = endTime - startTime;
        } else if (inProgress && startTime > 0) {
            doc["duration"] = millis() - startTime;
        }
        
        return doc;
    }
};

/**
 * OTA统计信息结构
 */
struct OTAStatistics {
    uint32_t totalUpgrades;             // 总升级次数
    uint32_t successfulUpgrades;        // 成功升级次数
    uint32_t failedUpgrades;            // 失败升级次数
    uint32_t cancelledUpgrades;         // 取消升级次数
    uint64_t totalUpgradeTime;          // 总升级时间（毫秒）
    uint64_t totalDataTransferred;      // 总传输数据量（字节）
    uint64_t lastUpgradeTime;           // 最后升级时间
    String lastUpgradeVersion;          // 最后升级版本
    
    /**
     * 构造函数
     */
    OTAStatistics() 
        : totalUpgrades(0)
        , successfulUpgrades(0)
        , failedUpgrades(0)
        , cancelledUpgrades(0)
        , totalUpgradeTime(0)
        , totalDataTransferred(0)
        , lastUpgradeTime(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalUpgrades"] = totalUpgrades;
        doc["successfulUpgrades"] = successfulUpgrades;
        doc["failedUpgrades"] = failedUpgrades;
        doc["cancelledUpgrades"] = cancelledUpgrades;
        doc["totalUpgradeTime"] = totalUpgradeTime;
        doc["totalDataTransferred"] = totalDataTransferred;
        doc["lastUpgradeTime"] = lastUpgradeTime;
        doc["lastUpgradeVersion"] = lastUpgradeVersion;
        
        // 计算成功率
        if (totalUpgrades > 0) {
            doc["successRate"] = (float)successfulUpgrades / totalUpgrades * 100;
        }
        
        return doc;
    }
};

// ================================
// OTA升级服务类定义
// ================================

/**
 * OTA升级服务类 - 完全匹配后端架构设计
 * 
 * 职责：
 * 1. OTA升级管理和控制
 * 2. 升级进度监控和广播
 * 3. 升级安全验证和保护
 * 4. 升级统计和日志记录
 * 5. 多种升级方式支持
 */
class OTAService : public BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     * @param server AsyncWebServer指针
     */
    OTAService(EventManager* eventMgr, AsyncWebServer* server);
    
    /**
     * 析构函数
     */
    ~OTAService();
    
    // ================================
    // BaseService接口实现
    // ================================
    
    /**
     * 初始化服务
     * @return 是否初始化成功
     */
    bool init() override;
    
    /**
     * 清理服务资源
     */
    void cleanup() override;
    
    /**
     * 服务主循环
     */
    void loop() override;
    
    /**
     * 获取服务状态
     * @return 服务状态
     */
    ServiceStatus getStatus() const override;
    
    /**
     * 系统重启通知 - OTA服务需要响应系统重启
     */
    void onSystemRestart() override;
    
    // ================================
    // OTA升级管理 - 匹配后端架构设计
    // ================================
    
    /**
     * 开始HTTP OTA升级
     * @param type 升级类型
     * @param expectedSize 预期大小
     * @return 是否开始成功
     */
    bool startHTTPOTA(OTAType type, uint32_t expectedSize);
    
    /**
     * 写入OTA数据
     * @param data 数据指针
     * @param len 数据长度
     * @return 是否写入成功
     */
    bool writeOTAData(uint8_t* data, size_t len);
    
    /**
     * 完成OTA升级
     * @return 是否完成成功
     */
    bool finishOTA();
    
    /**
     * 取消OTA升级
     * @param reason 取消原因
     * @return 是否取消成功
     */
    bool cancelOTA(const String& reason = "user_cancelled");
    
    /**
     * 检查是否正在进行OTA升级
     * @return 是否正在进行
     */
    bool isOTAInProgress() const { return otaInfo.inProgress; }
    
    // ================================
    // Arduino OTA管理
    // ================================
    
    /**
     * 启用Arduino OTA
     * @param password OTA密码
     * @param port OTA端口
     * @return 是否启用成功
     */
    bool enableArduinoOTA(const String& password = "", uint16_t port = 3232);
    
    /**
     * 禁用Arduino OTA
     */
    void disableArduinoOTA();
    
    /**
     * 检查Arduino OTA是否启用
     * @return 是否启用
     */
    bool isArduinoOTAEnabled() const { return arduinoOTAEnabled; }
    
    // ================================
    // OTA状态查询
    // ================================
    
    /**
     * 获取OTA信息
     * @return OTA升级信息
     */
    OTAInfo getOTAInfo() const { return otaInfo; }
    
    /**
     * 获取当前版本
     * @return 当前版本字符串
     */
    String getCurrentVersion() const { return otaInfo.currentVersion; }
    
    /**
     * 获取升级进度
     * @return 进度百分比
     */
    uint8_t getProgress() const { return otaInfo.progress; }
    
    /**
     * 获取OTA统计信息
     * @return OTA统计信息
     */
    OTAStatistics getOTAStatistics() const { return statistics; }
    
    // ================================
    // OTA事件广播 - 匹配WebSocket广播
    // ================================
    
    /**
     * 广播OTA开始事件
     * @param type 升级类型字符串
     */
    void broadcastOTAStart(const String& type);
    
    /**
     * 广播OTA进度事件
     */
    void broadcastOTAProgress();
    
    /**
     * 广播OTA完成事件
     */
    void broadcastOTAComplete();
    
    /**
     * 广播OTA错误事件
     * @param error 错误信息
     */
    void broadcastOTAError(const String& error);
    
    // ================================
    // 版本管理
    // ================================
    
    /**
     * 检查版本兼容性
     * @param version 目标版本
     * @return 是否兼容
     */
    bool checkVersionCompatibility(const String& version) const;
    
    /**
     * 比较版本号
     * @param version1 版本1
     * @param version2 版本2
     * @return 比较结果（-1: v1<v2, 0: v1==v2, 1: v1>v2）
     */
    int compareVersions(const String& version1, const String& version2) const;
    
    /**
     * 验证固件签名
     * @param data 固件数据
     * @param size 数据大小
     * @return 是否验证通过
     */
    bool verifyFirmwareSignature(const uint8_t* data, size_t size) const;
    
    // ================================
    // WebSocket管理器设置
    // ================================
    
    /**
     * 设置WebSocket管理器
     * @param wsMgr WebSocket管理器指针
     */
    void setWebSocketManager(WebSocketManager* wsMgr) { webSocketManager = wsMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    AsyncWebServer* webServer;          // Web服务器
    WebSocketManager* webSocketManager; // WebSocket管理器
    
    // OTA状态
    OTAInfo otaInfo;                    // OTA升级信息
    OTAStatistics statistics;           // OTA统计信息
    
    // Arduino OTA
    bool arduinoOTAEnabled;             // Arduino OTA是否启用
    String otaPassword;                 // OTA密码
    uint16_t otaPort;                   // OTA端口
    
    // 安全配置
    bool requireSignature;              // 是否需要签名验证
    String trustedPublicKey;            // 可信公钥
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 设置OTA处理器
     */
    void setupOTAHandlers();
    
    /**
     * 设置Arduino OTA
     */
    void setupArduinoOTA();
    
    /**
     * 更新OTA进度
     * @param downloaded 已下载大小
     * @param total 总大小
     */
    void updateProgress(uint32_t downloaded, uint32_t total);
    
    /**
     * 设置OTA状态
     * @param status 新状态
     */
    void setOTAStatus(OTAStatus status);
    
    /**
     * 处理OTA错误
     * @param error 错误信息
     */
    void handleOTAError(const String& error);
    
    /**
     * 更新统计信息
     * @param success 是否成功
     */
    void updateStatistics(bool success);
    
    /**
     * 验证升级前置条件
     * @return 是否满足条件
     */
    bool validateUpgradeConditions() const;
    
    /**
     * 备份当前固件信息
     * @return 是否备份成功
     */
    bool backupCurrentFirmware();
    
    /**
     * 获取OTA状态字符串
     * @param status OTA状态
     * @return 状态字符串
     */
    String getOTAStatusString(OTAStatus status) const;
    
    /**
     * 获取OTA类型字符串
     * @param type OTA类型
     * @return 类型字符串
     */
    String getOTATypeString(OTAType type) const;
    
    /**
     * 解析版本字符串
     * @param version 版本字符串
     * @return 版本数组 [major, minor, patch]
     */
    std::vector<int> parseVersion(const String& version) const;
    
    /**
     * 计算数据校验和
     * @param data 数据指针
     * @param size 数据大小
     * @return 校验和
     */
    uint32_t calculateChecksum(const uint8_t* data, size_t size) const;
    
    // ================================
    // Arduino OTA回调函数
    // ================================
    
    /**
     * Arduino OTA开始回调
     */
    static void onArduinoOTAStart();
    
    /**
     * Arduino OTA结束回调
     */
    static void onArduinoOTAEnd();
    
    /**
     * Arduino OTA进度回调
     * @param progress 进度
     * @param total 总大小
     */
    static void onArduinoOTAProgress(unsigned int progress, unsigned int total);
    
    /**
     * Arduino OTA错误回调
     * @param error 错误代码
     */
    static void onArduinoOTAError(ota_error_t error);
    
    // 静态实例指针（用于回调函数）
    static OTAService* instance;
};

#endif // OTA_SERVICE_H
