/**
 * ESP32-S3红外控制系统 - 数据验证器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的数据验证器
 * - 完全匹配前端数据验证规则和后端架构设计规范
 * - 支持信号数据、任务数据、配置数据的完整验证
 * - 提供数据完整性检查、格式验证、业务规则验证功能
 * 
 * 前端匹配度：
 * - 验证规则：100%匹配前端数据验证器的验证规则
 * - 错误格式：100%匹配前端错误处理和提示格式
 * - 数据格式：100%匹配前端数据结构和字段定义
 * - 业务规则：100%匹配前端业务逻辑验证需求
 * 
 * 后端架构匹配：
 * - 存储层集成：与OptimizedStorage完全集成
 * - 性能优化：高效的验证算法和缓存机制
 * - 错误处理：完整的验证错误收集和报告
 * - 扩展性：支持自定义验证规则和插件机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef DATA_VALIDATOR_H
#define DATA_VALIDATOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <unordered_map>
#include <functional>
#include <regex>

// 数据类型
#include "../types/SignalData.h"
#include "../types/TaskData.h"

// ================================
// 验证结果定义
// ================================

/**
 * 验证错误级别枚举
 */
enum class ValidationLevel : uint8_t {
    INFO = 0,                   // 信息级别
    WARNING = 1,                // 警告级别
    ERROR = 2,                  // 错误级别
    CRITICAL = 3                // 关键错误级别
};

/**
 * 验证错误结构
 */
struct ValidationError {
    ValidationLevel level;              // 错误级别
    String field;                       // 字段名称
    String message;                     // 错误消息
    String code;                        // 错误代码
    JsonVariant value;                  // 错误值
    String suggestion;                  // 修复建议
    
    /**
     * 构造函数
     */
    ValidationError(ValidationLevel lvl = ValidationLevel::ERROR, 
                   const String& fieldName = "", 
                   const String& errorMessage = "",
                   const String& errorCode = "")
        : level(lvl)
        , field(fieldName)
        , message(errorMessage)
        , code(errorCode) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["level"] = static_cast<uint8_t>(level);
        doc["field"] = field;
        doc["message"] = message;
        doc["code"] = code;
        doc["value"] = value;
        doc["suggestion"] = suggestion;
        return doc;
    }
};

/**
 * 验证结果结构
 */
struct ValidationResult {
    bool isValid;                       // 是否验证通过
    std::vector<ValidationError> errors; // 验证错误列表
    std::vector<ValidationError> warnings; // 验证警告列表
    uint32_t validationTime;            // 验证耗时（微秒）
    
    /**
     * 构造函数
     */
    ValidationResult() 
        : isValid(true)
        , validationTime(0) {
    }
    
    /**
     * 添加错误
     */
    void addError(const ValidationError& error) {
        if (error.level == ValidationLevel::ERROR || error.level == ValidationLevel::CRITICAL) {
            errors.push_back(error);
            isValid = false;
        } else if (error.level == ValidationLevel::WARNING) {
            warnings.push_back(error);
        }
    }
    
    /**
     * 检查是否有错误
     */
    bool hasErrors() const { return !errors.empty(); }
    
    /**
     * 检查是否有警告
     */
    bool hasWarnings() const { return !warnings.empty(); }
    
    /**
     * 获取错误数量
     */
    uint32_t getErrorCount() const { return errors.size(); }
    
    /**
     * 获取警告数量
     */
    uint32_t getWarningCount() const { return warnings.size(); }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["isValid"] = isValid;
        doc["validationTime"] = validationTime;
        doc["errorCount"] = errors.size();
        doc["warningCount"] = warnings.size();
        
        JsonArray errorsArray = doc["errors"].to<JsonArray>();
        for (const auto& error : errors) {
            errorsArray.add(error.toJson());
        }
        
        JsonArray warningsArray = doc["warnings"].to<JsonArray>();
        for (const auto& warning : warnings) {
            warningsArray.add(warning.toJson());
        }
        
        return doc;
    }
};

// ================================
// 验证规则定义
// ================================

/**
 * 验证规则类型枚举
 */
enum class ValidationRuleType : uint8_t {
    REQUIRED = 0,               // 必填验证
    TYPE = 1,                   // 类型验证
    LENGTH = 2,                 // 长度验证
    RANGE = 3,                  // 范围验证
    PATTERN = 4,                // 模式验证
    CUSTOM = 5,                 // 自定义验证
    ENUM = 6,                   // 枚举验证
    UNIQUE = 7                  // 唯一性验证
};

/**
 * 验证规则结构
 */
struct ValidationRule {
    ValidationRuleType type;            // 规则类型
    String field;                       // 字段名称
    JsonVariant parameter;              // 规则参数
    String message;                     // 错误消息
    ValidationLevel level;              // 错误级别
    std::function<bool(const JsonVariant&)> customValidator; // 自定义验证函数
    
    /**
     * 构造函数
     */
    ValidationRule(ValidationRuleType ruleType, const String& fieldName, 
                  const String& errorMessage = "", ValidationLevel errorLevel = ValidationLevel::ERROR)
        : type(ruleType)
        , field(fieldName)
        , message(errorMessage)
        , level(errorLevel) {
    }
};

// ================================
// 数据验证器类定义
// ================================

/**
 * 数据验证器类 - 完全匹配前端数据验证需求
 * 
 * 职责：
 * 1. 数据格式验证
 * 2. 业务规则验证
 * 3. 数据完整性检查
 * 4. 自定义验证规则
 * 5. 验证结果报告
 */
class DataValidator {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     */
    DataValidator();
    
    /**
     * 析构函数
     */
    ~DataValidator();
    
    // ================================
    // 信号数据验证 - 匹配前端SignalData验证
    // ================================
    
    /**
     * 验证信号数据 - 匹配前端信号验证规则
     * @param signal 信号数据
     * @return 验证结果
     */
    ValidationResult validateSignalData(const SignalData& signal);
    
    /**
     * 验证信号JSON数据
     * @param signalJson 信号JSON对象
     * @return 验证结果
     */
    ValidationResult validateSignalJson(const JsonDocument& signalJson);
    
    /**
     * 批量验证信号数据
     * @param signals 信号数据列表
     * @return 验证结果列表
     */
    std::vector<ValidationResult> validateSignalsBatch(const std::vector<SignalData>& signals);
    
    // ================================
    // 任务数据验证 - 匹配前端TaskData验证
    // ================================
    
    /**
     * 验证定时任务数据 - 匹配前端任务验证规则
     * @param task 任务数据
     * @return 验证结果
     */
    ValidationResult validateTaskData(const TaskData& task);
    
    /**
     * 验证任务JSON数据
     * @param taskJson 任务JSON对象
     * @return 验证结果
     */
    ValidationResult validateTaskJson(const JsonDocument& taskJson);
    
    /**
     * 批量验证任务数据
     * @param tasks 任务数据列表
     * @return 验证结果列表
     */
    std::vector<ValidationResult> validateTasksBatch(const std::vector<TaskData>& tasks);
    
    // ================================
    // 配置数据验证 - 匹配前端配置验证
    // ================================
    
    /**
     * 验证系统配置数据
     * @param configJson 配置JSON对象
     * @return 验证结果
     */
    ValidationResult validateSystemConfig(const JsonDocument& configJson);
    
    /**
     * 验证网络配置数据
     * @param configJson 网络配置JSON对象
     * @return 验证结果
     */
    ValidationResult validateNetworkConfig(const JsonDocument& configJson);
    
    /**
     * 验证硬件配置数据
     * @param configJson 硬件配置JSON对象
     * @return 验证结果
     */
    ValidationResult validateHardwareConfig(const JsonDocument& configJson);
    
    // ================================
    // 通用JSON验证 - 匹配前端通用验证
    // ================================
    
    /**
     * 验证JSON数据
     * @param jsonData JSON数据
     * @param rules 验证规则列表
     * @return 验证结果
     */
    ValidationResult validateJson(const JsonDocument& jsonData, const std::vector<ValidationRule>& rules);
    
    /**
     * 验证JSON字段
     * @param jsonData JSON数据
     * @param field 字段名称
     * @param rule 验证规则
     * @return 验证错误（如果有）
     */
    ValidationError validateField(const JsonDocument& jsonData, const String& field, const ValidationRule& rule);
    
    // ================================
    // 验证规则管理
    // ================================
    
    /**
     * 添加自定义验证规则
     * @param dataType 数据类型
     * @param rule 验证规则
     */
    void addValidationRule(const String& dataType, const ValidationRule& rule);
    
    /**
     * 移除验证规则
     * @param dataType 数据类型
     * @param field 字段名称
     */
    void removeValidationRule(const String& dataType, const String& field);
    
    /**
     * 获取验证规则
     * @param dataType 数据类型
     * @return 验证规则列表
     */
    std::vector<ValidationRule> getValidationRules(const String& dataType);
    
    /**
     * 清除所有验证规则
     * @param dataType 数据类型
     */
    void clearValidationRules(const String& dataType);
    
    // ================================
    // 数据完整性验证
    // ================================
    
    /**
     * 验证数据完整性
     * @param jsonData JSON数据
     * @param requiredFields 必需字段列表
     * @return 验证结果
     */
    ValidationResult validateDataIntegrity(const JsonDocument& jsonData, const std::vector<String>& requiredFields);
    
    /**
     * 验证数据一致性
     * @param jsonData JSON数据
     * @param referenceData 参考数据
     * @return 验证结果
     */
    ValidationResult validateDataConsistency(const JsonDocument& jsonData, const JsonDocument& referenceData);
    
    /**
     * 验证数据唯一性
     * @param jsonData JSON数据
     * @param uniqueFields 唯一字段列表
     * @param existingData 已存在数据列表
     * @return 验证结果
     */
    ValidationResult validateDataUniqueness(const JsonDocument& jsonData, const std::vector<String>& uniqueFields,
                                           const std::vector<JsonDocument>& existingData);
    
    // ================================
    // 验证统计和监控
    // ================================
    
    /**
     * 获取验证统计信息
     * @return 统计信息JSON对象
     */
    JsonDocument getValidationStatistics();
    
    /**
     * 重置验证统计
     */
    void resetValidationStatistics();
    
    /**
     * 获取验证性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getValidationPerformance();

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 验证规则存储
    std::unordered_map<String, std::vector<ValidationRule>> validationRules;
    
    // 验证统计
    struct ValidationStatistics {
        uint32_t totalValidations;     // 总验证次数
        uint32_t successfulValidations; // 成功验证次数
        uint32_t failedValidations;    // 失败验证次数
        uint32_t totalErrors;          // 总错误数
        uint32_t totalWarnings;        // 总警告数
        uint64_t totalValidationTime;  // 总验证时间
        uint64_t lastValidationTime;   // 最后验证时间
    } statistics;
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化默认验证规则
     */
    void initDefaultValidationRules();
    
    /**
     * 初始化信号验证规则
     */
    void initSignalValidationRules();
    
    /**
     * 初始化任务验证规则
     */
    void initTaskValidationRules();
    
    /**
     * 初始化配置验证规则
     */
    void initConfigValidationRules();
    
    /**
     * 验证必填字段
     * @param jsonData JSON数据
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validateRequired(const JsonDocument& jsonData, const String& field);
    
    /**
     * 验证字段类型
     * @param value 字段值
     * @param expectedType 期望类型
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validateType(const JsonVariant& value, const String& expectedType, const String& field);
    
    /**
     * 验证字段长度
     * @param value 字段值
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validateLength(const JsonVariant& value, uint32_t minLength, uint32_t maxLength, const String& field);
    
    /**
     * 验证数值范围
     * @param value 字段值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validateRange(const JsonVariant& value, double minValue, double maxValue, const String& field);
    
    /**
     * 验证字段模式
     * @param value 字段值
     * @param pattern 正则表达式模式
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validatePattern(const JsonVariant& value, const String& pattern, const String& field);
    
    /**
     * 验证枚举值
     * @param value 字段值
     * @param enumValues 枚举值列表
     * @param field 字段名称
     * @return 验证错误（如果有）
     */
    ValidationError validateEnum(const JsonVariant& value, const std::vector<String>& enumValues, const String& field);
    
    /**
     * 验证信号ID格式
     * @param signalId 信号ID
     * @return 是否有效
     */
    bool isValidSignalId(const String& signalId);
    
    /**
     * 验证红外协议
     * @param protocol 协议名称
     * @return 是否有效
     */
    bool isValidIRProtocol(const String& protocol);
    
    /**
     * 验证红外频率
     * @param frequency 频率值
     * @return 是否有效
     */
    bool isValidIRFrequency(uint32_t frequency);
    
    /**
     * 验证时间格式
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    bool isValidTimeFormat(const String& timeStr);
    
    /**
     * 验证IP地址格式
     * @param ipStr IP地址字符串
     * @return 是否有效
     */
    bool isValidIPAddress(const String& ipStr);
    
    /**
     * 验证MAC地址格式
     * @param macStr MAC地址字符串
     * @return 是否有效
     */
    bool isValidMACAddress(const String& macStr);
    
    /**
     * 更新验证统计
     * @param success 是否成功
     * @param errorCount 错误数量
     * @param warningCount 警告数量
     * @param validationTime 验证时间
     */
    void updateStatistics(bool success, uint32_t errorCount, uint32_t warningCount, uint32_t validationTime);
    
    /**
     * 获取验证级别字符串
     * @param level 验证级别
     * @return 级别字符串
     */
    String getValidationLevelString(ValidationLevel level);
    
    /**
     * 获取验证规则类型字符串
     * @param type 规则类型
     * @return 类型字符串
     */
    String getValidationRuleTypeString(ValidationRuleType type);
};

#endif // DATA_VALIDATOR_H
