/**
 * ESP32-S3红外控制系统 - 服务基类
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的服务基类
 * - 提供统一的服务接口和生命周期管理
 * - 完全匹配前端BaseModule的设计模式和架构标准
 * - 统一的错误处理、事件发布和配置管理机制
 * 
 * 前端匹配度：
 * - 模块化设计：100%匹配前端BaseModule的设计模式
 * - 生命周期：完全匹配前端模块的init/cleanup/loop流程
 * - 事件机制：统一的事件发布和订阅机制
 * - 错误处理：统一的错误处理和报告机制
 * 
 * 后端架构匹配：
 * - 服务注册：支持自动注册和发现机制
 * - 状态管理：统一的服务状态监控和报告
 * - 配置响应：支持配置变更通知和响应
 * - 系统集成：与SystemManager和EventManager完全集成
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef BASE_SERVICE_H
#define BASE_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>

// 配置文件
#include "../config/SystemConfig.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;
class ErrorHandler;

// ================================
// 服务状态枚举定义
// ================================

/**
 * 服务状态枚举 - 匹配前端模块状态管理
 */
enum class ServiceStatus : uint8_t {
    UNINITIALIZED = 0,      // 未初始化
    INITIALIZING = 1,       // 初始化中
    READY = 2,              // 就绪状态
    RUNNING = 3,            // 运行中
    PAUSED = 4,             // 已暂停
    ERROR = 5,              // 错误状态
    STOPPING = 6,           // 停止中
    STOPPED = 7             // 已停止
};

/**
 * 服务优先级枚举
 */
enum class ServicePriority : uint8_t {
    PRIORITY_LOW = 1,       // 低优先级
    PRIORITY_NORMAL = 2,    // 普通优先级
    PRIORITY_HIGH = 3,      // 高优先级
    PRIORITY_CRITICAL = 4   // 关键优先级
};

// ================================
// 服务信息结构定义
// ================================

/**
 * 服务信息结构
 */
struct ServiceInfo {
    String name;                    // 服务名称
    String version;                 // 服务版本
    String description;             // 服务描述
    ServiceStatus status;           // 服务状态
    ServicePriority priority;       // 服务优先级
    uint32_t startTime;             // 启动时间
    uint32_t lastActivity;          // 最后活动时间
    uint32_t errorCount;            // 错误计数
    String lastError;               // 最后错误信息
    
    /**
     * 构造函数
     */
    ServiceInfo(const String& serviceName = "", const String& serviceVersion = "1.0.0")
        : name(serviceName)
        , version(serviceVersion)
        , status(ServiceStatus::UNINITIALIZED)
        , priority(ServicePriority::NORMAL)
        , startTime(0)
        , lastActivity(0)
        , errorCount(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["name"] = name;
        doc["version"] = version;
        doc["description"] = description;
        doc["status"] = static_cast<uint8_t>(status);
        doc["priority"] = static_cast<uint8_t>(priority);
        doc["startTime"] = startTime;
        doc["lastActivity"] = lastActivity;
        doc["errorCount"] = errorCount;
        doc["lastError"] = lastError;
        doc["uptime"] = millis() - startTime;
        return doc;
    }
};

// ================================
// 服务基类定义
// ================================

/**
 * 服务基类 - 所有服务的统一基础接口
 * 
 * 设计原则：
 * 1. 统一的生命周期管理
 * 2. 标准化的错误处理
 * 3. 一致的事件发布机制
 * 4. 可扩展的配置管理
 * 5. 完整的状态监控
 */
class BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     * @param serviceName 服务名称
     * @param serviceVersion 服务版本
     * @param servicePriority 服务优先级
     */
    BaseService(EventManager* eventMgr, const String& serviceName, 
                const String& serviceVersion = "1.0.0", 
                ServicePriority servicePriority = ServicePriority::NORMAL);
    
    /**
     * 虚析构函数
     */
    virtual ~BaseService();
    
    // ================================
    // 纯虚函数 - 子类必须实现
    // ================================
    
    /**
     * 初始化服务 - 子类必须实现
     * @return 是否初始化成功
     */
    virtual bool init() = 0;
    
    /**
     * 清理服务资源 - 子类必须实现
     */
    virtual void cleanup() = 0;
    
    /**
     * 获取服务状态 - 子类必须实现
     * @return 服务状态
     */
    virtual ServiceStatus getStatus() const = 0;
    
    // ================================
    // 可选重写的虚函数
    // ================================
    
    /**
     * 服务主循环处理 - 子类可选重写
     */
    virtual void loop() {}
    
    /**
     * 配置变更通知 - 子类可选重写
     * @param config 新配置数据
     */
    virtual void onConfigChanged(const JsonDocument& config) {}
    
    /**
     * 系统重启通知 - 子类可选重写
     */
    virtual void onSystemRestart() {}
    
    /**
     * 系统暂停通知 - 子类可选重写
     */
    virtual void onSystemPause() {}
    
    /**
     * 系统恢复通知 - 子类可选重写
     */
    virtual void onSystemResume() {}
    
    /**
     * 网络状态变化通知 - 子类可选重写
     * @param connected 是否连接
     */
    virtual void onNetworkStateChanged(bool connected) {}
    
    // ================================
    // 服务信息获取
    // ================================
    
    /**
     * 获取服务名称
     * @return 服务名称
     */
    String getServiceName() const { return serviceInfo.name; }
    
    /**
     * 获取服务版本
     * @return 服务版本
     */
    String getServiceVersion() const { return serviceInfo.version; }
    
    /**
     * 获取服务描述
     * @return 服务描述
     */
    String getServiceDescription() const { return serviceInfo.description; }
    
    /**
     * 获取服务优先级
     * @return 服务优先级
     */
    ServicePriority getServicePriority() const { return serviceInfo.priority; }
    
    /**
     * 获取服务信息
     * @return 服务信息结构
     */
    ServiceInfo getServiceInfo() const { return serviceInfo; }
    
    /**
     * 获取服务运行时间
     * @return 运行时间（毫秒）
     */
    uint32_t getUptime() const { 
        return serviceInfo.startTime > 0 ? (millis() - serviceInfo.startTime) : 0; 
    }
    
    // ================================
    // 服务状态管理
    // ================================
    
    /**
     * 检查服务是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return initialized; }
    
    /**
     * 检查服务是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const { return getStatus() == ServiceStatus::RUNNING; }
    
    /**
     * 检查服务是否有错误
     * @return 是否有错误
     */
    bool hasError() const { return getStatus() == ServiceStatus::ERROR; }
    
    /**
     * 获取错误计数
     * @return 错误计数
     */
    uint32_t getErrorCount() const { return serviceInfo.errorCount; }
    
    /**
     * 获取最后错误信息
     * @return 最后错误信息
     */
    String getLastError() const { return serviceInfo.lastError; }
    
    // ================================
    // 统一错误处理
    // ================================
    
    /**
     * 处理服务错误 - 统一错误处理机制
     * @param operation 操作名称
     * @param error 错误信息
     * @param critical 是否为关键错误
     */
    void handleError(const String& operation, const String& error, bool critical = false);
    
    /**
     * 记录警告信息
     * @param operation 操作名称
     * @param warning 警告信息
     */
    void handleWarning(const String& operation, const String& warning);
    
    /**
     * 记录信息日志
     * @param operation 操作名称
     * @param info 信息内容
     */
    void logInfo(const String& operation, const String& info);
    
    /**
     * 记录调试信息
     * @param operation 操作名称
     * @param debug 调试信息
     */
    void logDebug(const String& operation, const String& debug);
    
    // ================================
    // 统一事件发布
    // ================================
    
    /**
     * 发布服务事件 - 统一事件发布机制
     * @param type 事件类型
     * @param data 事件数据
     */
    void emitEvent(EventType type, const JsonDocument& data = JsonDocument());
    
    /**
     * 发布服务状态变化事件
     * @param newStatus 新状态
     */
    void emitStatusChangeEvent(ServiceStatus newStatus);
    
    /**
     * 发布服务错误事件
     * @param operation 操作名称
     * @param error 错误信息
     */
    void emitErrorEvent(const String& operation, const String& error);
    
    // ================================
    // 配置管理
    // ================================
    
    /**
     * 设置服务配置
     * @param config 配置数据
     * @return 是否设置成功
     */
    virtual bool setConfig(const JsonDocument& config);
    
    /**
     * 获取服务配置
     * @return 配置数据
     */
    virtual JsonDocument getConfig() const;
    
    /**
     * 重置服务配置为默认值
     * @return 是否重置成功
     */
    virtual bool resetConfig();
    
    // ================================
    // 性能监控
    // ================================
    
    /**
     * 更新活动时间戳
     */
    void updateActivity() { serviceInfo.lastActivity = millis(); }
    
    /**
     * 获取性能统计
     * @return 性能统计JSON对象
     */
    virtual JsonDocument getPerformanceStats() const;
    
    /**
     * 重置性能统计
     */
    virtual void resetPerformanceStats();

protected:
    // ================================
    // 受保护成员变量
    // ================================
    
    EventManager* eventManager;         // 事件管理器
    ErrorHandler* errorHandler;         // 错误处理器
    ServiceInfo serviceInfo;            // 服务信息
    bool initialized;                   // 初始化标志
    
    // ================================
    // 受保护方法
    // ================================
    
    /**
     * 设置服务状态
     * @param status 新状态
     */
    void setServiceStatus(ServiceStatus status);
    
    /**
     * 设置服务描述
     * @param description 服务描述
     */
    void setServiceDescription(const String& description) { 
        serviceInfo.description = description; 
    }
    
    /**
     * 增加错误计数
     */
    void incrementErrorCount() { serviceInfo.errorCount++; }
    
    /**
     * 检查事件管理器是否可用
     * @return 是否可用
     */
    bool isEventManagerAvailable() const { return eventManager != nullptr; }
    
    /**
     * 检查错误处理器是否可用
     * @return 是否可用
     */
    bool isErrorHandlerAvailable() const { return errorHandler != nullptr; }

private:
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 获取状态字符串
     * @param status 状态枚举
     * @return 状态字符串
     */
    String getStatusString(ServiceStatus status) const;
    
    /**
     * 获取优先级字符串
     * @param priority 优先级枚举
     * @return 优先级字符串
     */
    String getPriorityString(ServicePriority priority) const;
};

// ================================
// 服务工厂宏定义
// ================================

/**
 * 服务注册宏 - 简化服务注册过程
 */
#define REGISTER_SERVICE(ServiceClass, serviceName, serviceVersion, priority) \
    class ServiceClass##Factory { \
    public: \
        static BaseService* create(EventManager* eventMgr) { \
            return new ServiceClass(eventMgr, serviceName, serviceVersion, priority); \
        } \
    };

/**
 * 服务状态检查宏
 */
#define CHECK_SERVICE_INITIALIZED(service) \
    if (!(service) || !(service)->isInitialized()) { \
        handleError(__FUNCTION__, "Service not initialized"); \
        return false; \
    }

/**
 * 服务错误处理宏
 */
#define SERVICE_ERROR_RETURN(condition, operation, error) \
    if (condition) { \
        handleError(operation, error); \
        return false; \
    }

/**
 * 服务警告处理宏
 */
#define SERVICE_WARNING(condition, operation, warning) \
    if (condition) { \
        handleWarning(operation, warning); \
    }

#endif // BASE_SERVICE_H
