/**
 * @file IRAPITypes.h
 * @brief API类型定义 - 避免命名冲突
 * 
 * 避免success/error方法名冲突
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_API_TYPES_H
#define IR_API_TYPES_H

#include <Arduino.h>
#include <ArduinoJson.h>

// API错误代码
enum IRAPIErrorCode {
    IR_API_SUCCESS = 0,
    IR_API_INVALID_PARAMS = 1,
    IR_API_SIGNAL_NOT_FOUND = 2,
    IR_API_STORAGE_FULL = 3,
    IR_API_HARDWARE_ERROR = 4,
    IR_API_TIMEOUT = 5,
    IR_API_LEARNING_FAILED = 6,
    IR_API_EMIT_FAILED = 7,
    IR_API_CONFIG_ERROR = 8,
    IR_API_NETWORK_ERROR = 9,
    IR_API_UNKNOWN_ERROR = 10
};

// API响应结构
struct IRAPIResponse {
    bool success;               // 是否成功
    IRAPIErrorCode errorCode;   // 错误代码
    char message[128];          // 响应消息
    char error[128];            // 错误信息
    uint32_t timestamp;         // 时间戳
    char data[512];             // 响应数据 (JSON字符串)
    
    // 构造函数
    IRAPIResponse() {
        success = false;
        errorCode = IR_API_UNKNOWN_ERROR;
        message[0] = '\0';
        error[0] = '\0';
        timestamp = millis();
        data[0] = '\0';
    }
    
    // 静态方法 - 避免与成员变量冲突
    static IRAPIResponse createSuccess(const char* msg = "操作成功", const char* jsonData = "{}") {
        IRAPIResponse response;
        response.success = true;
        response.errorCode = IR_API_SUCCESS;
        response.timestamp = millis();
        
        strncpy(response.message, msg, 127);
        response.message[127] = '\0';
        
        strncpy(response.data, jsonData, 511);
        response.data[511] = '\0';
        
        return response;
    }
    
    static IRAPIResponse createError(IRAPIErrorCode code, const char* errorMsg, const char* msg = "") {
        IRAPIResponse response;
        response.success = false;
        response.errorCode = code;
        response.timestamp = millis();
        
        strncpy(response.error, errorMsg, 127);
        response.error[127] = '\0';
        
        if (strlen(msg) > 0) {
            strncpy(response.message, msg, 127);
            response.message[127] = '\0';
        }
        
        strcpy(response.data, "{}");
        
        return response;
    }
    
    // 转换为JSON字符串
    String toJsonString() const {
        DynamicJsonDocument doc(1024);
        doc["success"] = success;
        doc["timestamp"] = timestamp;
        
        if (success) {
            doc["message"] = message;
            // 解析data字段为JSON对象
            DynamicJsonDocument dataDoc(512);
            deserializeJson(dataDoc, data);
            doc["data"] = dataDoc.as<JsonObject>();
        } else {
            doc["error"] = error;
            doc["errorCode"] = errorCode;
            if (strlen(message) > 0) {
                doc["message"] = message;
            }
        }
        
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    const char* getErrorCodeString() const {
        switch(errorCode) {
            case IR_API_SUCCESS: return "SUCCESS";
            case IR_API_INVALID_PARAMS: return "INVALID_PARAMS";
            case IR_API_SIGNAL_NOT_FOUND: return "SIGNAL_NOT_FOUND";
            case IR_API_STORAGE_FULL: return "STORAGE_FULL";
            case IR_API_HARDWARE_ERROR: return "HARDWARE_ERROR";
            case IR_API_TIMEOUT: return "TIMEOUT";
            case IR_API_LEARNING_FAILED: return "LEARNING_FAILED";
            case IR_API_EMIT_FAILED: return "EMIT_FAILED";
            case IR_API_CONFIG_ERROR: return "CONFIG_ERROR";
            case IR_API_NETWORK_ERROR: return "NETWORK_ERROR";
            default: return "UNKNOWN_ERROR";
        }
    }
};

// 学习控制请求
struct IRLearningRequest {
    char command[16];           // start/stop/status
    uint32_t timeout;           // 超时时间
    char signalName[32];        // 信号名称
    char signalType[16];        // 信号类型
    
    IRLearningRequest() {
        memset(this, 0, sizeof(IRLearningRequest));
        timeout = 30000;  // 默认30秒
    }
};

// 信号发射请求
struct IREmitRequest {
    char signalId[16];          // 信号ID
    uint16_t repeatCount;       // 重复次数
    uint16_t interval;          // 间隔时间
    
    IREmitRequest() {
        memset(this, 0, sizeof(IREmitRequest));
        repeatCount = 1;
        interval = 100;
    }
};

// 批量请求
struct IRBatchRequest {
    char signalIds[256];        // 信号ID列表，逗号分隔
    uint16_t repeatCount;       // 重复次数
    uint16_t interval;          // 间隔时间
    uint16_t batchInterval;     // 批次间隔
    
    IRBatchRequest() {
        memset(this, 0, sizeof(IRBatchRequest));
        repeatCount = 1;
        interval = 100;
        batchInterval = 1000;
    }
};

#endif
