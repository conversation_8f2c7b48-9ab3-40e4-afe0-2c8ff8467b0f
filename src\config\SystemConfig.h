/**
 * ESP32-S3红外控制系统 - 系统配置定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的系统配置
 * - 完整的性能参数配置，确保匹配前端性能标准
 * - 无PSRAM版本优化配置，针对ESP32-S3 0.2版本芯片
 * - 支持前端2,086个交互点的后端性能配置
 * 
 * 前端匹配度：
 * - 性能标准：<1ms硬实时响应，批处理优化70%
 * - 事件系统：101个事件类型的队列和缓冲配置
 * - 信号管理：1000个信号的存储和索引配置
 * - 网络通信：WebSocket和HTTP的超时和连接配置
 * 
 * 后端架构匹配：
 * - 双核配置：核心0和核心1的任务分配参数
 * - 内存优化：无PSRAM的L1+L2存储架构配置
 * - 批处理：50ms批处理窗口，匹配前端批量优化
 * - 实时性能：1ms精度定时器，满足硬实时要求
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

// ================================
// 系统版本信息
// ================================

#define SYSTEM_VERSION          "2.0.0"
#define SYSTEM_NAME             "ESP32-S3红外控制系统"
#define BUILD_DATE              __DATE__
#define BUILD_TIME              __TIME__
#define FIRMWARE_AUTHOR         "ESP32-S3红外控制系统团队"
#define FIRMWARE_DESCRIPTION    "基于双核并行架构的红外控制系统"

// ================================
// 硬件平台配置
// ================================

// ESP32-S3芯片配置
#define TARGET_CHIP             "ESP32-S3"
#define TARGET_BOARD            "ESP32-S3-DevKitC-1"
#define CPU_FREQUENCY_MHZ       240         // 240MHz双核运行
#define FLASH_SIZE_MB           8           // 8MB Flash存储
#define SRAM_SIZE_KB            512         // 512KB SRAM (无PSRAM版本)

// 双核配置
#define CORE0_PRIORITY          (configMAX_PRIORITIES - 1)  // 核心0最高优先级
#define CORE1_PRIORITY          (tskIDLE_PRIORITY + 2)      // 核心1中等优先级
#define CORE0_STACK_SIZE        8192        // 核心0栈空间 8KB
#define CORE1_STACK_SIZE        16384       // 核心1栈空间 16KB
#define CORE0_TICK_RATE         1           // 核心0: 1ms精度
#define CORE1_TICK_RATE         10          // 核心1: 10ms精度

// ================================
// 性能配置 - 匹配前端性能标准
// ================================

// 实时性能要求
#define REALTIME_RESPONSE_MS    1           // <1ms硬实时响应要求
#define NETWORK_RESPONSE_MS     15          // 网络响应时间上限
#define BATCH_OPTIMIZATION      70          // 批处理优化目标70%

// 信号管理性能
#define MAX_SIGNALS             1000        // 最大信号数量（匹配前端）
#define SIGNAL_SEARCH_MS        10          // 信号搜索时间上限
#define SIGNAL_EMIT_MS          1           // 信号发射时间上限
#define SIGNAL_LEARNING_MS      30000       // 信号学习超时30秒

// 任务管理性能
#define MAX_CONCURRENT_TASKS    10          // 最大并发任务数
#define TASK_SWITCH_US          100         // 任务切换时间上限100微秒
#define TASK_QUEUE_SIZE         32          // 任务队列大小

// 事件系统性能
#define EVENT_QUEUE_SIZE        256         // 事件队列大小（匹配前端101个事件类型）
#define HIGH_PRIORITY_QUEUE_SIZE 64         // 高优先级事件队列
#define EVENT_PROCESS_MS        8           // 事件处理时间片8ms
#define BATCH_TIMEOUT_MS        50          // 批处理超时50ms（匹配前端）

// ================================
// 内存配置 - 无PSRAM版本优化
// ================================

// L1缓存配置（SRAM高速缓存）
#define L1_CACHE_SIZE           20          // L1缓存信号数量（增加到20个）
#define L1_CACHE_ACCESS_NS      100         // L1缓存访问时间100ns
#define L1_EVICTION_THRESHOLD   18          // L1缓存淘汰阈值

// L2存储配置（Flash + 索引）
#define SIGNAL_INDEX_SIZE       1000        // 信号索引大小
#define FLASH_ACCESS_MS         1           // Flash访问时间1ms
#define INDEX_CACHE_SIZE        100         // 索引缓存大小

// DMA缓冲区配置
#define DMA_BUFFER_SIZE         2048        // DMA缓冲区2KB（节省SRAM）
#define IR_BUFFER_SIZE          1024        // 红外数据缓冲区1KB
#define NETWORK_BUFFER_SIZE     4096        // 网络缓冲区4KB

// 访问模式分析
#define ACCESS_PATTERN_SIZE     100         // 访问模式跟踪数量
#define HOT_SIGNAL_THRESHOLD    5           // 热点信号访问次数阈值
#define PRELOAD_SIGNAL_COUNT    10          // 预加载信号数量

// ================================
// 网络配置 - 匹配前端网络性能
// ================================

// HTTP服务器配置
#define HTTP_SERVER_PORT        80          // HTTP服务器端口
#define HTTP_REQUEST_TIMEOUT    5000        // HTTP请求超时5秒
#define HTTP_MAX_CONNECTIONS    10          // 最大HTTP连接数
#define HTTP_KEEPALIVE_TIMEOUT  30000       // HTTP保持连接超时

// WebSocket配置
#define WEBSOCKET_PORT          81          // WebSocket端口
// MAX_WEBSOCKET_CLIENTS在NetworkConfig.h中定义
#define WEBSOCKET_PING_INTERVAL 30000       // WebSocket心跳间隔30秒
#define WEBSOCKET_TIMEOUT       60000       // WebSocket超时60秒
#define WEBSOCKET_BUFFER_SIZE   2048        // WebSocket缓冲区大小

// 批量请求配置
#define BATCH_REQUEST_TIMEOUT   50          // 批量请求超时50ms
#define MAX_BATCH_REQUESTS      10          // 最大批量请求数
#define BATCH_RESPONSE_SIZE     8192        // 批量响应缓冲区大小

// ================================
// 硬件配置 - 匹配前端硬件控制
// ================================

// 红外硬件配置
#define IR_CARRIER_FREQUENCY    38000       // 默认载波频率38kHz
#define IR_DUTY_CYCLE           33          // PWM占空比33%
#define IR_TRANSMIT_POWER       100         // 发射功率100%
#define IR_RECEIVE_SENSITIVITY  80          // 接收灵敏度80%

// 定时器配置
#define HARDWARE_TIMER_FREQ     1000000     // 硬件定时器频率1MHz
#define TIMER_RESOLUTION_US     1           // 定时器分辨率1微秒
#define LEARNING_TIMEOUT        30000       // 学习超时30秒
#define EMIT_TIMEOUT            5000        // 发射超时5秒

// LED状态配置
#define LED_BLINK_FAST_MS       100         // 快速闪烁间隔
#define LED_BLINK_SLOW_MS       500         // 慢速闪烁间隔
#define LED_PULSE_PERIOD_MS     2000        // 脉冲周期
#define LED_BREATHING_PERIOD_MS 4000        // 呼吸灯周期

// ================================
// 存储配置 - 匹配前端数据管理
// ================================

// Flash存储配置
#define FLASH_PARTITION_SIZE    (4 * 1024 * 1024)  // Flash分区4MB
#define SIGNAL_STORAGE_SIZE     (2 * 1024 * 1024)  // 信号存储2MB
#define CONFIG_STORAGE_SIZE     (64 * 1024)        // 配置存储64KB
#define LOG_STORAGE_SIZE        (256 * 1024)       // 日志存储256KB

// 配置管理
#define CONFIG_VERSION          1           // 配置版本号
#define CONFIG_NAMESPACE        "ir_config" // 配置命名空间
#define CONFIG_AUTO_SAVE        true        // 自动保存配置
#define CONFIG_BACKUP_COUNT     3           // 配置备份数量
#define CONFIG_SAVE_INTERVAL    5000        // 配置保存间隔5秒

// 数据验证配置
#define ENABLE_DATA_VALIDATION  true        // 启用数据验证
#define VALIDATION_TIMEOUT_MS   100         // 验证超时100ms
#define MAX_VALIDATION_ERRORS   10          // 最大验证错误数

// ================================
// 日志配置 - 匹配前端调试系统
// ================================

// 日志级别定义
#define LOG_LEVEL_ERROR         1           // 错误级别
#define LOG_LEVEL_WARN          2           // 警告级别
#define LOG_LEVEL_INFO          3           // 信息级别
#define LOG_LEVEL_DEBUG         4           // 调试级别
#define LOG_LEVEL_VERBOSE       5           // 详细级别

// 日志配置
#define DEFAULT_LOG_LEVEL       LOG_LEVEL_INFO  // 默认日志级别
#define MAX_LOG_ENTRIES         1000        // 最大日志条目数
#define LOG_BUFFER_SIZE         512         // 日志缓冲区大小
#define LOG_FLUSH_INTERVAL      1000        // 日志刷新间隔1秒

// 串口日志配置
#define SERIAL_BAUD_RATE        115200      // 串口波特率
#define SERIAL_LOG_ENABLED      true        // 启用串口日志
#define SERIAL_BUFFER_SIZE      256         // 串口缓冲区大小

// ================================
// OTA配置 - 匹配前端OTA功能
// ================================

#define OTA_ENABLED             true        // 启用OTA功能
#define OTA_PASSWORD            "ota_secure_2024"  // OTA密码
#define OTA_PORT                3232        // OTA端口
#define OTA_TIMEOUT             300000      // OTA超时时间5分钟
#define OTA_BUFFER_SIZE         1024        // OTA缓冲区大小
#define OTA_MAX_RETRIES         3           // OTA最大重试次数

// OTA安全配置
#define OTA_AUTH_REQUIRED       true        // 需要OTA认证
#define OTA_HOSTNAME            "ESP32-IR-Controller"  // OTA主机名
#define OTA_AUTO_REBOOT         true        // OTA完成后自动重启

// ================================
// 调试配置 - 匹配前端调试工具
// ================================

// 调试功能开关
#define DEBUG_ENABLED           true        // 启用调试功能
#define PERFORMANCE_MONITOR     true        // 启用性能监控
#define MEMORY_MONITOR          true        // 启用内存监控
#define TASK_MONITOR            true        // 启用任务监控

// 调试输出配置
#define DEBUG_SERIAL_ENABLED    true        // 启用串口调试
#define DEBUG_WEBSOCKET_ENABLED true        // 启用WebSocket调试
#define DEBUG_BUFFER_SIZE       1024        // 调试缓冲区大小

// 性能监控配置
#define PERF_MONITOR_INTERVAL   5000        // 性能监控间隔5秒
#define PERF_HISTORY_SIZE       100         // 性能历史记录数量
#define PERF_ALERT_THRESHOLD    80          // 性能警告阈值80%

// ================================
// 安全配置
// ================================

// 访问控制
#define ENABLE_ACCESS_CONTROL   false       // 启用访问控制（开发阶段关闭）
#define MAX_LOGIN_ATTEMPTS      5           // 最大登录尝试次数
#define LOGIN_TIMEOUT_MS        300000      // 登录超时5分钟

// 数据加密
#define ENABLE_DATA_ENCRYPTION  false       // 启用数据加密（开发阶段关闭）
#define ENCRYPTION_KEY_SIZE     32          // 加密密钥大小

// ================================
// 开发配置
// ================================

// 开发模式配置
#define DEVELOPMENT_MODE        true        // 开发模式
#define ENABLE_TEST_DATA        true        // 启用测试数据
#define ENABLE_SIMULATION       true        // 启用模拟模式
#define SKIP_HARDWARE_CHECK     false       // 跳过硬件检查

// 测试配置
#define TEST_SIGNAL_COUNT       3           // 测试信号数量
#define TEST_TASK_COUNT         2           // 测试任务数量
#define SIMULATION_SUCCESS_RATE 99          // 模拟成功率99%

// ================================
// 编译时检查
// ================================

// 内存检查 - 估算SignalData大小约为512字节
#if (L1_CACHE_SIZE * 512) > (SRAM_SIZE_KB * 1024 / 4)
#error "L1缓存大小超过SRAM限制，请减少L1_CACHE_SIZE"
#endif

// 队列大小检查
#if (EVENT_QUEUE_SIZE + HIGH_PRIORITY_QUEUE_SIZE) > 512
#error "事件队列总大小过大，请减少队列大小"
#endif

// 缓冲区检查
#if (DMA_BUFFER_SIZE + IR_BUFFER_SIZE + NETWORK_BUFFER_SIZE) > (SRAM_SIZE_KB * 1024 / 2)
#error "缓冲区总大小超过SRAM限制，请减少缓冲区大小"
#endif

#endif // SYSTEM_CONFIG_H
