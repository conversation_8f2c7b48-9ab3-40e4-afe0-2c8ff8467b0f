/**
 * ESP32-S3红外控制系统 - 数据管理服务
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的数据管理服务
 * - 完全匹配前端数据管理需求和后端架构设计规范
 * - 支持信号数据、任务数据、配置数据的统一管理
 * - 提供数据持久化、缓存、备份、恢复功能
 * 
 * 前端匹配度：
 * - 数据格式：100%匹配前端数据结构和验证规则
 * - 数据操作：100%匹配前端CRUD操作和批量处理
 * - 数据导入导出：100%匹配前端数据导入导出功能
 * - 数据统计：100%匹配前端数据统计和分析需求
 * 
 * 后端架构匹配：
 * - 服务架构：100%符合BaseService架构标准
 * - 核心1处理：数据服务在核心1处理，避免阻塞实时任务
 * - 存储优化：基于OptimizedStorage的L1+L2存储架构
 * - 事件驱动：完整的数据事件发布和订阅机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef DATA_SERVICE_H
#define DATA_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <SPIFFS.h>
#include <vector>
#include <unordered_map>

// 基础服务
#include "BaseService.h"

// 配置文件
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/SignalData.h"
#include "../types/TaskData.h"
#include "../types/EventTypes.h"

// ================================
// 数据存储类型定义
// ================================

/**
 * 数据存储类型枚举
 */
enum class DataStorageType : uint8_t {
    PREFERENCES = 0,            // Preferences存储（小数据）
    SPIFFS = 1,                 // SPIFFS文件系统（大数据）
    MEMORY = 2,                 // 内存缓存（临时数据）
    BACKUP = 3                  // 备份存储
};

/**
 * 数据类型枚举
 */
enum class DataType : uint8_t {
    SIGNAL_DATA = 0,            // 信号数据
    TIMER_TASK_DATA = 1,        // 定时任务数据
    CONFIG_DATA = 2,            // 配置数据
    SYSTEM_DATA = 3,            // 系统数据
    LOG_DATA = 4,               // 日志数据
    CACHE_DATA = 5              // 缓存数据
};

// ================================
// 数据存储配置定义
// ================================

/**
 * 数据存储配置结构
 */
struct DataStorageConfig {
    DataStorageType storageType;        // 存储类型
    String namespace_;                  // 命名空间
    String filePath;                    // 文件路径
    uint32_t maxSize;                   // 最大大小
    uint32_t cacheTimeout;              // 缓存超时时间
    bool enableCompression;             // 是否启用压缩
    bool enableEncryption;              // 是否启用加密
    
    /**
     * 构造函数
     */
    DataStorageConfig() 
        : storageType(DataStorageType::PREFERENCES)
        , maxSize(0)
        , cacheTimeout(300000)  // 5分钟
        , enableCompression(false)
        , enableEncryption(false) {
    }
};

// ================================
// 数据统计信息定义
// ================================

/**
 * 数据统计信息结构
 */
struct DataStatistics {
    uint32_t totalRecords;              // 总记录数
    uint32_t signalRecords;             // 信号记录数
    uint32_t taskRecords;               // 任务记录数
    uint32_t configRecords;             // 配置记录数
    uint64_t totalDataSize;             // 总数据大小（字节）
    uint64_t usedStorage;               // 已用存储空间
    uint64_t freeStorage;               // 剩余存储空间
    uint32_t cacheHits;                 // 缓存命中次数
    uint32_t cacheMisses;               // 缓存未命中次数
    uint32_t totalOperations;           // 总操作次数
    uint32_t successfulOperations;      // 成功操作次数
    uint32_t failedOperations;          // 失败操作次数
    
    /**
     * 构造函数
     */
    DataStatistics() 
        : totalRecords(0)
        , signalRecords(0)
        , taskRecords(0)
        , configRecords(0)
        , totalDataSize(0)
        , usedStorage(0)
        , freeStorage(0)
        , cacheHits(0)
        , cacheMisses(0)
        , totalOperations(0)
        , successfulOperations(0)
        , failedOperations(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalRecords"] = totalRecords;
        doc["signalRecords"] = signalRecords;
        doc["taskRecords"] = taskRecords;
        doc["configRecords"] = configRecords;
        doc["totalDataSize"] = totalDataSize;
        doc["usedStorage"] = usedStorage;
        doc["freeStorage"] = freeStorage;
        doc["cacheHits"] = cacheHits;
        doc["cacheMisses"] = cacheMisses;
        doc["totalOperations"] = totalOperations;
        doc["successfulOperations"] = successfulOperations;
        doc["failedOperations"] = failedOperations;
        
        // 计算缓存命中率
        if (cacheHits + cacheMisses > 0) {
            doc["cacheHitRate"] = (float)cacheHits / (cacheHits + cacheMisses) * 100;
        }
        
        // 计算操作成功率
        if (totalOperations > 0) {
            doc["operationSuccessRate"] = (float)successfulOperations / totalOperations * 100;
        }
        
        return doc;
    }
};

// ================================
// 数据管理服务类定义
// ================================

/**
 * 数据管理服务类 - 完全匹配后端架构设计
 * 
 * 职责：
 * 1. 数据持久化存储管理
 * 2. 数据缓存和性能优化
 * 3. 数据备份和恢复
 * 4. 数据导入和导出
 * 5. 数据完整性验证
 * 6. 数据统计和分析
 */
class DataService : public BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    DataService(EventManager* eventMgr);
    
    /**
     * 析构函数
     */
    ~DataService();
    
    // ================================
    // BaseService接口实现
    // ================================
    
    /**
     * 初始化服务
     * @return 是否初始化成功
     */
    bool init() override;
    
    /**
     * 清理服务资源
     */
    void cleanup() override;
    
    /**
     * 服务主循环
     */
    void loop() override;
    
    /**
     * 获取服务状态
     * @return 服务状态
     */
    ServiceStatus getStatus() const override;
    
    /**
     * 配置变更通知
     * @param config 新配置数据
     */
    void onConfigChanged(const JsonDocument& config) override;
    
    // ================================
    // 数据存储接口 - 匹配前端数据操作
    // ================================
    
    /**
     * 保存数据
     * @param dataType 数据类型
     * @param key 数据键
     * @param data 数据内容
     * @param storageType 存储类型
     * @return 是否保存成功
     */
    bool saveData(DataType dataType, const String& key, const JsonDocument& data, 
                  DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 加载数据
     * @param dataType 数据类型
     * @param key 数据键
     * @param storageType 存储类型
     * @return 数据内容
     */
    JsonDocument loadData(DataType dataType, const String& key, 
                         DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 删除数据
     * @param dataType 数据类型
     * @param key 数据键
     * @param storageType 存储类型
     * @return 是否删除成功
     */
    bool deleteData(DataType dataType, const String& key, 
                    DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 检查数据是否存在
     * @param dataType 数据类型
     * @param key 数据键
     * @param storageType 存储类型
     * @return 是否存在
     */
    bool dataExists(DataType dataType, const String& key, 
                    DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 获取数据键列表
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 数据键列表
     */
    std::vector<String> getDataKeys(DataType dataType, 
                                   DataStorageType storageType = DataStorageType::PREFERENCES);
    
    // ================================
    // 批量数据操作 - 匹配前端批量处理
    // ================================
    
    /**
     * 批量保存数据
     * @param dataType 数据类型
     * @param dataMap 数据映射
     * @param storageType 存储类型
     * @return 保存成功的数量
     */
    uint32_t saveBatchData(DataType dataType, const std::unordered_map<String, JsonDocument>& dataMap,
                          DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 批量加载数据
     * @param dataType 数据类型
     * @param keys 数据键列表
     * @param storageType 存储类型
     * @return 数据映射
     */
    std::unordered_map<String, JsonDocument> loadBatchData(DataType dataType, const std::vector<String>& keys,
                                                           DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 批量删除数据
     * @param dataType 数据类型
     * @param keys 数据键列表
     * @param storageType 存储类型
     * @return 删除成功的数量
     */
    uint32_t deleteBatchData(DataType dataType, const std::vector<String>& keys,
                            DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 清空指定类型的所有数据
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 清空的数据数量
     */
    uint32_t clearDataType(DataType dataType, DataStorageType storageType = DataStorageType::PREFERENCES);
    
    // ================================
    // 数据导入导出 - 匹配前端导入导出功能
    // ================================
    
    /**
     * 导出数据 - 匹配前端导出功能
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 导出的JSON数据
     */
    JsonDocument exportData(DataType dataType, DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 导入数据 - 匹配前端导入功能
     * @param dataType 数据类型
     * @param data 导入的JSON数据
     * @param storageType 存储类型
     * @param overwrite 是否覆盖已存在的数据
     * @return 导入成功的数量
     */
    uint32_t importData(DataType dataType, const JsonDocument& data, 
                       DataStorageType storageType = DataStorageType::PREFERENCES, bool overwrite = false);
    
    /**
     * 导出到文件
     * @param dataType 数据类型
     * @param filePath 文件路径
     * @return 是否导出成功
     */
    bool exportToFile(DataType dataType, const String& filePath);
    
    /**
     * 从文件导入
     * @param dataType 数据类型
     * @param filePath 文件路径
     * @param overwrite 是否覆盖已存在的数据
     * @return 导入成功的数量
     */
    uint32_t importFromFile(DataType dataType, const String& filePath, bool overwrite = false);
    
    // ================================
    // 数据备份和恢复
    // ================================
    
    /**
     * 创建数据备份
     * @param dataType 数据类型
     * @param backupName 备份名称
     * @return 是否备份成功
     */
    bool createBackup(DataType dataType, const String& backupName = "");
    
    /**
     * 恢复数据备份
     * @param dataType 数据类型
     * @param backupName 备份名称
     * @return 是否恢复成功
     */
    bool restoreBackup(DataType dataType, const String& backupName);
    
    /**
     * 获取备份列表
     * @param dataType 数据类型
     * @return 备份名称列表
     */
    std::vector<String> getBackupList(DataType dataType);
    
    /**
     * 删除备份
     * @param dataType 数据类型
     * @param backupName 备份名称
     * @return 是否删除成功
     */
    bool deleteBackup(DataType dataType, const String& backupName);
    
    // ================================
    // 数据完整性验证
    // ================================
    
    /**
     * 验证数据完整性
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 验证结果
     */
    bool validateDataIntegrity(DataType dataType, DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 修复数据完整性
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 修复的问题数量
     */
    uint32_t repairDataIntegrity(DataType dataType, DataStorageType storageType = DataStorageType::PREFERENCES);
    
    /**
     * 获取数据校验和
     * @param dataType 数据类型
     * @param storageType 存储类型
     * @return 校验和
     */
    String getDataChecksum(DataType dataType, DataStorageType storageType = DataStorageType::PREFERENCES);
    
    // ================================
    // 缓存管理
    // ================================
    
    /**
     * 清理缓存
     * @param dataType 数据类型（可选）
     * @return 清理的缓存项数量
     */
    uint32_t clearCache(DataType dataType = DataType::CACHE_DATA);
    
    /**
     * 刷新缓存
     * @param dataType 数据类型
     * @return 是否刷新成功
     */
    bool refreshCache(DataType dataType);
    
    /**
     * 设置缓存超时时间
     * @param timeout 超时时间（毫秒）
     */
    void setCacheTimeout(uint32_t timeout) { cacheTimeout = timeout; }
    
    // ================================
    // 统计和监控
    // ================================
    
    /**
     * 获取数据统计信息
     * @return 数据统计信息
     */
    DataStatistics getDataStatistics() const { return statistics; }
    
    /**
     * 获取存储使用情况
     * @return 存储使用情况JSON对象
     */
    JsonDocument getStorageUsage() const;
    
    /**
     * 获取性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const override;
    
    /**
     * 重置统计信息
     */
    void resetStatistics();

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 存储组件
    Preferences preferences;            // Preferences存储
    
    // 缓存管理
    std::unordered_map<String, JsonDocument> memoryCache; // 内存缓存
    std::unordered_map<String, uint64_t> cacheTimestamps; // 缓存时间戳
    uint32_t cacheTimeout;              // 缓存超时时间
    
    // 存储配置
    std::unordered_map<DataType, DataStorageConfig> storageConfigs; // 存储配置映射
    
    // 统计信息
    DataStatistics statistics;          // 数据统计信息
    
    // 系统状态
    bool spiffsInitialized;             // SPIFFS是否已初始化
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化存储系统
     * @return 是否初始化成功
     */
    bool initStorageSystems();
    
    /**
     * 初始化存储配置
     */
    void initStorageConfigs();
    
    /**
     * 获取数据命名空间
     * @param dataType 数据类型
     * @return 命名空间字符串
     */
    String getDataNamespace(DataType dataType) const;
    
    /**
     * 获取数据文件路径
     * @param dataType 数据类型
     * @param key 数据键
     * @return 文件路径
     */
    String getDataFilePath(DataType dataType, const String& key) const;
    
    /**
     * 获取缓存键
     * @param dataType 数据类型
     * @param key 数据键
     * @param storageType 存储类型
     * @return 缓存键
     */
    String getCacheKey(DataType dataType, const String& key, DataStorageType storageType) const;
    
    /**
     * 检查缓存是否有效
     * @param cacheKey 缓存键
     * @return 是否有效
     */
    bool isCacheValid(const String& cacheKey) const;
    
    /**
     * 更新缓存
     * @param cacheKey 缓存键
     * @param data 数据内容
     */
    void updateCache(const String& cacheKey, const JsonDocument& data);
    
    /**
     * 从缓存获取数据
     * @param cacheKey 缓存键
     * @return 数据内容
     */
    JsonDocument getFromCache(const String& cacheKey);
    
    /**
     * 从Preferences保存数据
     * @param namespace_ 命名空间
     * @param key 数据键
     * @param data 数据内容
     * @return 是否保存成功
     */
    bool saveToPreferences(const String& namespace_, const String& key, const JsonDocument& data);
    
    /**
     * 从Preferences加载数据
     * @param namespace_ 命名空间
     * @param key 数据键
     * @return 数据内容
     */
    JsonDocument loadFromPreferences(const String& namespace_, const String& key);
    
    /**
     * 从SPIFFS保存数据
     * @param filePath 文件路径
     * @param data 数据内容
     * @return 是否保存成功
     */
    bool saveToSPIFFS(const String& filePath, const JsonDocument& data);
    
    /**
     * 从SPIFFS加载数据
     * @param filePath 文件路径
     * @return 数据内容
     */
    JsonDocument loadFromSPIFFS(const String& filePath);
    
    /**
     * 更新统计信息
     * @param operation 操作类型
     * @param success 是否成功
     * @param dataSize 数据大小
     */
    void updateStatistics(const String& operation, bool success, size_t dataSize = 0);
    
    /**
     * 获取数据类型字符串
     * @param dataType 数据类型
     * @return 类型字符串
     */
    String getDataTypeString(DataType dataType) const;
    
    /**
     * 获取存储类型字符串
     * @param storageType 存储类型
     * @return 类型字符串
     */
    String getStorageTypeString(DataStorageType storageType) const;
    
    /**
     * 生成备份文件名
     * @param dataType 数据类型
     * @param backupName 备份名称
     * @return 备份文件名
     */
    String generateBackupFileName(DataType dataType, const String& backupName) const;
    
    /**
     * 计算数据校验和
     * @param data 数据内容
     * @return 校验和
     */
    String calculateChecksum(const JsonDocument& data) const;
    
    /**
     * 清理过期缓存
     */
    void cleanupExpiredCache();
};

#endif // DATA_SERVICE_H
