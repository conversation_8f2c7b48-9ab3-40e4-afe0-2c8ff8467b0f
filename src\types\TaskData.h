/**
 * ESP32-S3红外控制系统 - 任务数据结构定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档的TaskData结构100%匹配实现
 * - 基于后端架构设计的双核并行任务调度数据结构
 * - 完整的10个字段定义，确保前后端任务数据格式完全一致
 * - 支持任务优先级管理，匹配前端控制模块的任务优先级系统
 * 
 * 前端匹配度：
 * - 数据字段：10个字段100%匹配前端TaskData定义
 * - 任务状态：5种状态完全匹配前端任务状态管理
 * - ID格式：task_12345678格式，匹配前端ID生成规则
 * - 优先级系统：1-4级优先级，匹配前端TASK_PRIORITIES定义
 * 
 * 后端架构匹配：
 * - 双核调度：支持核心0和核心1的任务分配
 * - 优先级管理：高优先级任务可抢占低优先级任务
 * - 状态同步：支持实时状态更新和WebSocket广播
 * - 配置管理：灵活的JSON配置系统
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef TASK_DATA_H
#define TASK_DATA_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>

// ================================
// 任务状态枚举定义
// ================================

/**
 * 任务状态枚举 - 完全匹配前端任务状态定义
 * 
 * 基于前端分析，任务状态包含5种状态：
 * - pending: 等待执行
 * - running: 正在执行
 * - paused: 已暂停
 * - completed: 已完成
 * - failed: 执行失败
 */
enum class TaskStatus : uint8_t {
    PENDING = 0,    // 等待执行
    RUNNING = 1,    // 正在执行
    PAUSED = 2,     // 已暂停
    COMPLETED = 3,  // 已完成
    FAILED = 4      // 执行失败
};

/**
 * 任务类型枚举 - 基于前端控制模块分析
 */
enum class TaskType : uint8_t {
    SIGNAL_EMIT = 0,        // 信号发射任务
    SIGNAL_LEARNING = 1,    // 信号学习任务
    TIMER_TASK = 2,         // 定时任务
    BATCH_EMIT = 3,         // 批量发射任务
    SYSTEM_TASK = 4         // 系统任务
};

/**
 * 任务优先级定义 - 匹配前端TASK_PRIORITIES
 */
enum class TaskPriority : uint8_t {
    LOW = 1,            // 全部信号发射 - 最低优先级
    MEDIUM = 2,         // 选中信号发射 - 中等优先级
    HIGH = 3,           // 定时任务 - 中等优先级
    CRITICAL = 4        // 信号学习 - 最高优先级
};

// ================================
// 任务数据结构定义
// ================================

/**
 * 任务数据结构 - 100%匹配前端TaskData格式
 * 
 * 基于前端完整数据文档分析，前端TaskData包含10个核心字段：
 * 1. id: task_12345678格式的唯一标识符
 * 2. name: 任务名称，用户可编辑
 * 3. type: 任务类型，系统定义
 * 4. priority: 优先级1-4，数字越大优先级越高
 * 5. status: 任务状态，5种状态枚举
 * 6. signals: 信号ID数组，任务要处理的信号列表
 * 7. config: 任务配置，JSON对象格式
 * 8. created: 创建时间，13位毫秒时间戳
 * 9. started: 开始时间，13位毫秒时间戳
 * 10. completed: 完成时间，13位毫秒时间戳
 */
struct TaskData {
    // ================================
    // 核心字段 - 完全匹配前端10个字段
    // ================================
    
    String id;                      // 任务ID - task_12345678格式
    String name;                    // 任务名称 - 用户可编辑，1-64字符
    String type;                    // 任务类型 - 字符串格式，匹配前端
    uint8_t priority;               // 优先级 - 1-4，数字越大优先级越高
    TaskStatus status;              // 任务状态 - 5种状态枚举
    std::vector<String> signals;    // 信号ID数组 - 任务要处理的信号列表
    JsonDocument config;            // 任务配置 - JSON对象格式
    uint64_t created;               // 创建时间 - 13位毫秒时间戳
    uint64_t started;               // 开始时间 - 13位毫秒时间戳
    uint64_t completed;             // 完成时间 - 13位毫秒时间戳
    
    // ================================
    // 内部处理字段 - 后端优化使用
    // ================================
    
    TaskType taskType;              // 任务类型枚举 - 内部处理使用
    uint8_t coreId;                 // 分配的核心ID - 0或1
    uint32_t executionTime;         // 执行耗时 - 毫秒
    uint8_t retryCount;             // 重试次数 - 失败重试计数
    String errorMessage;            // 错误信息 - 失败时的详细信息
    
    // ================================
    // 构造函数
    // ================================
    
    /**
     * 默认构造函数
     */
    TaskData() 
        : priority(1)
        , status(TaskStatus::PENDING)
        , created(0)
        , started(0)
        , completed(0)
        , taskType(TaskType::SIGNAL_EMIT)
        , coreId(1)
        , executionTime(0)
        , retryCount(0) {
    }
    
    /**
     * 完整构造函数
     */
    TaskData(const String& id, const String& name, const String& type,
             uint8_t priority, const std::vector<String>& signals)
        : id(id)
        , name(name)
        , type(type)
        , priority(priority)
        , status(TaskStatus::PENDING)
        , signals(signals)
        , created(millis())
        , started(0)
        , completed(0)
        , taskType(TaskType::SIGNAL_EMIT)
        , coreId(1)
        , executionTime(0)
        , retryCount(0) {
    }
    
    // ================================
    // 辅助方法
    // ================================
    
    /**
     * 检查任务数据是否有效
     * @return 是否有效
     */
    bool isValid() const {
        return !id.isEmpty() && 
               !name.isEmpty() && 
               !type.isEmpty() &&
               priority >= 1 && priority <= 4 &&
               created > 0;
    }
    
    /**
     * 检查任务是否正在执行
     * @return 是否正在执行
     */
    bool isRunning() const {
        return status == TaskStatus::RUNNING;
    }
    
    /**
     * 检查任务是否已完成
     * @return 是否已完成
     */
    bool isCompleted() const {
        return status == TaskStatus::COMPLETED || status == TaskStatus::FAILED;
    }
    
    /**
     * 检查任务是否可以执行
     * @return 是否可以执行
     */
    bool canExecute() const {
        return status == TaskStatus::PENDING || status == TaskStatus::PAUSED;
    }
    
    /**
     * 开始执行任务
     */
    void start() {
        if (canExecute()) {
            status = TaskStatus::RUNNING;
            started = millis();
        }
    }
    
    /**
     * 暂停任务
     */
    void pause() {
        if (status == TaskStatus::RUNNING) {
            status = TaskStatus::PAUSED;
        }
    }
    
    /**
     * 恢复任务
     */
    void resume() {
        if (status == TaskStatus::PAUSED) {
            status = TaskStatus::RUNNING;
        }
    }
    
    /**
     * 完成任务
     */
    void complete() {
        if (status == TaskStatus::RUNNING) {
            status = TaskStatus::COMPLETED;
            completed = millis();
            executionTime = completed - started;
        }
    }
    
    /**
     * 任务失败
     * @param error 错误信息
     */
    void fail(const String& error) {
        status = TaskStatus::FAILED;
        completed = millis();
        errorMessage = error;
        if (started > 0) {
            executionTime = completed - started;
        }
    }
    
    /**
     * 获取任务状态字符串 - 匹配前端状态字符串
     * @return 状态字符串
     */
    String getStatusString() const {
        switch (status) {
            case TaskStatus::PENDING: return "pending";
            case TaskStatus::RUNNING: return "running";
            case TaskStatus::PAUSED: return "paused";
            case TaskStatus::COMPLETED: return "completed";
            case TaskStatus::FAILED: return "failed";
            default: return "unknown";
        }
    }
    
    /**
     * 从状态字符串设置状态
     * @param statusStr 状态字符串
     */
    void setStatusFromString(const String& statusStr) {
        if (statusStr == "pending") status = TaskStatus::PENDING;
        else if (statusStr == "running") status = TaskStatus::RUNNING;
        else if (statusStr == "paused") status = TaskStatus::PAUSED;
        else if (statusStr == "completed") status = TaskStatus::COMPLETED;
        else if (statusStr == "failed") status = TaskStatus::FAILED;
    }
    
    /**
     * 获取任务进度百分比
     * @return 进度百分比 0-100
     */
    uint8_t getProgress() const {
        if (status == TaskStatus::COMPLETED) return 100;
        if (status == TaskStatus::FAILED) return 0;
        if (status == TaskStatus::PENDING) return 0;
        
        // 对于运行中的任务，基于信号数量估算进度
        if (signals.size() > 0) {
            // 这里可以根据实际执行情况计算进度
            return 50; // 简化实现
        }
        
        return 0;
    }
    
    // ================================
    // JSON转换方法 - 严格匹配前端格式
    // ================================
    
    /**
     * 转换为JSON对象 - 完全匹配前端API响应格式
     * @return JSON文档对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        // 核心字段 - 按前端期望的顺序和格式
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["priority"] = priority;
        doc["status"] = getStatusString();
        
        // 信号数组
        JsonArray signalsArray = doc["signals"].to<JsonArray>();
        for (const String& signalId : signals) {
            signalsArray.add(signalId);
        }
        
        // 配置对象
        doc["config"] = config;
        
        // 时间戳
        doc["created"] = created;
        doc["started"] = started;
        doc["completed"] = completed;
        
        return doc;
    }
    
    /**
     * 从JSON对象创建任务数据 - 匹配前端数据格式
     * @param json JSON对象
     * @return 任务数据对象
     */
    static TaskData fromJson(const JsonObject& json) {
        TaskData task;
        
        // 核心字段解析
        task.id = json["id"].as<String>();
        task.name = json["name"].as<String>();
        task.type = json["type"].as<String>();
        task.priority = json["priority"].as<uint8_t>();
        task.setStatusFromString(json["status"].as<String>());
        
        // 信号数组解析
        JsonArray signalsArray = json["signals"];
        for (JsonVariant signalId : signalsArray) {
            task.signals.push_back(signalId.as<String>());
        }
        
        // 配置对象
        task.config = json["config"];
        
        // 时间戳
        task.created = json["created"].as<uint64_t>();
        task.started = json["started"].as<uint64_t>();
        task.completed = json["completed"].as<uint64_t>();
        
        return task;
    }
    
    /**
     * 转换为JSON字符串
     * @return JSON字符串
     */
    String toJsonString() const {
        JsonDocument doc = toJson();
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    /**
     * 从JSON字符串创建任务数据
     * @param jsonString JSON字符串
     * @return 任务数据对象
     */
    static TaskData fromJsonString(const String& jsonString) {
        JsonDocument doc;
        deserializeJson(doc, jsonString);
        return fromJson(doc.as<JsonObject>());
    }
    
    // ================================
    // 比较操作符
    // ================================
    
    /**
     * 相等比较操作符
     */
    bool operator==(const TaskData& other) const {
        return id == other.id;
    }
    
    /**
     * 不等比较操作符
     */
    bool operator!=(const TaskData& other) const {
        return !(*this == other);
    }
    
    /**
     * 小于比较操作符 - 用于优先级排序（优先级高的排在前面）
     */
    bool operator<(const TaskData& other) const {
        if (priority != other.priority) {
            return priority > other.priority; // 优先级高的排在前面
        }
        return created < other.created; // 同优先级按创建时间排序
    }
};

// ================================
// 任务数据工具函数
// ================================

/**
 * 生成任务ID - 匹配前端ID生成规则
 * @return 格式为task_12345678的ID
 */
inline String generateTaskId() {
    uint32_t timestamp = millis();
    return "task_" + String(timestamp % 100000000); // 8位数字
}

/**
 * 验证任务ID格式 - 匹配前端数据验证器
 * @param id 任务ID
 * @return 是否为有效格式
 */
inline bool isValidTaskId(const String& id) {
    if (id.length() != 13) return false; // task_ + 8位数字 = 13字符
    if (!id.startsWith("task_")) return false;
    
    String numberPart = id.substring(5);
    for (char c : numberPart) {
        if (!isDigit(c)) return false;
    }
    
    return true;
}

/**
 * 创建信号发射任务 - 匹配前端任务创建模式
 * @param name 任务名称
 * @param signals 信号ID列表
 * @param priority 任务优先级
 * @return 标准格式的任务数据
 */
inline TaskData createSignalEmitTask(const String& name, const std::vector<String>& signals, uint8_t priority = 2) {
    TaskData task;
    task.id = generateTaskId();
    task.name = name;
    task.type = "signal_emit";
    task.priority = priority;
    task.signals = signals;
    task.created = millis();
    task.taskType = TaskType::SIGNAL_EMIT;
    
    return task;
}

#endif // TASK_DATA_H
