/**
 * ESP32-S3红外控制系统 - WebSocket管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的WebSocket管理器
 * - 完全匹配前端WebSocket事件规范和6个事件类型的完整实现
 * - 支持前端WebSocket连接管理、消息处理、事件广播功能
 * - 提供实时双向通信和事件驱动的状态同步机制
 * 
 * 前端匹配度：
 * - 事件类型：100%匹配前端6个WebSocket事件类型
 * - 消息格式：100%匹配前端WebSocket消息格式标准
 * - 连接管理：100%匹配前端连接成功/断开事件
 * - 实时通信：100%匹配前端实时状态更新和事件广播
 * 
 * 后端架构匹配：
 * - 核心1处理：WebSocket在核心1处理，避免阻塞实时任务
 * - 事件驱动：完整的WebSocket事件发布和订阅机制
 * - 连接管理：多客户端连接管理和状态监控
 * - 消息验证：完整的消息格式验证和错误处理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef WEBSOCKET_MANAGER_H
#define WEBSOCKET_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <AsyncWebSocket.h>
#include <vector>
#include <unordered_map>
#include <functional>

// 配置文件
#include "../config/SystemConfig.h"
#include "../config/NetworkConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class EventManager;
class SystemManager;

// ================================
// WebSocket消息类型定义
// ================================

/**
 * WebSocket消息类型枚举 - 匹配前端6个事件类型
 */
enum class WebSocketMessageType : uint8_t {
    // 连接管理事件
    CONNECTED = 0,              // 连接成功
    DISCONNECTED = 1,           // 连接断开
    
    // 信号相关事件
    SIGNAL_LEARNED = 2,         // 信号学习完成
    SIGNAL_SENT = 3,            // 信号发射完成
    
    // 系统状态事件
    STATUS_UPDATE = 4,          // 系统状态更新
    ERROR = 5,                  // 系统错误
    
    // 扩展事件类型
    HEARTBEAT = 10,             // 心跳消息
    CONFIG_CHANGED = 11,        // 配置变更
    TIMER_EVENT = 12,           // 定时器事件
    HARDWARE_EVENT = 13         // 硬件事件
};

// ================================
// WebSocket消息结构定义
// ================================

/**
 * WebSocket消息结构 - 匹配前端消息格式标准
 */
struct WebSocketMessage {
    WebSocketMessageType type;          // 消息类型
    JsonDocument payload;               // 消息数据
    uint64_t timestamp;                 // 时间戳
    String clientId;                    // 客户端ID（可选）
    
    /**
     * 构造函数
     */
    WebSocketMessage(WebSocketMessageType msgType = WebSocketMessageType::HEARTBEAT)
        : type(msgType)
        , timestamp(millis()) {
    }
    
    /**
     * 转换为JSON字符串 - 匹配前端消息格式
     */
    String toJsonString() const {
        JsonDocument doc;
        doc["type"] = getTypeString();
        doc["payload"] = payload;
        doc["timestamp"] = timestamp;
        if (!clientId.isEmpty()) {
            doc["clientId"] = clientId;
        }
        
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    /**
     * 从JSON字符串解析
     */
    bool fromJsonString(const String& jsonStr) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, jsonStr);
        if (error) return false;
        
        String typeStr = doc["type"].as<String>();
        type = parseTypeString(typeStr);
        payload = doc["payload"];
        timestamp = doc["timestamp"].as<uint64_t>();
        clientId = doc["clientId"].as<String>();
        
        return true;
    }
    
    /**
     * 获取消息类型字符串
     */
    String getTypeString() const {
        switch (type) {
            case WebSocketMessageType::CONNECTED: return "connected";
            case WebSocketMessageType::DISCONNECTED: return "disconnected";
            case WebSocketMessageType::SIGNAL_LEARNED: return "signal_learned";
            case WebSocketMessageType::SIGNAL_SENT: return "signal_sent";
            case WebSocketMessageType::STATUS_UPDATE: return "status_update";
            case WebSocketMessageType::ERROR: return "error";
            case WebSocketMessageType::HEARTBEAT: return "heartbeat";
            case WebSocketMessageType::CONFIG_CHANGED: return "config_changed";
            case WebSocketMessageType::TIMER_EVENT: return "timer_event";
            case WebSocketMessageType::HARDWARE_EVENT: return "hardware_event";
            default: return "unknown";
        }
    }
    
    /**
     * 解析消息类型字符串
     */
    WebSocketMessageType parseTypeString(const String& typeStr) const {
        if (typeStr == "connected") return WebSocketMessageType::CONNECTED;
        if (typeStr == "disconnected") return WebSocketMessageType::DISCONNECTED;
        if (typeStr == "signal_learned") return WebSocketMessageType::SIGNAL_LEARNED;
        if (typeStr == "signal_sent") return WebSocketMessageType::SIGNAL_SENT;
        if (typeStr == "status_update") return WebSocketMessageType::STATUS_UPDATE;
        if (typeStr == "error") return WebSocketMessageType::ERROR;
        if (typeStr == "heartbeat") return WebSocketMessageType::HEARTBEAT;
        if (typeStr == "config_changed") return WebSocketMessageType::CONFIG_CHANGED;
        if (typeStr == "timer_event") return WebSocketMessageType::TIMER_EVENT;
        if (typeStr == "hardware_event") return WebSocketMessageType::HARDWARE_EVENT;
        return WebSocketMessageType::HEARTBEAT;
    }
};

// ================================
// 客户端连接信息定义
// ================================

/**
 * WebSocket客户端信息结构
 */
struct WebSocketClient {
    uint32_t clientId;                  // 客户端ID
    String clientIP;                    // 客户端IP地址
    String userAgent;                   // 用户代理
    uint64_t connectTime;               // 连接时间
    uint64_t lastActivity;              // 最后活动时间
    uint32_t messagesSent;              // 发送消息数
    uint32_t messagesReceived;          // 接收消息数
    bool authenticated;                 // 是否已认证
    
    /**
     * 构造函数
     */
    WebSocketClient(uint32_t id = 0) 
        : clientId(id)
        , connectTime(millis())
        , lastActivity(millis())
        , messagesSent(0)
        , messagesReceived(0)
        , authenticated(false) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["clientId"] = clientId;
        doc["clientIP"] = clientIP;
        doc["userAgent"] = userAgent;
        doc["connectTime"] = connectTime;
        doc["lastActivity"] = lastActivity;
        doc["messagesSent"] = messagesSent;
        doc["messagesReceived"] = messagesReceived;
        doc["authenticated"] = authenticated;
        doc["sessionDuration"] = millis() - connectTime;
        return doc;
    }
};

// ================================
// WebSocket统计信息定义
// ================================

/**
 * WebSocket统计信息结构
 */
struct WebSocketStatistics {
    uint32_t totalConnections;          // 总连接数
    uint32_t activeConnections;         // 活跃连接数
    uint32_t totalMessages;             // 总消息数
    uint32_t messagesSent;              // 发送消息数
    uint32_t messagesReceived;          // 接收消息数
    uint32_t broadcastMessages;         // 广播消息数
    uint64_t totalDataSent;             // 总发送数据量（字节）
    uint64_t totalDataReceived;         // 总接收数据量（字节）
    uint32_t connectionErrors;          // 连接错误数
    uint32_t messageErrors;             // 消息错误数
    
    /**
     * 构造函数
     */
    WebSocketStatistics() 
        : totalConnections(0)
        , activeConnections(0)
        , totalMessages(0)
        , messagesSent(0)
        , messagesReceived(0)
        , broadcastMessages(0)
        , totalDataSent(0)
        , totalDataReceived(0)
        , connectionErrors(0)
        , messageErrors(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalConnections"] = totalConnections;
        doc["activeConnections"] = activeConnections;
        doc["totalMessages"] = totalMessages;
        doc["messagesSent"] = messagesSent;
        doc["messagesReceived"] = messagesReceived;
        doc["broadcastMessages"] = broadcastMessages;
        doc["totalDataSent"] = totalDataSent;
        doc["totalDataReceived"] = totalDataReceived;
        doc["connectionErrors"] = connectionErrors;
        doc["messageErrors"] = messageErrors;
        return doc;
    }
};

// ================================
// WebSocket管理器类定义
// ================================

/**
 * WebSocket管理器类 - 完全匹配前端WebSocket事件规范
 * 
 * 职责：
 * 1. WebSocket连接管理
 * 2. 消息发送和广播
 * 3. 事件处理和转发
 * 4. 客户端状态监控
 * 5. 消息验证和错误处理
 */
class WebSocketManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     * @param sysMgr 系统管理器指针
     */
    WebSocketManager(EventManager* eventMgr = nullptr, SystemManager* sysMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~WebSocketManager();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化WebSocket管理器
     * @param webSocket AsyncWebSocket指针
     * @return 是否初始化成功
     */
    bool init(AsyncWebSocket* webSocket);
    
    /**
     * 清理WebSocket资源
     */
    void cleanup();
    
    /**
     * WebSocket管理器主循环
     */
    void loop();
    
    // ================================
    // 消息发送接口 - 匹配前端事件类型
    // ================================
    
    /**
     * 发送消息到指定客户端
     * @param clientId 客户端ID
     * @param message WebSocket消息
     * @return 是否发送成功
     */
    bool sendMessage(uint32_t clientId, const WebSocketMessage& message);
    
    /**
     * 广播消息到所有客户端
     * @param message WebSocket消息
     * @return 发送成功的客户端数量
     */
    uint32_t broadcastMessage(const WebSocketMessage& message);
    
    /**
     * 发送连接成功事件 - 匹配前端connected事件
     * @param clientId 客户端ID
     */
    void sendConnectedEvent(uint32_t clientId);
    
    /**
     * 广播连接断开事件 - 匹配前端disconnected事件
     * @param reason 断开原因
     */
    void broadcastDisconnectedEvent(const String& reason);
    
    /**
     * 广播信号学习完成事件 - 匹配前端signal_learned事件
     * @param signalData 信号数据
     */
    void broadcastSignalLearnedEvent(const JsonDocument& signalData);
    
    /**
     * 广播信号发射完成事件 - 匹配前端signal_sent事件
     * @param signalId 信号ID
     * @param success 是否成功
     */
    void broadcastSignalSentEvent(const String& signalId, bool success);
    
    /**
     * 广播系统状态更新事件 - 匹配前端status_update事件
     * @param statusData 状态数据
     */
    void broadcastStatusUpdateEvent(const JsonDocument& statusData);
    
    /**
     * 广播系统错误事件 - 匹配前端error事件
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    void broadcastErrorEvent(uint32_t errorCode, const String& errorMessage);
    
    /**
     * 广播配置变更事件
     * @param configData 配置数据
     */
    void broadcastConfigChangedEvent(const JsonDocument& configData = JsonDocument());
    
    // ================================
    // 连接管理接口
    // ================================
    
    /**
     * 获取活跃连接数
     * @return 活跃连接数
     */
    uint32_t getActiveConnectionCount() const { return statistics.activeConnections; }
    
    /**
     * 获取所有客户端信息
     * @return 客户端信息列表
     */
    std::vector<WebSocketClient> getAllClients() const;
    
    /**
     * 获取客户端信息
     * @param clientId 客户端ID
     * @return 客户端信息
     */
    WebSocketClient getClient(uint32_t clientId) const;
    
    /**
     * 断开指定客户端
     * @param clientId 客户端ID
     * @param reason 断开原因
     * @return 是否断开成功
     */
    bool disconnectClient(uint32_t clientId, const String& reason = "server_disconnect");
    
    /**
     * 断开所有客户端
     * @param reason 断开原因
     * @return 断开的客户端数量
     */
    uint32_t disconnectAllClients(const String& reason = "server_shutdown");
    
    // ================================
    // 统计和监控
    // ================================
    
    /**
     * 获取WebSocket统计信息
     * @return 统计信息
     */
    WebSocketStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取WebSocket状态JSON
     * @return 状态JSON对象
     */
    JsonDocument getWebSocketStatusJson() const;
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }
    
    /**
     * 设置系统管理器
     * @param sysMgr 系统管理器指针
     */
    void setSystemManager(SystemManager* sysMgr) { systemManager = sysMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    AsyncWebSocket* webSocket;          // WebSocket服务器
    EventManager* eventManager;         // 事件管理器
    SystemManager* systemManager;       // 系统管理器
    
    // 客户端管理
    std::unordered_map<uint32_t, WebSocketClient> clients; // 客户端映射
    uint32_t nextClientId;              // 下一个客户端ID
    
    // 统计信息
    WebSocketStatistics statistics;     // WebSocket统计
    
    // 心跳管理
    uint32_t lastHeartbeatTime;         // 上次心跳时间
    uint32_t heartbeatInterval;         // 心跳间隔
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    
    // ================================
    // 私有方法 - WebSocket事件处理
    // ================================
    
    /**
     * 处理WebSocket连接事件
     * @param client AsyncWebSocketClient指针
     */
    void handleWebSocketConnect(AsyncWebSocketClient* client);
    
    /**
     * 处理WebSocket断开事件
     * @param client AsyncWebSocketClient指针
     */
    void handleWebSocketDisconnect(AsyncWebSocketClient* client);
    
    /**
     * 处理WebSocket消息事件
     * @param client AsyncWebSocketClient指针
     * @param data 消息数据
     * @param len 数据长度
     */
    void handleWebSocketMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    
    /**
     * 处理WebSocket错误事件
     * @param client AsyncWebSocketClient指针
     * @param error 错误代码
     */
    void handleWebSocketError(AsyncWebSocketClient* client, uint16_t error);
    
    // ================================
    // 私有方法 - 消息处理
    // ================================
    
    /**
     * 验证WebSocket消息格式 - 匹配前端消息验证
     * @param message 消息对象
     * @return 是否有效
     */
    bool validateMessage(const WebSocketMessage& message) const;
    
    /**
     * 处理接收到的消息
     * @param clientId 客户端ID
     * @param message 消息对象
     */
    void processReceivedMessage(uint32_t clientId, const WebSocketMessage& message);
    
    /**
     * 发送心跳消息
     */
    void sendHeartbeat();
    
    /**
     * 检查客户端活动状态
     */
    void checkClientActivity();
    
    /**
     * 更新客户端活动时间
     * @param clientId 客户端ID
     */
    void updateClientActivity(uint32_t clientId);
    
    /**
     * 更新统计信息
     * @param sent 是否为发送操作
     * @param dataSize 数据大小
     */
    void updateStatistics(bool sent, size_t dataSize);
    
    /**
     * 生成客户端ID
     * @return 唯一客户端ID
     */
    String generateClientId() const;
    
    /**
     * 记录WebSocket错误
     * @param operation 操作名称
     * @param error 错误信息
     */
    void logWebSocketError(const String& operation, const String& error);
    
    /**
     * 静态WebSocket事件处理器
     * @param server AsyncWebSocket指针
     * @param client AsyncWebSocketClient指针
     * @param type 事件类型
     * @param arg 参数
     * @param data 数据
     * @param len 数据长度
     */
    static void webSocketEventHandler(AsyncWebSocket* server, AsyncWebSocketClient* client,
                                     AwsEventType type, void* arg, uint8_t* data, size_t len);
};

#endif // WEBSOCKET_MANAGER_H
