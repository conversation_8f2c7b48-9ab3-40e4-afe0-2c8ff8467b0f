/**
 * @file IRTaskData.h
 * @brief 任务数据结构定义 - 避免Arduino冲突
 * 
 * 避免使用LOW/HIGH等Arduino保留字
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_TASK_DATA_H
#define IR_TASK_DATA_H

#include <Arduino.h>

// 避免使用LOW/HIGH等Arduino保留字
enum IRTaskPriority {
    IR_PRIORITY_LOWEST = 1,     // 全部信号发射 - 最低优先级
    IR_PRIORITY_NORMAL = 2,     // 选中信号发射 - 中等优先级
    IR_PRIORITY_URGENT = 3,     // 定时任务 - 中等优先级
    IR_PRIORITY_CRITICAL = 4    // 信号学习 - 最高优先级
};

enum IRTaskType {
    IR_TASK_EMIT_SIGNAL = 0,    // 发射信号
    IR_TASK_LEARN_SIGNAL = 1,   // 学习信号
    IR_TASK_EMIT_BATCH = 2,     // 批量发射
    IR_TASK_TIMER_EMIT = 3,     // 定时发射
    IR_TASK_STATUS_UPDATE = 4   // 状态更新
};

enum IRTaskState {
    IR_TASK_PENDING = 0,        // 等待执行
    IR_TASK_RUNNING = 1,        // 正在执行
    IR_TASK_COMPLETED = 2,      // 执行完成
    IR_TASK_FAILED = 3,         // 执行失败
    IR_TASK_CANCELLED = 4       // 已取消
};

// 简化的任务数据结构
struct IRTaskData {
    // 核心字段
    char taskId[16];            // 任务ID
    IRTaskType type;            // 任务类型
    IRTaskPriority priority;    // 任务优先级
    IRTaskState state;          // 任务状态
    uint32_t created;           // 创建时间
    uint32_t scheduled;         // 计划执行时间
    uint32_t started;           // 开始执行时间
    uint32_t completed;         // 完成时间
    uint32_t timeout;           // 超时时间
    
    // 任务数据
    char signalId[16];          // 信号ID (用于发射任务)
    char signalIds[128];        // 信号ID列表 (用于批量任务，逗号分隔)
    uint16_t repeatCount;       // 重复次数
    uint16_t interval;          // 间隔时间 (毫秒)
    
    // 结果数据
    bool success;               // 是否成功
    char errorMessage[64];      // 错误信息
    uint16_t actualRepeats;     // 实际重复次数
    
    // 构造函数
    IRTaskData() {
        memset(this, 0, sizeof(IRTaskData));
        state = IR_TASK_PENDING;
        priority = IR_PRIORITY_NORMAL;
        timeout = 30000;  // 默认30秒超时
        repeatCount = 1;
        interval = 100;
    }
    
    // 辅助方法
    bool isExpired() const {
        return timeout > 0 && (millis() - created) > timeout;
    }
    
    bool isReady() const {
        return state == IR_TASK_PENDING && millis() >= scheduled;
    }
    
    void markStarted() {
        state = IR_TASK_RUNNING;
        started = millis();
    }
    
    void markCompleted(bool isSuccess, const char* error = nullptr) {
        state = isSuccess ? IR_TASK_COMPLETED : IR_TASK_FAILED;
        success = isSuccess;
        completed = millis();
        if (error && !isSuccess) {
            strncpy(errorMessage, error, 63);
            errorMessage[63] = '\0';
        }
    }
    
    const char* getStateString() const {
        switch(state) {
            case IR_TASK_PENDING: return "pending";
            case IR_TASK_RUNNING: return "running";
            case IR_TASK_COMPLETED: return "completed";
            case IR_TASK_FAILED: return "failed";
            case IR_TASK_CANCELLED: return "cancelled";
            default: return "unknown";
        }
    }
    
    const char* getTypeString() const {
        switch(type) {
            case IR_TASK_EMIT_SIGNAL: return "emit_signal";
            case IR_TASK_LEARN_SIGNAL: return "learn_signal";
            case IR_TASK_EMIT_BATCH: return "emit_batch";
            case IR_TASK_TIMER_EMIT: return "timer_emit";
            case IR_TASK_STATUS_UPDATE: return "status_update";
            default: return "unknown";
        }
    }
};

#endif
