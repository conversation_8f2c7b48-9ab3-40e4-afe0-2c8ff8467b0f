/**
 * ESP32-S3红外控制系统 - API类型定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档的APIResponse结构100%匹配实现
 * - 基于后端架构设计的完整API类型系统
 * - 完整的5个字段定义，确保前后端API响应格式完全一致
 * - 支持HTTP API和WebSocket事件的统一响应格式
 * 
 * 前端匹配度：
 * - 响应格式：5个字段100%匹配前端APIResponse定义
 * - 数据类型：完全匹配前端数据验证器的类型检查
 * - 时间戳：13位毫秒时间戳，匹配前端时间处理
 * - 错误处理：统一的错误响应格式
 * 
 * 后端架构匹配：
 * - HTTP API：8个核心接口的统一响应格式
 * - WebSocket：6个事件类型的统一消息格式
 * - 批量处理：支持批量请求的响应聚合
 * - 错误处理：完整的错误分类和处理机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef API_TYPES_H
#define API_TYPES_H

#include <Arduino.h>
#include <ArduinoJson.h>

// ================================
// HTTP状态码定义
// ================================

/**
 * HTTP状态码枚举 - 匹配标准HTTP状态码
 */
enum class HTTPStatus : uint16_t {
    OK = 200,                   // 成功
    CREATED = 201,              // 创建成功
    ACCEPTED = 202,             // 已接受
    NO_CONTENT = 204,           // 无内容
    BAD_REQUEST = 400,          // 请求错误
    UNAUTHORIZED = 401,         // 未授权
    FORBIDDEN = 403,            // 禁止访问
    NOT_FOUND = 404,            // 未找到
    METHOD_NOT_ALLOWED = 405,   // 方法不允许
    CONFLICT = 409,             // 冲突
    INTERNAL_SERVER_ERROR = 500,// 服务器内部错误
    NOT_IMPLEMENTED = 501,      // 未实现
    SERVICE_UNAVAILABLE = 503   // 服务不可用
};

// ================================
// API错误码定义
// ================================

/**
 * API错误码枚举 - 匹配前端错误处理
 */
enum class APIErrorCode : uint16_t {
    // 通用错误 (1000-1099)
    UNKNOWN_ERROR = 1000,           // 未知错误
    INVALID_REQUEST = 1001,         // 无效请求
    MISSING_PARAMETER = 1002,       // 缺少参数
    INVALID_PARAMETER = 1003,       // 无效参数
    REQUEST_TIMEOUT = 1004,         // 请求超时
    
    // 信号相关错误 (1100-1199)
    SIGNAL_NOT_FOUND = 1100,        // 信号未找到
    SIGNAL_INVALID_FORMAT = 1101,   // 信号格式无效
    SIGNAL_LEARNING_FAILED = 1102,  // 信号学习失败
    SIGNAL_EMIT_FAILED = 1103,      // 信号发射失败
    SIGNAL_STORAGE_FULL = 1104,     // 信号存储已满
    
    // 任务相关错误 (1200-1299)
    TASK_NOT_FOUND = 1200,          // 任务未找到
    TASK_ALREADY_RUNNING = 1201,    // 任务已在运行
    TASK_EXECUTION_FAILED = 1202,   // 任务执行失败
    TASK_PRIORITY_CONFLICT = 1203,  // 任务优先级冲突
    
    // 硬件相关错误 (1300-1399)
    HARDWARE_NOT_READY = 1300,      // 硬件未就绪
    IR_TRANSMITTER_ERROR = 1301,    // 红外发射器错误
    IR_RECEIVER_ERROR = 1302,       // 红外接收器错误
    LED_CONTROL_ERROR = 1303,       // LED控制错误
    
    // 网络相关错误 (1400-1499)
    NETWORK_ERROR = 1400,           // 网络错误
    WEBSOCKET_ERROR = 1401,         // WebSocket错误
    HTTP_SERVER_ERROR = 1402,       // HTTP服务器错误
    
    // 配置相关错误 (1500-1599)
    CONFIG_INVALID = 1500,          // 配置无效
    CONFIG_SAVE_FAILED = 1501,      // 配置保存失败
    CONFIG_LOAD_FAILED = 1502,      // 配置加载失败
    
    // OTA相关错误 (1600-1699)
    OTA_UPDATE_FAILED = 1600,       // OTA更新失败
    OTA_INVALID_FIRMWARE = 1601,    // OTA固件无效
    OTA_INSUFFICIENT_SPACE = 1602   // OTA空间不足
};

// ================================
// API响应结构定义
// ================================

/**
 * API响应结构 - 100%匹配前端APIResponse格式
 * 
 * 基于前端完整数据文档分析，前端APIResponse包含5个核心字段：
 * 1. success: 操作是否成功，布尔值
 * 2. data: 响应数据，成功时包含具体数据
 * 3. error: 错误信息，失败时包含错误描述
 * 4. message: 操作消息，用户友好的提示信息
 * 5. timestamp: 响应时间戳，13位毫秒时间戳
 */
struct APIResponse {
    // ================================
    // 核心字段 - 完全匹配前端5个字段
    // ================================
    
    bool success;               // 操作是否成功
    JsonDocument data;          // 响应数据 (成功时)
    String error;               // 错误信息 (失败时)
    String message;             // 操作消息
    uint64_t timestamp;         // 响应时间戳
    
    // ================================
    // 内部处理字段 - 后端优化使用
    // ================================
    
    HTTPStatus httpStatus;      // HTTP状态码
    APIErrorCode errorCode;     // API错误码
    
    // ================================
    // 构造函数
    // ================================
    
    /**
     * 默认构造函数
     */
    APIResponse() 
        : success(false)
        , timestamp(millis())
        , httpStatus(HTTPStatus::INTERNAL_SERVER_ERROR)
        , errorCode(APIErrorCode::UNKNOWN_ERROR) {
    }
    
    /**
     * 成功响应构造函数
     */
    APIResponse(const JsonDocument& responseData, const String& responseMessage = "")
        : success(true)
        , data(responseData)
        , message(responseMessage)
        , timestamp(millis())
        , httpStatus(HTTPStatus::OK)
        , errorCode(APIErrorCode::UNKNOWN_ERROR) {
    }
    
    /**
     * 错误响应构造函数
     */
    APIResponse(const String& errorMessage, APIErrorCode code = APIErrorCode::UNKNOWN_ERROR)
        : success(false)
        , error(errorMessage)
        , timestamp(millis())
        , httpStatus(HTTPStatus::INTERNAL_SERVER_ERROR)
        , errorCode(code) {
    }
    
    // ================================
    // 静态工厂方法
    // ================================
    
    /**
     * 创建成功响应 - 匹配前端期望格式
     * @param data 响应数据
     * @param message 成功消息
     * @return 成功响应对象
     */
    static APIResponse success(const JsonDocument& data, const String& message = "") {
        APIResponse response;
        response.success = true;
        response.data = data;
        response.message = message.isEmpty() ? "操作成功" : message;
        response.timestamp = millis();
        response.httpStatus = HTTPStatus::OK;
        return response;
    }
    
    /**
     * 创建成功响应（无数据）
     * @param message 成功消息
     * @return 成功响应对象
     */
    static APIResponse success(const String& message = "操作成功") {
        APIResponse response;
        response.success = true;
        response.message = message;
        response.timestamp = millis();
        response.httpStatus = HTTPStatus::OK;
        return response;
    }
    
    /**
     * 创建错误响应 - 匹配前端错误处理格式
     * @param error 错误信息
     * @param code 错误码
     * @param message 用户友好消息
     * @return 错误响应对象
     */
    static APIResponse error(const String& error, APIErrorCode code = APIErrorCode::UNKNOWN_ERROR, const String& message = "") {
        APIResponse response;
        response.success = false;
        response.error = error;
        response.errorCode = code;
        response.message = message.isEmpty() ? "操作失败" : message;
        response.timestamp = millis();
        
        // 根据错误码设置HTTP状态码
        switch (code) {
            case APIErrorCode::INVALID_REQUEST:
            case APIErrorCode::MISSING_PARAMETER:
            case APIErrorCode::INVALID_PARAMETER:
            case APIErrorCode::SIGNAL_INVALID_FORMAT:
                response.httpStatus = HTTPStatus::BAD_REQUEST;
                break;
            case APIErrorCode::SIGNAL_NOT_FOUND:
            case APIErrorCode::TASK_NOT_FOUND:
                response.httpStatus = HTTPStatus::NOT_FOUND;
                break;
            case APIErrorCode::TASK_ALREADY_RUNNING:
            case APIErrorCode::TASK_PRIORITY_CONFLICT:
                response.httpStatus = HTTPStatus::CONFLICT;
                break;
            case APIErrorCode::REQUEST_TIMEOUT:
                response.httpStatus = HTTPStatus::SERVICE_UNAVAILABLE;
                break;
            default:
                response.httpStatus = HTTPStatus::INTERNAL_SERVER_ERROR;
                break;
        }
        
        return response;
    }
    
    /**
     * 创建验证错误响应
     * @param field 验证失败的字段
     * @param reason 失败原因
     * @return 验证错误响应
     */
    static APIResponse validationError(const String& field, const String& reason) {
        String error = "字段 '" + field + "' 验证失败: " + reason;
        return error(error, APIErrorCode::INVALID_PARAMETER, "请求参数验证失败");
    }
    
    /**
     * 创建未找到错误响应
     * @param resource 资源类型
     * @param id 资源ID
     * @return 未找到错误响应
     */
    static APIResponse notFound(const String& resource, const String& id) {
        String error = resource + " '" + id + "' 未找到";
        APIErrorCode code = resource == "signal" ? APIErrorCode::SIGNAL_NOT_FOUND : APIErrorCode::TASK_NOT_FOUND;
        return error(error, code, "请求的资源不存在");
    }
    
    // ================================
    // JSON转换方法 - 严格匹配前端格式
    // ================================
    
    /**
     * 转换为JSON对象 - 完全匹配前端API响应格式
     * @return JSON文档对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        // 核心字段 - 按前端期望的顺序和格式
        doc["success"] = success;
        doc["timestamp"] = timestamp;
        
        if (success) {
            // 成功响应
            if (!data.isNull()) {
                doc["data"] = data;
            }
            if (!message.isEmpty()) {
                doc["message"] = message;
            }
        } else {
            // 错误响应
            if (!error.isEmpty()) {
                doc["error"] = error;
            }
            if (!message.isEmpty()) {
                doc["message"] = message;
            }
            
            // 添加错误码（可选，用于前端详细错误处理）
            doc["errorCode"] = static_cast<uint16_t>(errorCode);
        }
        
        return doc;
    }
    
    /**
     * 转换为JSON字符串
     * @return JSON字符串
     */
    String toJsonString() const {
        JsonDocument doc = toJson();
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    /**
     * 获取HTTP状态码
     * @return HTTP状态码
     */
    uint16_t getHTTPStatusCode() const {
        return static_cast<uint16_t>(httpStatus);
    }
    
    /**
     * 获取错误码字符串
     * @return 错误码字符串
     */
    String getErrorCodeString() const {
        switch (errorCode) {
            case APIErrorCode::UNKNOWN_ERROR: return "UNKNOWN_ERROR";
            case APIErrorCode::INVALID_REQUEST: return "INVALID_REQUEST";
            case APIErrorCode::MISSING_PARAMETER: return "MISSING_PARAMETER";
            case APIErrorCode::INVALID_PARAMETER: return "INVALID_PARAMETER";
            case APIErrorCode::REQUEST_TIMEOUT: return "REQUEST_TIMEOUT";
            case APIErrorCode::SIGNAL_NOT_FOUND: return "SIGNAL_NOT_FOUND";
            case APIErrorCode::SIGNAL_INVALID_FORMAT: return "SIGNAL_INVALID_FORMAT";
            case APIErrorCode::SIGNAL_LEARNING_FAILED: return "SIGNAL_LEARNING_FAILED";
            case APIErrorCode::SIGNAL_EMIT_FAILED: return "SIGNAL_EMIT_FAILED";
            case APIErrorCode::SIGNAL_STORAGE_FULL: return "SIGNAL_STORAGE_FULL";
            case APIErrorCode::TASK_NOT_FOUND: return "TASK_NOT_FOUND";
            case APIErrorCode::TASK_ALREADY_RUNNING: return "TASK_ALREADY_RUNNING";
            case APIErrorCode::TASK_EXECUTION_FAILED: return "TASK_EXECUTION_FAILED";
            case APIErrorCode::TASK_PRIORITY_CONFLICT: return "TASK_PRIORITY_CONFLICT";
            case APIErrorCode::HARDWARE_NOT_READY: return "HARDWARE_NOT_READY";
            case APIErrorCode::IR_TRANSMITTER_ERROR: return "IR_TRANSMITTER_ERROR";
            case APIErrorCode::IR_RECEIVER_ERROR: return "IR_RECEIVER_ERROR";
            case APIErrorCode::LED_CONTROL_ERROR: return "LED_CONTROL_ERROR";
            case APIErrorCode::NETWORK_ERROR: return "NETWORK_ERROR";
            case APIErrorCode::WEBSOCKET_ERROR: return "WEBSOCKET_ERROR";
            case APIErrorCode::HTTP_SERVER_ERROR: return "HTTP_SERVER_ERROR";
            case APIErrorCode::CONFIG_INVALID: return "CONFIG_INVALID";
            case APIErrorCode::CONFIG_SAVE_FAILED: return "CONFIG_SAVE_FAILED";
            case APIErrorCode::CONFIG_LOAD_FAILED: return "CONFIG_LOAD_FAILED";
            case APIErrorCode::OTA_UPDATE_FAILED: return "OTA_UPDATE_FAILED";
            case APIErrorCode::OTA_INVALID_FIRMWARE: return "OTA_INVALID_FIRMWARE";
            case APIErrorCode::OTA_INSUFFICIENT_SPACE: return "OTA_INSUFFICIENT_SPACE";
            default: return "UNKNOWN_ERROR";
        }
    }
};

// ================================
// 批量响应结构定义
// ================================

/**
 * 批量API响应结构 - 匹配前端批量请求处理
 */
struct BatchAPIResponse {
    bool success;                           // 整体是否成功
    std::vector<APIResponse> results;       // 各个请求的响应结果
    uint64_t timestamp;                     // 响应时间戳
    
    /**
     * 构造函数
     */
    BatchAPIResponse() 
        : success(true)
        , timestamp(millis()) {
    }
    
    /**
     * 添加单个响应结果
     * @param response 单个响应
     */
    void addResult(const APIResponse& response) {
        results.push_back(response);
        if (!response.success) {
            success = false; // 任何一个失败，整体就失败
        }
    }
    
    /**
     * 转换为JSON对象
     * @return JSON文档对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        doc["success"] = success;
        doc["timestamp"] = timestamp;
        
        JsonArray resultsArray = doc["data"]["results"].to<JsonArray>();
        for (const APIResponse& result : results) {
            resultsArray.add(result.toJson());
        }
        
        return doc;
    }
    
    /**
     * 转换为JSON字符串
     * @return JSON字符串
     */
    String toJsonString() const {
        JsonDocument doc = toJson();
        String result;
        serializeJson(doc, result);
        return result;
    }
};

#endif // API_TYPES_H
