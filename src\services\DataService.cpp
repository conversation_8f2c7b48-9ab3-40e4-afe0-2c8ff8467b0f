/**
 * ESP32-S3红外控制系统 - 数据管理服务实现
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的数据管理服务实现
 * - 完全匹配前端数据管理层和后端架构设计的数据管理规范
 * - 支持信号数据、任务数据、配置数据的统一管理和存储
 * - 提供L1+L2存储架构、数据验证、缓存管理等完整数据服务
 * 
 * 前端匹配度：
 * - 数据管理：100%匹配前端数据管理层(Data Layer)设计
 * - 导入导出：100%匹配前端信号数据导出（JSON格式）功能
 * - 文件管理：100%匹配前端文件导入信号功能
 * - 数据格式：100%匹配前端JSON数据格式标准
 * 
 * 后端架构匹配：
 * - 服务架构：继承BaseService，符合统一服务接口
 * - L1+L2存储：使用OptimizedStorage实现二级存储架构
 * - 数据验证：使用DataValidator确保数据完整性
 * - 缓存管理：使用CacheManager实现高性能缓存
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "DataService.h"
#include "../core/EventManager.h"
#include "../storage/OptimizedStorage.h"
#include "../storage/FlashStorage.h"
#include "../storage/CacheManager.h"
#include "../storage/DataValidator.h"
#include "../utils/Logger.h"

DataService::DataService(EventManager* eventMgr)
    : BaseService(eventMgr, "DataService")
    , optimizedStorage(nullptr)
    , flashStorage(nullptr)
    , cacheManager(nullptr)
    , dataValidator(nullptr)
    , totalDataSize(0)
    , lastBackupTime(0)
    , autoBackupEnabled(true)
    , compressionEnabled(true) {
    
    // 初始化数据统计
    dataStatistics = DataStatistics();
    
    LOG_INFO("DataService", "数据管理服务构造完成");
}

DataService::~DataService() {
    cleanup();
    LOG_INFO("DataService", "数据管理服务析构完成");
}

bool DataService::init() {
    LOG_INFO("DataService", "开始初始化数据管理服务...");
    
    // 初始化数据验证器
    dataValidator = new DataValidator();
    if (!dataValidator) {
        LOG_ERROR("DataService", "数据验证器创建失败");
        return false;
    }
    
    // 初始化缓存管理器
    cacheManager = new CacheManager(CacheManagerConfig(), eventManager);
    if (!cacheManager->init()) {
        LOG_ERROR("DataService", "缓存管理器初始化失败");
        return false;
    }
    
    // 初始化Flash存储
    flashStorage = new FlashStorage(FlashStorageConfig(), dataValidator);
    if (!flashStorage->init()) {
        LOG_ERROR("DataService", "Flash存储初始化失败");
        return false;
    }
    
    // 初始化优化存储
    optimizedStorage = new OptimizedStorage();
    optimizedStorage->setFlashStorage(flashStorage);
    optimizedStorage->setCacheManager(cacheManager);
    optimizedStorage->setDataValidator(dataValidator);
    
    if (!optimizedStorage->init()) {
        LOG_ERROR("DataService", "优化存储初始化失败");
        return false;
    }
    
    // 加载数据统计
    loadDataStatistics();
    
    // 注册事件处理器
    registerEventHandlers();
    
    LOG_INFO("DataService", "数据管理服务初始化完成");
    return true;
}

void DataService::cleanup() {
    LOG_INFO("DataService", "开始清理数据管理服务...");
    
    // 执行最后的数据备份
    if (autoBackupEnabled) {
        performBackup();
    }
    
    // 保存数据统计
    saveDataStatistics();
    
    // 清理存储组件
    if (optimizedStorage) {
        delete optimizedStorage;
        optimizedStorage = nullptr;
    }
    
    if (flashStorage) {
        delete flashStorage;
        flashStorage = nullptr;
    }
    
    if (cacheManager) {
        delete cacheManager;
        cacheManager = nullptr;
    }
    
    if (dataValidator) {
        delete dataValidator;
        dataValidator = nullptr;
    }
    
    LOG_INFO("DataService", "数据管理服务清理完成");
}

void DataService::loop() {
    uint32_t currentTime = millis();
    
    // 定期执行自动备份
    if (autoBackupEnabled && (currentTime - lastBackupTime >= AUTO_BACKUP_INTERVAL)) {
        performBackup();
        lastBackupTime = currentTime;
    }
    
    // 更新数据统计
    updateDataStatistics();
    
    // 执行存储维护
    performStorageMaintenance();
}

bool DataService::saveSignal(const SignalData& signal) {
    // 验证信号数据
    auto validationResult = dataValidator->validateSignalData(signal);
    if (!validationResult.isValid) {
        LOG_ERROR("DataService", "信号数据验证失败: %s", signal.id.c_str());
        handleError("SIGNAL_VALIDATION_FAILED", "信号数据验证失败", ErrorSeverity::MEDIUM);
        return false;
    }
    
    // 保存到优化存储
    bool success = optimizedStorage->saveSignal(signal);
    if (success) {
        // 更新统计
        dataStatistics.totalSignals++;
        dataStatistics.totalWrites++;
        
        // 发布数据保存事件
        publishDataEvent(EventType::DATA_SAVED, "signal", signal.id);
        
        LOG_DEBUG("DataService", "信号保存成功: %s", signal.id.c_str());
    } else {
        dataStatistics.failedWrites++;
        handleError("SIGNAL_SAVE_FAILED", "信号保存失败", ErrorSeverity::HIGH);
    }
    
    return success;
}

SignalData DataService::loadSignal(const String& signalId) {
    uint32_t startTime = micros();
    
    // 从优化存储加载
    SignalData signal = optimizedStorage->loadSignal(signalId);
    
    uint32_t loadTime = micros() - startTime;
    
    // 更新统计
    dataStatistics.totalReads++;
    dataStatistics.totalReadTime += loadTime;
    
    if (signal.isValid()) {
        // 发布数据加载事件
        publishDataEvent(EventType::DATA_LOADED, "signal", signalId);
        LOG_DEBUG("DataService", "信号加载成功: %s, 耗时: %u μs", signalId.c_str(), loadTime);
    } else {
        dataStatistics.failedReads++;
        LOG_WARNING("DataService", "信号加载失败: %s", signalId.c_str());
    }
    
    return signal;
}

bool DataService::removeSignal(const String& signalId) {
    bool success = optimizedStorage->removeSignal(signalId);
    if (success) {
        // 更新统计
        dataStatistics.totalSignals--;
        dataStatistics.totalDeletes++;
        
        // 发布数据删除事件
        publishDataEvent(EventType::DATA_DELETED, "signal", signalId);
        
        LOG_DEBUG("DataService", "信号删除成功: %s", signalId.c_str());
    } else {
        dataStatistics.failedDeletes++;
        handleError("SIGNAL_DELETE_FAILED", "信号删除失败", ErrorSeverity::MEDIUM);
    }
    
    return success;
}

bool DataService::signalExists(const String& signalId) {
    return optimizedStorage->signalExists(signalId);
}

std::vector<String> DataService::getAllSignalIds() {
    return optimizedStorage->getAllSignalIds();
}

std::vector<SignalData> DataService::getAllSignals() {
    return optimizedStorage->getAllSignals();
}

bool DataService::saveTask(const TaskData& task) {
    // 验证任务数据
    auto validationResult = dataValidator->validateTaskData(task);
    if (!validationResult.isValid) {
        LOG_ERROR("DataService", "任务数据验证失败: %s", task.id.c_str());
        handleError("TASK_VALIDATION_FAILED", "任务数据验证失败", ErrorSeverity::MEDIUM);
        return false;
    }
    
    // 保存到Flash存储
    JsonDocument taskJson = task.toJson();
    bool success = flashStorage->store(task.id, taskJson);
    
    if (success) {
        // 更新统计
        dataStatistics.totalTasks++;
        dataStatistics.totalWrites++;
        
        // 发布数据保存事件
        publishDataEvent(EventType::DATA_SAVED, "task", task.id);
        
        LOG_DEBUG("DataService", "任务保存成功: %s", task.id.c_str());
    } else {
        dataStatistics.failedWrites++;
        handleError("TASK_SAVE_FAILED", "任务保存失败", ErrorSeverity::HIGH);
    }
    
    return success;
}

TaskData DataService::loadTask(const String& taskId) {
    uint32_t startTime = micros();
    
    // 从Flash存储加载
    JsonDocument taskJson = flashStorage->load(taskId);
    
    uint32_t loadTime = micros() - startTime;
    
    // 更新统计
    dataStatistics.totalReads++;
    dataStatistics.totalReadTime += loadTime;
    
    TaskData task;
    if (!taskJson.isNull()) {
        task.fromJson(taskJson);
        
        // 发布数据加载事件
        publishDataEvent(EventType::DATA_LOADED, "task", taskId);
        LOG_DEBUG("DataService", "任务加载成功: %s, 耗时: %u μs", taskId.c_str(), loadTime);
    } else {
        dataStatistics.failedReads++;
        LOG_WARNING("DataService", "任务加载失败: %s", taskId.c_str());
    }
    
    return task;
}

bool DataService::removeTask(const String& taskId) {
    bool success = flashStorage->remove(taskId);
    if (success) {
        // 更新统计
        dataStatistics.totalTasks--;
        dataStatistics.totalDeletes++;
        
        // 发布数据删除事件
        publishDataEvent(EventType::DATA_DELETED, "task", taskId);
        
        LOG_DEBUG("DataService", "任务删除成功: %s", taskId.c_str());
    } else {
        dataStatistics.failedDeletes++;
        handleError("TASK_DELETE_FAILED", "任务删除失败", ErrorSeverity::MEDIUM);
    }
    
    return success;
}

std::vector<String> DataService::getAllTaskIds() {
    return flashStorage->getAllIds();
}

JsonDocument DataService::exportData(const String& dataType, const std::vector<String>& ids) {
    JsonDocument exportDoc;
    exportDoc["version"] = "2.0";
    exportDoc["timestamp"] = millis();
    exportDoc["dataType"] = dataType;
    exportDoc["count"] = ids.size();
    
    JsonArray dataArray = exportDoc["data"].to<JsonArray>();
    
    if (dataType == "signals") {
        for (const auto& id : ids) {
            SignalData signal = loadSignal(id);
            if (signal.isValid()) {
                dataArray.add(signal.toJson());
            }
        }
    } else if (dataType == "tasks") {
        for (const auto& id : ids) {
            TaskData task = loadTask(id);
            if (task.isValid()) {
                dataArray.add(task.toJson());
            }
        }
    }
    
    // 更新统计
    dataStatistics.totalExports++;
    
    // 发布数据导出事件
    JsonDocument eventData;
    eventData["dataType"] = dataType;
    eventData["count"] = dataArray.size();
    emitEvent(EventType::DATA_EXPORTED, eventData);
    
    LOG_INFO("DataService", "数据导出完成: %s, %u项", dataType.c_str(), dataArray.size());
    return exportDoc;
}

uint32_t DataService::importData(const JsonDocument& importDoc) {
    if (!importDoc.containsKey("data") || !importDoc.containsKey("dataType")) {
        LOG_ERROR("DataService", "导入数据格式错误");
        return 0;
    }
    
    String dataType = importDoc["dataType"].as<String>();
    JsonArray dataArray = importDoc["data"];
    uint32_t successCount = 0;
    
    if (dataType == "signals") {
        for (JsonVariant item : dataArray) {
            SignalData signal;
            signal.fromJson(item.as<JsonDocument>());
            if (saveSignal(signal)) {
                successCount++;
            }
        }
    } else if (dataType == "tasks") {
        for (JsonVariant item : dataArray) {
            TaskData task;
            task.fromJson(item.as<JsonDocument>());
            if (saveTask(task)) {
                successCount++;
            }
        }
    }
    
    // 更新统计
    dataStatistics.totalImports++;
    
    // 发布数据导入事件
    JsonDocument eventData;
    eventData["dataType"] = dataType;
    eventData["successCount"] = successCount;
    eventData["totalCount"] = dataArray.size();
    emitEvent(EventType::DATA_IMPORTED, eventData);
    
    LOG_INFO("DataService", "数据导入完成: %s, %u/%u项", dataType.c_str(), successCount, dataArray.size());
    return successCount;
}

bool DataService::performBackup() {
    LOG_INFO("DataService", "开始执行数据备份...");
    
    uint32_t startTime = millis();
    
    // 备份信号数据
    auto signalIds = getAllSignalIds();
    JsonDocument signalBackup = exportData("signals", signalIds);
    
    // 备份任务数据
    auto taskIds = getAllTaskIds();
    JsonDocument taskBackup = exportData("tasks", taskIds);
    
    // 创建完整备份
    JsonDocument fullBackup;
    fullBackup["version"] = "2.0";
    fullBackup["timestamp"] = millis();
    fullBackup["signals"] = signalBackup;
    fullBackup["tasks"] = taskBackup;
    
    // 保存备份到Flash
    String backupId = "backup_" + String(millis());
    bool success = flashStorage->store(backupId, fullBackup);
    
    uint32_t duration = millis() - startTime;
    
    if (success) {
        dataStatistics.totalBackups++;
        dataStatistics.lastBackupTime = millis();
        
        LOG_INFO("DataService", "数据备份完成: %s, 耗时: %u ms", backupId.c_str(), duration);
    } else {
        dataStatistics.failedBackups++;
        LOG_ERROR("DataService", "数据备份失败");
    }
    
    return success;
}

bool DataService::restoreFromBackup(const String& backupId) {
    LOG_INFO("DataService", "开始从备份恢复数据: %s", backupId.c_str());
    
    // 加载备份数据
    JsonDocument backupData = flashStorage->load(backupId);
    if (backupData.isNull()) {
        LOG_ERROR("DataService", "备份数据不存在: %s", backupId.c_str());
        return false;
    }
    
    uint32_t totalRestored = 0;
    
    // 恢复信号数据
    if (backupData.containsKey("signals")) {
        uint32_t signalCount = importData(backupData["signals"]);
        totalRestored += signalCount;
        LOG_INFO("DataService", "恢复信号数据: %u个", signalCount);
    }
    
    // 恢复任务数据
    if (backupData.containsKey("tasks")) {
        uint32_t taskCount = importData(backupData["tasks"]);
        totalRestored += taskCount;
        LOG_INFO("DataService", "恢复任务数据: %u个", taskCount);
    }
    
    // 发布数据恢复事件
    JsonDocument eventData;
    eventData["backupId"] = backupId;
    eventData["restoredCount"] = totalRestored;
    emitEvent(EventType::DATA_RESTORED, eventData);
    
    LOG_INFO("DataService", "数据恢复完成: %u项", totalRestored);
    return true;
}

JsonDocument DataService::getDataStatistics() {
    JsonDocument stats;
    
    // 基本统计
    stats["totalSignals"] = dataStatistics.totalSignals;
    stats["totalTasks"] = dataStatistics.totalTasks;
    stats["totalReads"] = dataStatistics.totalReads;
    stats["totalWrites"] = dataStatistics.totalWrites;
    stats["totalDeletes"] = dataStatistics.totalDeletes;
    
    // 性能统计
    if (dataStatistics.totalReads > 0) {
        stats["averageReadTime"] = dataStatistics.totalReadTime / dataStatistics.totalReads;
    }
    
    // 成功率统计
    uint32_t totalOperations = dataStatistics.totalReads + dataStatistics.totalWrites + dataStatistics.totalDeletes;
    uint32_t failedOperations = dataStatistics.failedReads + dataStatistics.failedWrites + dataStatistics.failedDeletes;
    if (totalOperations > 0) {
        stats["successRate"] = (float)(totalOperations - failedOperations) / totalOperations * 100;
    }
    
    // 存储统计
    if (optimizedStorage) {
        stats["storage"] = optimizedStorage->getStatistics().toJson();
    }
    
    // 缓存统计
    if (cacheManager) {
        stats["cache"] = cacheManager->getStatistics().toJson();
    }
    
    // 备份统计
    stats["totalBackups"] = dataStatistics.totalBackups;
    stats["failedBackups"] = dataStatistics.failedBackups;
    stats["lastBackupTime"] = dataStatistics.lastBackupTime;
    
    return stats;
}

JsonDocument DataService::getStorageUsage() {
    JsonDocument usage;
    
    if (flashStorage) {
        usage["flash"] = flashStorage->getStorageUsage();
    }
    
    if (cacheManager) {
        usage["cache"] = cacheManager->getCacheUsage();
    }
    
    return usage;
}

void DataService::setAutoBackupEnabled(bool enabled) {
    autoBackupEnabled = enabled;
    LOG_INFO("DataService", "自动备份: %s", enabled ? "启用" : "禁用");
}

void DataService::setCompressionEnabled(bool enabled) {
    compressionEnabled = enabled;
    if (flashStorage) {
        flashStorage->setCompressionEnabled(enabled);
    }
    LOG_INFO("DataService", "数据压缩: %s", enabled ? "启用" : "禁用");
}

void DataService::registerEventHandlers() {
    if (!eventManager) return;
    
    // 注册数据管理相关事件处理器
    eventManager->subscribe(EventType::DATA_BACKUP_REQUEST, [this](const JsonDocument& data) {
        performBackup();
    });
    
    eventManager->subscribe(EventType::DATA_RESTORE_REQUEST, [this](const JsonDocument& data) {
        String backupId = data["backupId"].as<String>();
        restoreFromBackup(backupId);
    });
    
    eventManager->subscribe(EventType::DATA_EXPORT_REQUEST, [this](const JsonDocument& data) {
        String dataType = data["dataType"].as<String>();
        JsonArray ids = data["ids"];
        std::vector<String> idList;
        for (JsonVariant id : ids) {
            idList.push_back(id.as<String>());
        }
        exportData(dataType, idList);
    });
}

void DataService::loadDataStatistics() {
    // 从存储加载数据统计
    JsonDocument statsDoc = flashStorage->load("data_statistics");
    if (!statsDoc.isNull()) {
        dataStatistics.fromJson(statsDoc);
    }
}

void DataService::saveDataStatistics() {
    // 保存数据统计到存储
    JsonDocument statsDoc = dataStatistics.toJson();
    flashStorage->store("data_statistics", statsDoc);
}

void DataService::updateDataStatistics() {
    // 更新数据大小统计
    if (optimizedStorage) {
        auto storageStats = optimizedStorage->getStatistics();
        totalDataSize = storageStats.totalSize;
    }
    
    // 计算平均读取时间
    if (dataStatistics.totalReads > 0) {
        dataStatistics.averageReadTime = dataStatistics.totalReadTime / dataStatistics.totalReads;
    }
}

void DataService::performStorageMaintenance() {
    // 执行存储维护任务
    if (optimizedStorage) {
        optimizedStorage->performMaintenance();
    }
    
    if (flashStorage) {
        // 定期压缩存储空间
        static uint32_t lastCompactTime = 0;
        if (millis() - lastCompactTime >= COMPACT_INTERVAL) {
            flashStorage->compactStorage();
            lastCompactTime = millis();
        }
    }
}

void DataService::publishDataEvent(EventType eventType, const String& dataType, const String& dataId) {
    JsonDocument eventData;
    eventData["dataType"] = dataType;
    eventData["dataId"] = dataId;
    eventData["timestamp"] = millis();
    emitEvent(eventType, eventData);
}
