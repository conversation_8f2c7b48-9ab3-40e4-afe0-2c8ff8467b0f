/**
 * @file IRSignalData.h
 * @brief 红外信号数据结构定义 - Arduino友好版本
 * 
 * 完全避免Arduino保留字冲突，使用固定长度字符数组
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_SIGNAL_DATA_H
#define IR_SIGNAL_DATA_H

#include <Arduino.h>

// 避免使用Arduino保留字，使用IR前缀
enum IRSignalType {
    IR_TYPE_TV = 0,
    IR_TYPE_AC = 1,
    IR_TYPE_FAN = 2,
    IR_TYPE_LIGHT = 3,
    IR_TYPE_OTHER = 4
};

enum IRProtocolType {
    IR_PROTOCOL_NEC = 0,
    IR_PROTOCOL_RC5 = 1,
    IR_PROTOCOL_SONY = 2,
    IR_PROTOCOL_RAW = 3
};

// 简化的信号数据结构，避免复杂字段
struct IRSignalData {
    // 核心字段 - 完全匹配前端期望
    char id[16];                // signal_12345678格式
    char name[32];              // 信号名称
    IRSignalType type;          // 信号类型枚举
    char description[64];       // 信号描述
    char signalCode[32];        // 信号代码
    IRProtocolType protocol;    // 协议类型枚举
    uint16_t frequency;         // 载波频率
    char data[128];             // 红外数据 (十六进制字符串)
    bool isLearned;             // 是否已学习
    uint32_t created;           // 创建时间 (Unix时间戳)
    uint32_t lastSent;          // 最后发送时间
    uint16_t sentCount;         // 发送次数
    
    // 辅助字段
    bool isValid;               // 数据是否有效
    
    // 构造函数
    IRSignalData() {
        memset(this, 0, sizeof(IRSignalData));
        type = IR_TYPE_OTHER;
        protocol = IR_PROTOCOL_NEC;
        frequency = 38000;
        isLearned = false;
        isValid = false;
    }
    
    // 辅助方法
    const char* getTypeString() const {
        switch(type) {
            case IR_TYPE_TV: return "tv";
            case IR_TYPE_AC: return "ac";
            case IR_TYPE_FAN: return "fan";
            case IR_TYPE_LIGHT: return "light";
            default: return "other";
        }
    }
    
    const char* getProtocolString() const {
        switch(protocol) {
            case IR_PROTOCOL_NEC: return "NEC";
            case IR_PROTOCOL_RC5: return "RC5";
            case IR_PROTOCOL_SONY: return "SONY";
            default: return "RAW";
        }
    }
    
    // 验证数据有效性
    bool validate() {
        isValid = (strlen(id) > 0 && strlen(name) > 0 && 
                  strlen(data) > 0 && frequency > 0);
        return isValid;
    }
    
    // 更新访问统计
    void updateAccess() {
        lastSent = millis();
        sentCount++;
    }
};

#endif
