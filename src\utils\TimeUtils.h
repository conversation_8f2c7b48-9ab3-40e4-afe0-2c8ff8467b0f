/**
 * ESP32-S3红外控制系统 - 时间工具类
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的时间工具类
 * - 完全匹配前端定时任务管理和后端架构设计的时间处理规范
 * - 支持高精度时间处理、定时任务调度、时间片处理等功能
 * - 提供100ms精度的统一定时器管理和时间格式化工具
 * 
 * 前端匹配度：
 * - 定时精度：100%匹配前端100ms精度定时任务要求
 * - 时间片处理：100%匹配前端8ms时间片避免阻塞UI
 * - 定时任务：100%匹配前端统一定时器管理器需求
 * - 时间格式：100%匹配前端时间显示和格式化需求
 * 
 * 后端架构匹配：
 * - 高精度定时：基于ESP32-S3硬件定时器的精确时间控制
 * - 统一调度：单一主定时器管理所有定时任务
 * - 性能优化：避免多个定时器造成的性能开销
 * - 时区支持：完整的时区和夏令时处理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef TIME_UTILS_H
#define TIME_UTILS_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <time.h>
#include <sys/time.h>
#include <vector>
#include <functional>

// 配置文件
#include "../config/SystemConfig.h"

// ================================
// 时间常量定义
// ================================

/**
 * 时间常量
 */
namespace TimeConstants {
    const uint32_t MILLISECONDS_PER_SECOND = 1000;
    const uint32_t SECONDS_PER_MINUTE = 60;
    const uint32_t MINUTES_PER_HOUR = 60;
    const uint32_t HOURS_PER_DAY = 24;
    const uint32_t DAYS_PER_WEEK = 7;
    const uint32_t MILLISECONDS_PER_MINUTE = MILLISECONDS_PER_SECOND * SECONDS_PER_MINUTE;
    const uint32_t MILLISECONDS_PER_HOUR = MILLISECONDS_PER_MINUTE * MINUTES_PER_HOUR;
    const uint32_t MILLISECONDS_PER_DAY = MILLISECONDS_PER_HOUR * HOURS_PER_DAY;
    
    // 前端匹配的时间精度
    const uint32_t TIMER_PRECISION_MS = 100;    // 100ms精度
    const uint32_t TIME_SLICE_MS = 8;            // 8ms时间片
}

// ================================
// 时间格式定义
// ================================

/**
 * 时间格式枚举
 */
enum class TimeFormat : uint8_t {
    ISO8601 = 0,                // ISO 8601格式 (2025-01-01T12:00:00Z)
    RFC3339 = 1,                // RFC 3339格式 (2025-01-01T12:00:00+08:00)
    UNIX_TIMESTAMP = 2,         // Unix时间戳 (1735689600)
    HUMAN_READABLE = 3,         // 人类可读格式 (2025年1月1日 12:00:00)
    TIME_ONLY = 4,              // 仅时间 (12:00:00)
    DATE_ONLY = 5,              // 仅日期 (2025-01-01)
    RELATIVE = 6,               // 相对时间 (2小时前)
    DURATION = 7                // 持续时间 (2h 30m 15s)
};

/**
 * 星期枚举
 */
enum class Weekday : uint8_t {
    SUNDAY = 0,
    MONDAY = 1,
    TUESDAY = 2,
    WEDNESDAY = 3,
    THURSDAY = 4,
    FRIDAY = 5,
    SATURDAY = 6
};

// ================================
// 时间结构定义
// ================================

/**
 * 时间信息结构 - 匹配前端时间处理需求
 */
struct TimeInfo {
    uint16_t year;                      // 年份
    uint8_t month;                      // 月份 (1-12)
    uint8_t day;                        // 日期 (1-31)
    uint8_t hour;                       // 小时 (0-23)
    uint8_t minute;                     // 分钟 (0-59)
    uint8_t second;                     // 秒钟 (0-59)
    uint16_t millisecond;               // 毫秒 (0-999)
    Weekday weekday;                    // 星期
    int16_t timezone;                   // 时区偏移（分钟）
    bool isDST;                         // 是否夏令时
    
    /**
     * 构造函数
     */
    TimeInfo() 
        : year(2025), month(1), day(1)
        , hour(0), minute(0), second(0), millisecond(0)
        , weekday(Weekday::MONDAY), timezone(480), isDST(false) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["year"] = year;
        doc["month"] = month;
        doc["day"] = day;
        doc["hour"] = hour;
        doc["minute"] = minute;
        doc["second"] = second;
        doc["millisecond"] = millisecond;
        doc["weekday"] = static_cast<uint8_t>(weekday);
        doc["timezone"] = timezone;
        doc["isDST"] = isDST;
        return doc;
    }
    
    /**
     * 从JSON对象加载
     */
    void fromJson(const JsonDocument& doc) {
        year = doc["year"].as<uint16_t>();
        month = doc["month"].as<uint8_t>();
        day = doc["day"].as<uint8_t>();
        hour = doc["hour"].as<uint8_t>();
        minute = doc["minute"].as<uint8_t>();
        second = doc["second"].as<uint8_t>();
        millisecond = doc["millisecond"].as<uint16_t>();
        weekday = static_cast<Weekday>(doc["weekday"].as<uint8_t>());
        timezone = doc["timezone"].as<int16_t>();
        isDST = doc["isDST"].as<bool>();
    }
};

/**
 * 持续时间结构
 */
struct Duration {
    uint32_t days;                      // 天数
    uint8_t hours;                      // 小时
    uint8_t minutes;                    // 分钟
    uint8_t seconds;                    // 秒钟
    uint16_t milliseconds;              // 毫秒
    
    /**
     * 构造函数
     */
    Duration(uint64_t totalMs = 0) {
        fromMilliseconds(totalMs);
    }
    
    /**
     * 从毫秒数构造
     */
    void fromMilliseconds(uint64_t totalMs) {
        days = totalMs / TimeConstants::MILLISECONDS_PER_DAY;
        totalMs %= TimeConstants::MILLISECONDS_PER_DAY;
        
        hours = totalMs / TimeConstants::MILLISECONDS_PER_HOUR;
        totalMs %= TimeConstants::MILLISECONDS_PER_HOUR;
        
        minutes = totalMs / TimeConstants::MILLISECONDS_PER_MINUTE;
        totalMs %= TimeConstants::MILLISECONDS_PER_MINUTE;
        
        seconds = totalMs / TimeConstants::MILLISECONDS_PER_SECOND;
        milliseconds = totalMs % TimeConstants::MILLISECONDS_PER_SECOND;
    }
    
    /**
     * 转换为毫秒数
     */
    uint64_t toMilliseconds() const {
        return days * TimeConstants::MILLISECONDS_PER_DAY +
               hours * TimeConstants::MILLISECONDS_PER_HOUR +
               minutes * TimeConstants::MILLISECONDS_PER_MINUTE +
               seconds * TimeConstants::MILLISECONDS_PER_SECOND +
               milliseconds;
    }
    
    /**
     * 转换为字符串
     */
    String toString() const {
        String result;
        if (days > 0) result += String(days) + "d ";
        if (hours > 0) result += String(hours) + "h ";
        if (minutes > 0) result += String(minutes) + "m ";
        if (seconds > 0) result += String(seconds) + "s";
        if (result.isEmpty()) result = "0s";
        return result.trim();
    }
};

// ================================
// 时间工具类定义
// ================================

/**
 * 时间工具类 - 完全匹配前端定时任务管理需求
 * 
 * 职责：
 * 1. 高精度时间获取和处理
 * 2. 时间格式化和解析
 * 3. 定时任务调度支持
 * 4. 时区和夏令时处理
 * 5. 性能优化的时间计算
 */
class TimeUtils {
public:
    // ================================
    // 时间获取接口 - 匹配前端时间处理
    // ================================
    
    /**
     * 获取当前时间戳（毫秒） - 匹配前端时间精度
     * @return 当前时间戳
     */
    static uint64_t getCurrentTimestamp();
    
    /**
     * 获取当前时间戳（微秒） - 高精度时间
     * @return 当前时间戳（微秒）
     */
    static uint64_t getCurrentTimestampMicros();
    
    /**
     * 获取系统运行时间（毫秒）
     * @return 系统运行时间
     */
    static uint64_t getUptime();
    
    /**
     * 获取当前时间信息
     * @param timezone 时区偏移（分钟）
     * @return 时间信息结构
     */
    static TimeInfo getCurrentTimeInfo(int16_t timezone = 480);
    
    /**
     * 获取UTC时间信息
     * @return UTC时间信息
     */
    static TimeInfo getUTCTimeInfo();
    
    // ================================
    // 时间格式化接口 - 匹配前端时间显示
    // ================================
    
    /**
     * 格式化时间戳 - 匹配前端时间显示格式
     * @param timestamp 时间戳（毫秒）
     * @param format 时间格式
     * @param timezone 时区偏移（分钟）
     * @return 格式化的时间字符串
     */
    static String formatTimestamp(uint64_t timestamp, TimeFormat format = TimeFormat::ISO8601, int16_t timezone = 480);
    
    /**
     * 格式化时间信息
     * @param timeInfo 时间信息
     * @param format 时间格式
     * @return 格式化的时间字符串
     */
    static String formatTimeInfo(const TimeInfo& timeInfo, TimeFormat format = TimeFormat::ISO8601);
    
    /**
     * 格式化持续时间 - 匹配前端持续时间显示
     * @param duration 持续时间（毫秒）
     * @param format 格式类型
     * @return 格式化的持续时间字符串
     */
    static String formatDuration(uint64_t duration, TimeFormat format = TimeFormat::DURATION);
    
    /**
     * 格式化相对时间 - 匹配前端相对时间显示
     * @param timestamp 时间戳（毫秒）
     * @param baseTimestamp 基准时间戳（毫秒，0表示当前时间）
     * @return 相对时间字符串
     */
    static String formatRelativeTime(uint64_t timestamp, uint64_t baseTimestamp = 0);
    
    // ================================
    // 时间解析接口
    // ================================
    
    /**
     * 解析时间字符串
     * @param timeString 时间字符串
     * @param format 时间格式
     * @return 时间戳（毫秒）
     */
    static uint64_t parseTimeString(const String& timeString, TimeFormat format = TimeFormat::ISO8601);
    
    /**
     * 解析ISO8601时间字符串
     * @param iso8601String ISO8601格式字符串
     * @return 时间戳（毫秒）
     */
    static uint64_t parseISO8601(const String& iso8601String);
    
    /**
     * 解析持续时间字符串
     * @param durationString 持续时间字符串 (如 "2h 30m 15s")
     * @return 持续时间（毫秒）
     */
    static uint64_t parseDuration(const String& durationString);
    
    // ================================
    // 时间计算接口 - 匹配前端定时任务计算
    // ================================
    
    /**
     * 添加时间 - 匹配前端定时任务计算
     * @param timestamp 基准时间戳（毫秒）
     * @param duration 添加的持续时间（毫秒）
     * @return 新的时间戳
     */
    static uint64_t addTime(uint64_t timestamp, uint64_t duration);
    
    /**
     * 减去时间
     * @param timestamp 基准时间戳（毫秒）
     * @param duration 减去的持续时间（毫秒）
     * @return 新的时间戳
     */
    static uint64_t subtractTime(uint64_t timestamp, uint64_t duration);
    
    /**
     * 计算时间差
     * @param timestamp1 时间戳1（毫秒）
     * @param timestamp2 时间戳2（毫秒）
     * @return 时间差（毫秒）
     */
    static uint64_t timeDifference(uint64_t timestamp1, uint64_t timestamp2);
    
    /**
     * 检查时间是否在范围内
     * @param timestamp 检查的时间戳
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否在范围内
     */
    static bool isTimeInRange(uint64_t timestamp, uint64_t startTime, uint64_t endTime);
    
    // ================================
    // 定时任务支持接口 - 匹配前端定时任务
    // ================================
    
    /**
     * 计算下次执行时间 - 匹配前端定时任务调度
     * @param currentTime 当前时间
     * @param interval 执行间隔（毫秒）
     * @param lastExecution 上次执行时间
     * @return 下次执行时间
     */
    static uint64_t calculateNextExecution(uint64_t currentTime, uint64_t interval, uint64_t lastExecution = 0);
    
    /**
     * 检查是否到达执行时间
     * @param targetTime 目标时间
     * @param currentTime 当前时间（0表示使用当前时间）
     * @param tolerance 容差（毫秒）
     * @return 是否到达执行时间
     */
    static bool isExecutionTime(uint64_t targetTime, uint64_t currentTime = 0, uint32_t tolerance = TimeConstants::TIMER_PRECISION_MS);
    
    /**
     * 计算Cron表达式下次执行时间
     * @param cronExpression Cron表达式
     * @param currentTime 当前时间
     * @return 下次执行时间
     */
    static uint64_t calculateCronNextExecution(const String& cronExpression, uint64_t currentTime = 0);
    
    // ================================
    // 时区和夏令时接口
    // ================================
    
    /**
     * 设置系统时区
     * @param timezone 时区偏移（分钟）
     * @param dstEnabled 是否启用夏令时
     * @return 是否设置成功
     */
    static bool setTimezone(int16_t timezone, bool dstEnabled = false);
    
    /**
     * 获取当前时区
     * @return 时区偏移（分钟）
     */
    static int16_t getCurrentTimezone();
    
    /**
     * 转换时区
     * @param timestamp 时间戳（毫秒）
     * @param fromTimezone 源时区偏移（分钟）
     * @param toTimezone 目标时区偏移（分钟）
     * @return 转换后的时间戳
     */
    static uint64_t convertTimezone(uint64_t timestamp, int16_t fromTimezone, int16_t toTimezone);
    
    /**
     * 检查是否为夏令时
     * @param timestamp 时间戳（毫秒）
     * @param timezone 时区偏移（分钟）
     * @return 是否为夏令时
     */
    static bool isDaylightSavingTime(uint64_t timestamp, int16_t timezone);
    
    // ================================
    // 时间验证接口
    // ================================
    
    /**
     * 验证时间信息
     * @param timeInfo 时间信息
     * @return 是否有效
     */
    static bool validateTimeInfo(const TimeInfo& timeInfo);
    
    /**
     * 验证时间戳
     * @param timestamp 时间戳（毫秒）
     * @return 是否有效
     */
    static bool validateTimestamp(uint64_t timestamp);
    
    /**
     * 检查是否为闰年
     * @param year 年份
     * @return 是否为闰年
     */
    static bool isLeapYear(uint16_t year);
    
    /**
     * 获取月份天数
     * @param year 年份
     * @param month 月份
     * @return 天数
     */
    static uint8_t getDaysInMonth(uint16_t year, uint8_t month);
    
    // ================================
    // 性能优化接口
    // ================================
    
    /**
     * 高精度延时 - 匹配前端时间片处理
     * @param microseconds 延时时间（微秒）
     */
    static void delayMicroseconds(uint32_t microseconds);
    
    /**
     * 精确延时 - 匹配前端定时精度
     * @param milliseconds 延时时间（毫秒）
     */
    static void delayMilliseconds(uint32_t milliseconds);
    
    /**
     * 获取高精度时间差
     * @param startTime 开始时间（微秒）
     * @param endTime 结束时间（微秒，0表示当前时间）
     * @return 时间差（微秒）
     */
    static uint64_t getHighPrecisionDuration(uint64_t startTime, uint64_t endTime = 0);
    
    /**
     * 同步系统时间
     * @param ntpServer NTP服务器地址
     * @param timeout 超时时间（毫秒）
     * @return 是否同步成功
     */
    static bool syncSystemTime(const String& ntpServer = "pool.ntp.org", uint32_t timeout = 5000);

private:
    // ================================
    // 私有成员变量
    // ================================
    
    static int16_t currentTimezone;     // 当前时区
    static bool dstEnabled;             // 是否启用夏令时
    static uint64_t systemStartTime;    // 系统启动时间
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 时间戳转时间信息
     * @param timestamp 时间戳（毫秒）
     * @param timezone 时区偏移（分钟）
     * @return 时间信息
     */
    static TimeInfo timestampToTimeInfo(uint64_t timestamp, int16_t timezone);
    
    /**
     * 时间信息转时间戳
     * @param timeInfo 时间信息
     * @return 时间戳（毫秒）
     */
    static uint64_t timeInfoToTimestamp(const TimeInfo& timeInfo);
    
    /**
     * 获取星期
     * @param year 年份
     * @param month 月份
     * @param day 日期
     * @return 星期
     */
    static Weekday getWeekday(uint16_t year, uint8_t month, uint8_t day);
    
    /**
     * 解析Cron字段
     * @param field Cron字段字符串
     * @param min 最小值
     * @param max 最大值
     * @return 匹配的值列表
     */
    static std::vector<uint8_t> parseCronField(const String& field, uint8_t min, uint8_t max);
    
    /**
     * 获取时区名称
     * @param timezone 时区偏移（分钟）
     * @return 时区名称
     */
    static String getTimezoneName(int16_t timezone);
};

#endif // TIME_UTILS_H
