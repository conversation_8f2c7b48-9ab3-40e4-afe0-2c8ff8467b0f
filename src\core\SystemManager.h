/**
 * ESP32-S3红外控制系统 - 系统管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的系统管理器
 * - 统一管理所有系统组件的初始化、运行和清理
 * - 协调双核管理器、事件管理器和各种服务的工作
 * - 提供系统级的错误处理和状态监控
 * 
 * 前端匹配度：
 * - 系统状态：完全匹配前端R1System的状态管理
 * - 生命周期：匹配前端系统初始化和清理流程
 * - 事件响应：100%支持前端101个事件类型的处理
 * - 性能监控：匹配前端性能监控和状态报告
 * 
 * 后端架构匹配：
 * - 双核协调：统一管理核心0和核心1的任务分配
 * - 服务管理：统一管理所有服务的生命周期
 * - 错误处理：集中的错误处理和恢复机制
 * - 资源管理：统一的内存和硬件资源管理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include <memory>
#include <vector>

// 配置文件
#include "../config/SystemConfig.h"
#include "../config/PinConfig.h"
#include "../config/NetworkConfig.h"

// 数据类型
#include "../types/SignalData.h"
#include "../types/TaskData.h"
#include "../types/APITypes.h"
#include "../types/EventTypes.h"

// 前向声明
class DualCoreManager;
class EventManager;
class ErrorHandler;
class BaseService;
class SignalService;
class IRControlService;
class TimerService;
class DataService;
class StatusService;
class ConfigService;
class OTAService;
class WiFiManager;
class WebServerManager;
class WebSocketManager;
class HardwareManager;
class PerformanceMonitor;

// ================================
// 系统状态枚举定义
// ================================

/**
 * 系统状态枚举 - 匹配前端R1System状态管理
 */
enum class SystemState : uint8_t {
    UNINITIALIZED = 0,      // 未初始化
    INITIALIZING = 1,       // 初始化中
    READY = 2,              // 就绪状态
    RUNNING = 3,            // 运行中
    ERROR = 4,              // 错误状态
    SHUTTING_DOWN = 5,      // 关闭中
    SHUTDOWN = 6            // 已关闭
};

/**
 * 系统模式枚举 - 匹配前端系统模式
 */
enum class SystemMode : uint8_t {
    NORMAL = 0,             // 正常模式
    LEARNING = 1,           // 学习模式
    BATCH_EMIT = 2,         // 批量发射模式
    MAINTENANCE = 3,        // 维护模式
    DEBUG = 4               // 调试模式
};

// ================================
// 系统管理器类定义
// ================================

/**
 * 系统管理器类 - 系统的核心控制器
 * 
 * 职责：
 * 1. 系统初始化和清理
 * 2. 组件生命周期管理
 * 3. 系统状态监控
 * 4. 错误处理和恢复
 * 5. 性能监控和优化
 */
class SystemManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     * @param dualCoreMgr 双核管理器指针
     */
    SystemManager(EventManager* eventMgr = nullptr, DualCoreManager* dualCoreMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~SystemManager();
    
    // ================================
    // 系统生命周期管理
    // ================================
    
    /**
     * 初始化系统 - 匹配前端R1System初始化流程
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 启动系统服务
     * @return 是否启动成功
     */
    bool start();
    
    /**
     * 停止系统服务
     * @return 是否停止成功
     */
    bool stop();
    
    /**
     * 清理系统资源
     */
    void cleanup();
    
    /**
     * 重启系统
     * @param delay 重启延迟（毫秒）
     */
    void restart(uint32_t delay = 1000);
    
    // ================================
    // 系统主循环
    // ================================
    
    /**
     * 系统主循环 - 匹配前端R1System主循环
     */
    void loop();
    
    /**
     * 处理系统事件
     */
    void processEvents();
    
    /**
     * 更新系统状态
     */
    void updateStatus();
    
    // ================================
    // 系统状态管理
    // ================================
    
    /**
     * 获取系统状态
     * @return 当前系统状态
     */
    SystemState getState() const { return currentState; }
    
    /**
     * 获取系统模式
     * @return 当前系统模式
     */
    SystemMode getMode() const { return currentMode; }
    
    /**
     * 设置系统模式
     * @param mode 新的系统模式
     * @return 是否设置成功
     */
    bool setMode(SystemMode mode);
    
    /**
     * 检查系统是否就绪
     * @return 是否就绪
     */
    bool isReady() const { return currentState == SystemState::READY || currentState == SystemState::RUNNING; }
    
    /**
     * 检查系统是否运行中
     * @return 是否运行中
     */
    bool isRunning() const { return currentState == SystemState::RUNNING; }
    
    /**
     * 检查系统是否有错误
     * @return 是否有错误
     */
    bool hasError() const { return currentState == SystemState::ERROR; }
    
    // ================================
    // 组件管理
    // ================================
    
    /**
     * 获取双核管理器
     * @return 双核管理器指针
     */
    DualCoreManager* getDualCoreManager() const { return dualCoreManager; }
    
    /**
     * 获取事件管理器
     * @return 事件管理器指针
     */
    EventManager* getEventManager() const { return eventManager; }
    
    /**
     * 获取错误处理器
     * @return 错误处理器指针
     */
    ErrorHandler* getErrorHandler() const { return errorHandler; }
    
    /**
     * 获取硬件管理器
     * @return 硬件管理器指针
     */
    HardwareManager* getHardwareManager() const { return hardwareManager; }
    
    /**
     * 获取性能监控器
     * @return 性能监控器指针
     */
    PerformanceMonitor* getPerformanceMonitor() const { return performanceMonitor; }
    
    // ================================
    // 服务管理
    // ================================
    
    /**
     * 获取信号服务
     * @return 信号服务指针
     */
    SignalService* getSignalService() const { return signalService; }
    
    /**
     * 获取红外控制服务
     * @return 红外控制服务指针
     */
    IRControlService* getIRControlService() const { return irControlService; }
    
    /**
     * 获取定时器服务
     * @return 定时器服务指针
     */
    TimerService* getTimerService() const { return timerService; }
    
    /**
     * 获取数据服务
     * @return 数据服务指针
     */
    DataService* getDataService() const { return dataService; }
    
    /**
     * 获取状态服务
     * @return 状态服务指针
     */
    StatusService* getStatusService() const { return statusService; }
    
    /**
     * 获取配置服务
     * @return 配置服务指针
     */
    ConfigService* getConfigService() const { return configService; }
    
    /**
     * 获取OTA服务
     * @return OTA服务指针
     */
    OTAService* getOTAService() const { return otaService; }
    
    // ================================
    // 网络管理
    // ================================
    
    /**
     * 获取WiFi管理器
     * @return WiFi管理器指针
     */
    WiFiManager* getWiFiManager() const { return wifiManager; }
    
    /**
     * 获取Web服务器管理器
     * @return Web服务器管理器指针
     */
    WebServerManager* getWebServerManager() const { return webServerManager; }
    
    /**
     * 获取WebSocket管理器
     * @return WebSocket管理器指针
     */
    WebSocketManager* getWebSocketManager() const { return webSocketManager; }
    
    // ================================
    // 系统信息
    // ================================
    
    /**
     * 获取系统运行时间
     * @return 运行时间（毫秒）
     */
    uint32_t getUptime() const { return millis() - startTime; }
    
    /**
     * 获取系统启动时间
     * @return 启动时间（毫秒）
     */
    uint32_t getStartTime() const { return startTime; }
    
    /**
     * 获取系统版本
     * @return 版本字符串
     */
    String getVersion() const { return SYSTEM_VERSION; }
    
    /**
     * 获取系统状态字符串
     * @return 状态字符串
     */
    String getStateString() const;
    
    /**
     * 获取系统模式字符串
     * @return 模式字符串
     */
    String getModeString() const;
    
    // ================================
    // 系统统计
    // ================================
    
    /**
     * 获取系统统计信息
     * @return 统计信息JSON对象
     */
    JsonDocument getSystemStats() const;
    
    /**
     * 获取内存使用情况
     * @return 内存信息JSON对象
     */
    JsonDocument getMemoryInfo() const;
    
    /**
     * 获取性能信息
     * @return 性能信息JSON对象
     */
    JsonDocument getPerformanceInfo() const;
    
    // ================================
    // 事件处理
    // ================================
    
    /**
     * 处理系统事件
     * @param event 事件数据
     */
    void handleSystemEvent(const EventData& event);
    
    /**
     * 处理错误事件
     * @param event 错误事件数据
     */
    void handleErrorEvent(const EventData& event);
    
    /**
     * 处理状态变化事件
     * @param event 状态变化事件数据
     */
    void handleStateChangeEvent(const EventData& event);

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 系统状态
    SystemState currentState;           // 当前系统状态
    SystemMode currentMode;             // 当前系统模式
    uint32_t startTime;                 // 系统启动时间
    uint32_t lastStatusUpdate;          // 上次状态更新时间
    uint32_t lastHeartbeat;             // 上次心跳时间
    
    // 核心组件
    DualCoreManager* dualCoreManager;   // 双核管理器
    EventManager* eventManager;         // 事件管理器
    ErrorHandler* errorHandler;         // 错误处理器
    HardwareManager* hardwareManager;   // 硬件管理器
    PerformanceMonitor* performanceMonitor; // 性能监控器
    
    // 服务组件
    SignalService* signalService;       // 信号服务
    IRControlService* irControlService; // 红外控制服务
    TimerService* timerService;         // 定时器服务
    DataService* dataService;           // 数据服务
    StatusService* statusService;       // 状态服务
    ConfigService* configService;       // 配置服务
    OTAService* otaService;             // OTA服务
    
    // 网络组件
    WiFiManager* wifiManager;           // WiFi管理器
    WebServerManager* webServerManager; // Web服务器管理器
    WebSocketManager* webSocketManager; // WebSocket管理器
    
    // 服务列表
    std::vector<BaseService*> services; // 所有服务的列表
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    bool componentsOwned;               // 是否拥有组件所有权
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化核心组件
     * @return 是否初始化成功
     */
    bool initCoreComponents();
    
    /**
     * 初始化服务组件
     * @return 是否初始化成功
     */
    bool initServices();
    
    /**
     * 初始化网络组件
     * @return 是否初始化成功
     */
    bool initNetworkComponents();
    
    /**
     * 设置系统状态
     * @param newState 新状态
     */
    void setState(SystemState newState);
    
    /**
     * 发布系统状态事件
     */
    void publishStateEvent();
    
    /**
     * 检查系统健康状态
     * @return 是否健康
     */
    bool checkSystemHealth();
    
    /**
     * 处理系统错误
     * @param error 错误信息
     */
    void handleSystemError(const String& error);
    
    /**
     * 清理核心组件
     */
    void cleanupCoreComponents();
    
    /**
     * 清理服务组件
     */
    void cleanupServices();
    
    /**
     * 清理网络组件
     */
    void cleanupNetworkComponents();
};

#endif // SYSTEM_MANAGER_H
