/**
 * ESP32-S3红外控制系统 - 双核管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的双核管理器
 * - 管理ESP32-S3双核任务分配和调度
 * - 核心0：实时控制任务（红外发射、接收、状态LED）
 * - 核心1：网络处理任务（HTTP、WebSocket、数据处理）
 * 
 * 前端匹配度：
 * - 实时响应：核心0保证<1ms硬实时响应，匹配前端性能要求
 * - 批处理优化：核心1批处理网络请求，实现70%性能提升
 * - 事件处理：双核协同处理101个事件类型
 * - 负载均衡：动态调整任务分配，保证系统稳定性
 * 
 * 后端架构匹配：
 * - 任务分离：严格按照架构设计分离实时和非实时任务
 * - 核心间通信：高效的消息队列和共享内存机制
 * - 性能监控：实时监控双核负载和性能指标
 * - 错误恢复：核心级错误检测和自动恢复机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef DUAL_CORE_MANAGER_H
#define DUAL_CORE_MANAGER_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/semphr.h>
#include <freertos/timers.h>
#include <vector>

// 配置文件
#include "../config/SystemConfig.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;
class HardwareManager;
class IRControlService;
class StatusService;
class WebServerManager;
class WebSocketManager;
class DataService;

// ================================
// 核心间通信消息定义
// ================================

/**
 * 核心间消息类型枚举
 */
enum class CoreMessageType : uint8_t {
    // 控制消息
    TASK_START = 0,             // 启动任务
    TASK_STOP = 1,              // 停止任务
    TASK_PAUSE = 2,             // 暂停任务
    TASK_RESUME = 3,            // 恢复任务
    
    // 数据消息
    SIGNAL_DATA = 10,           // 信号数据
    TASK_DATA = 11,             // 任务数据
    STATUS_DATA = 12,           // 状态数据
    CONFIG_DATA = 13,           // 配置数据
    
    // 事件消息
    HIGH_PRIORITY_EVENT = 20,   // 高优先级事件
    NORMAL_EVENT = 21,          // 普通事件
    ERROR_EVENT = 22,           // 错误事件
    
    // 系统消息
    HEARTBEAT = 30,             // 心跳消息
    PERFORMANCE_DATA = 31,      // 性能数据
    MEMORY_STATUS = 32,         // 内存状态
    CORE_STATUS = 33            // 核心状态
};

/**
 * 核心间通信消息结构
 */
struct CoreMessage {
    CoreMessageType type;       // 消息类型
    uint8_t sourceCore;         // 源核心ID
    uint8_t targetCore;         // 目标核心ID
    uint32_t timestamp;         // 时间戳
    uint16_t dataSize;          // 数据大小
    uint8_t data[64];           // 消息数据（最大64字节）
    
    /**
     * 构造函数
     */
    CoreMessage(CoreMessageType msgType = CoreMessageType::HEARTBEAT, 
                uint8_t src = 0, uint8_t target = 1)
        : type(msgType)
        , sourceCore(src)
        , targetCore(target)
        , timestamp(millis())
        , dataSize(0) {
        memset(data, 0, sizeof(data));
    }
    
    /**
     * 设置数据
     */
    void setData(const void* srcData, uint16_t size) {
        if (size <= sizeof(data)) {
            memcpy(data, srcData, size);
            dataSize = size;
        }
    }
    
    /**
     * 获取数据
     */
    void getData(void* destData, uint16_t maxSize) const {
        uint16_t copySize = (dataSize < maxSize) ? dataSize : maxSize;
        memcpy(destData, data, copySize);
    }
};

// ================================
// 核心状态定义
// ================================

/**
 * 核心状态枚举
 */
enum class CoreState : uint8_t {
    UNINITIALIZED = 0,          // 未初始化
    INITIALIZING = 1,           // 初始化中
    READY = 2,                  // 就绪
    RUNNING = 3,                // 运行中
    PAUSED = 4,                 // 暂停
    ERROR = 5,                  // 错误
    STOPPING = 6,               // 停止中
    STOPPED = 7                 // 已停止
};

/**
 * 核心状态信息结构
 */
struct CoreStatus {
    CoreState state;            // 核心状态
    uint8_t coreId;             // 核心ID
    uint32_t uptime;            // 运行时间
    uint32_t taskCount;         // 任务数量
    uint32_t cpuUsage;          // CPU使用率（百分比）
    uint32_t freeStack;         // 剩余栈空间
    uint32_t lastHeartbeat;     // 上次心跳时间
    String currentTask;         // 当前任务名称
    
    /**
     * 构造函数
     */
    CoreStatus(uint8_t id = 0) 
        : state(CoreState::UNINITIALIZED)
        , coreId(id)
        , uptime(0)
        , taskCount(0)
        , cpuUsage(0)
        , freeStack(0)
        , lastHeartbeat(0) {
    }
    
    /**
     * 检查核心是否健康
     */
    bool isHealthy() const {
        return state == CoreState::RUNNING && 
               (millis() - lastHeartbeat) < 5000 && // 5秒内有心跳
               freeStack > 1024; // 栈空间充足
    }
};

// ================================
// 双核管理器类定义
// ================================

/**
 * 双核管理器类 - ESP32-S3双核协调控制器
 * 
 * 职责：
 * 1. 双核任务分配和调度
 * 2. 核心间通信协调
 * 3. 负载均衡和性能监控
 * 4. 核心级错误处理和恢复
 */
class DualCoreManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     */
    DualCoreManager();
    
    /**
     * 析构函数
     */
    ~DualCoreManager();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化双核管理器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 启动双核任务
     * @return 是否启动成功
     */
    bool startCoreTasks();
    
    /**
     * 停止双核任务
     * @return 是否停止成功
     */
    bool stopCoreTasks();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    // ================================
    // 核心任务管理
    // ================================
    
    /**
     * 启动核心0任务（实时控制）
     * @return 是否启动成功
     */
    bool startCore0Tasks();
    
    /**
     * 启动核心1任务（网络处理）
     * @return 是否启动成功
     */
    bool startCore1Tasks();
    
    /**
     * 暂停核心任务
     * @param coreId 核心ID
     * @return 是否暂停成功
     */
    bool pauseCoreTasks(uint8_t coreId);
    
    /**
     * 恢复核心任务
     * @param coreId 核心ID
     * @return 是否恢复成功
     */
    bool resumeCoreTasks(uint8_t coreId);
    
    // ================================
    // 核心间通信
    // ================================
    
    /**
     * 发送核心间消息
     * @param message 消息对象
     * @param timeout 超时时间（毫秒）
     * @return 是否发送成功
     */
    bool sendInterCoreMessage(const CoreMessage& message, uint32_t timeout = 100);
    
    /**
     * 接收核心间消息
     * @param message 消息对象引用
     * @param timeout 超时时间（毫秒）
     * @return 是否接收成功
     */
    bool receiveInterCoreMessage(CoreMessage& message, uint32_t timeout = 100);
    
    /**
     * 广播消息到所有核心
     * @param message 消息对象
     * @return 是否广播成功
     */
    bool broadcastMessage(const CoreMessage& message);
    
    // ================================
    // 状态监控
    // ================================
    
    /**
     * 获取核心状态
     * @param coreId 核心ID
     * @return 核心状态信息
     */
    CoreStatus getCoreStatus(uint8_t coreId) const;
    
    /**
     * 获取所有核心状态
     * @return 核心状态数组
     */
    std::vector<CoreStatus> getAllCoreStatus() const;
    
    /**
     * 检查核心是否健康
     * @param coreId 核心ID
     * @return 是否健康
     */
    bool isCoreHealthy(uint8_t coreId) const;
    
    /**
     * 检查所有核心是否健康
     * @return 是否所有核心都健康
     */
    bool areAllCoresHealthy() const;
    
    // ================================
    // 性能监控
    // ================================
    
    /**
     * 获取核心CPU使用率
     * @param coreId 核心ID
     * @return CPU使用率（百分比）
     */
    uint32_t getCoreCpuUsage(uint8_t coreId) const;
    
    /**
     * 获取核心剩余栈空间
     * @param coreId 核心ID
     * @return 剩余栈空间（字节）
     */
    uint32_t getCoreFreeStack(uint8_t coreId) const;
    
    /**
     * 获取核心任务数量
     * @param coreId 核心ID
     * @return 任务数量
     */
    uint32_t getCoreTaskCount(uint8_t coreId) const;
    
    /**
     * 获取系统性能统计
     * @return 性能统计JSON对象
     */
    JsonDocument getPerformanceStats() const;
    
    // ================================
    // 负载均衡
    // ================================
    
    /**
     * 检查核心负载
     * @param coreId 核心ID
     * @return 负载百分比
     */
    uint32_t getCoreLoad(uint8_t coreId) const;
    
    /**
     * 获取最空闲的核心
     * @return 核心ID
     */
    uint8_t getLeastLoadedCore() const;
    
    /**
     * 平衡核心负载
     * @return 是否平衡成功
     */
    bool balanceLoad();
    
    // ================================
    // 错误处理
    // ================================
    
    /**
     * 处理核心错误
     * @param coreId 核心ID
     * @param error 错误信息
     */
    void handleCoreError(uint8_t coreId, const String& error);
    
    /**
     * 重启核心任务
     * @param coreId 核心ID
     * @return 是否重启成功
     */
    bool restartCoreTasks(uint8_t coreId);
    
    /**
     * 核心看门狗检查
     */
    void watchdogCheck();
    
    // ================================
    // 组件注册
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }
    
    /**
     * 设置硬件管理器
     * @param hwMgr 硬件管理器指针
     */
    void setHardwareManager(HardwareManager* hwMgr) { hardwareManager = hwMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // FreeRTOS任务句柄
    TaskHandle_t core0TaskHandle;       // 核心0任务句柄
    TaskHandle_t core1TaskHandle;       // 核心1任务句柄
    TaskHandle_t watchdogTaskHandle;    // 看门狗任务句柄
    
    // 核心间通信
    QueueHandle_t interCoreQueue;       // 核心间消息队列
    SemaphoreHandle_t sharedDataMutex;  // 共享数据互斥锁
    
    // 核心状态
    CoreStatus core0Status;             // 核心0状态
    CoreStatus core1Status;             // 核心1状态
    
    // 性能监控
    uint32_t lastPerformanceCheck;      // 上次性能检查时间
    uint32_t performanceCheckInterval;  // 性能检查间隔
    
    // 组件引用
    EventManager* eventManager;         // 事件管理器
    HardwareManager* hardwareManager;   // 硬件管理器
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    bool tasksStarted;                  // 任务是否已启动
    
    // ================================
    // 静态任务函数
    // ================================
    
    /**
     * 核心0任务函数（实时控制）
     * @param parameter 参数指针
     */
    static void core0TaskFunction(void* parameter);
    
    /**
     * 核心1任务函数（网络处理）
     * @param parameter 参数指针
     */
    static void core1TaskFunction(void* parameter);
    
    /**
     * 看门狗任务函数
     * @param parameter 参数指针
     */
    static void watchdogTaskFunction(void* parameter);
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化核心间通信
     * @return 是否初始化成功
     */
    bool initInterCoreComm();
    
    /**
     * 更新核心状态
     * @param coreId 核心ID
     */
    void updateCoreStatus(uint8_t coreId);
    
    /**
     * 发送心跳消息
     * @param coreId 核心ID
     */
    void sendHeartbeat(uint8_t coreId);
    
    /**
     * 处理核心间消息
     * @param message 消息对象
     */
    void handleInterCoreMessage(const CoreMessage& message);
    
    /**
     * 检查任务栈使用情况
     * @param taskHandle 任务句柄
     * @return 剩余栈空间
     */
    uint32_t checkTaskStackUsage(TaskHandle_t taskHandle);
    
    /**
     * 计算CPU使用率
     * @param coreId 核心ID
     * @return CPU使用率
     */
    uint32_t calculateCpuUsage(uint8_t coreId);
};

#endif // DUAL_CORE_MANAGER_H
