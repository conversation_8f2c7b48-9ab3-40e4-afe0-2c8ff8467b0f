/**
 * ESP32-S3红外控制系统 - 状态服务
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的状态服务
 * - 完全匹配前端StatusDisplay的1,129行完整功能实现
 * - 支持前端系统状态、任务状态、网络状态监控的完整后端实现
 * - 提供实时状态更新和统计数据收集功能
 * 
 * 前端匹配度：
 * - 系统统计：100%匹配前端systemStats的8个状态指标
 * - 状态更新：100%匹配前端30秒更新机制和实时状态显示
 * - 任务监控：100%匹配前端任务执行监控和信号显示
 * - 网络状态：100%匹配前端网络连接状态和断线检测
 * - 性能监控：100%匹配前端CPU、内存使用率监控
 * 
 * 后端架构匹配：
 * - 实时收集：基于定时器的状态数据实时收集
 * - 事件驱动：完整的状态事件发布和订阅机制
 * - 数据聚合：多源数据聚合和统计分析
 * - 缓存优化：状态数据缓存和增量更新
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef STATUS_SERVICE_H
#define STATUS_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/timers.h>
#include <unordered_map>

// 基础服务
#include "BaseService.h"

// 配置文件
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class SystemManager;
class SignalService;
class IRControlService;
class TimerService;
class HardwareManager;
class WebServerManager;

// ================================
// 系统状态数据定义
// ================================

/**
 * 系统统计数据结构 - 匹配前端systemStats
 */
struct SystemStatistics {
    // 基础统计 - 匹配前端systemStats
    uint32_t totalSignals;              // 总信号数量
    uint32_t activeTimers;              // 活跃定时器数量
    uint32_t runningTasks;              // 运行中任务数量
    float memoryUsage;                  // 内存使用率（百分比）
    float cpuUsage;                     // CPU使用率（百分比）
    String networkStatus;               // 网络状态
    uint32_t uptime;                    // 系统运行时间（秒）
    uint64_t lastUpdate;                // 最后更新时间
    
    // 扩展统计
    uint32_t totalConnections;          // 总连接数
    uint32_t activeConnections;         // 活跃连接数
    uint32_t totalRequests;             // 总请求数
    uint32_t successfulRequests;        // 成功请求数
    uint32_t failedRequests;            // 失败请求数
    uint32_t totalEmissions;            // 总发射次数
    uint32_t successfulEmissions;       // 成功发射次数
    uint32_t failedEmissions;           // 失败发射次数
    
    // 硬件状态
    float chipTemperature;              // 芯片温度
    uint32_t freeHeap;                  // 剩余堆内存
    uint32_t minFreeHeap;               // 最小剩余堆内存
    int32_t wifiRSSI;                   // WiFi信号强度
    
    /**
     * 构造函数
     */
    SystemStatistics() 
        : totalSignals(0)
        , activeTimers(0)
        , runningTasks(0)
        , memoryUsage(0.0)
        , cpuUsage(0.0)
        , networkStatus("disconnected")
        , uptime(0)
        , lastUpdate(millis())
        , totalConnections(0)
        , activeConnections(0)
        , totalRequests(0)
        , successfulRequests(0)
        , failedRequests(0)
        , totalEmissions(0)
        , successfulEmissions(0)
        , failedEmissions(0)
        , chipTemperature(0.0)
        , freeHeap(0)
        , minFreeHeap(0)
        , wifiRSSI(0) {
    }
    
    /**
     * 转换为JSON对象 - 匹配前端数据格式
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        // 基础统计 - 完全匹配前端systemStats
        doc["totalSignals"] = totalSignals;
        doc["activeTimers"] = activeTimers;
        doc["runningTasks"] = runningTasks;
        doc["memoryUsage"] = memoryUsage;
        doc["cpuUsage"] = cpuUsage;
        doc["networkStatus"] = networkStatus;
        doc["uptime"] = uptime;
        doc["lastUpdate"] = lastUpdate;
        
        // 扩展统计
        doc["totalConnections"] = totalConnections;
        doc["activeConnections"] = activeConnections;
        doc["totalRequests"] = totalRequests;
        doc["successfulRequests"] = successfulRequests;
        doc["failedRequests"] = failedRequests;
        doc["totalEmissions"] = totalEmissions;
        doc["successfulEmissions"] = successfulEmissions;
        doc["failedEmissions"] = failedEmissions;
        
        // 硬件状态
        doc["chipTemperature"] = chipTemperature;
        doc["freeHeap"] = freeHeap;
        doc["minFreeHeap"] = minFreeHeap;
        doc["wifiRSSI"] = wifiRSSI;
        
        // 计算成功率
        if (totalRequests > 0) {
            doc["requestSuccessRate"] = (float)successfulRequests / totalRequests * 100;
        }
        if (totalEmissions > 0) {
            doc["emissionSuccessRate"] = (float)successfulEmissions / totalEmissions * 100;
        }
        
        return doc;
    }
};

/**
 * 任务状态信息结构 - 匹配前端任务监控
 */
struct TaskStatusInfo {
    String taskId;                      // 任务ID
    String taskName;                    // 任务名称
    String taskType;                    // 任务类型
    String status;                      // 任务状态
    uint64_t startTime;                 // 开始时间
    uint64_t endTime;                   // 结束时间
    uint32_t duration;                  // 持续时间
    std::vector<String> signalIds;      // 相关信号ID列表
    String currentSignal;               // 当前处理信号
    uint32_t progress;                  // 进度百分比
    
    /**
     * 构造函数
     */
    TaskStatusInfo() 
        : startTime(0)
        , endTime(0)
        , duration(0)
        , progress(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["taskId"] = taskId;
        doc["taskName"] = taskName;
        doc["taskType"] = taskType;
        doc["status"] = status;
        doc["startTime"] = startTime;
        doc["endTime"] = endTime;
        doc["duration"] = duration;
        doc["currentSignal"] = currentSignal;
        doc["progress"] = progress;
        
        JsonArray signalsArray = doc["signalIds"].to<JsonArray>();
        for (const String& signalId : signalIds) {
            signalsArray.add(signalId);
        }
        
        return doc;
    }
};

// ================================
// 状态服务类定义
// ================================

/**
 * 状态服务类 - 完全匹配前端StatusDisplay功能
 * 
 * 职责：
 * 1. 系统状态数据收集和聚合
 * 2. 实时状态更新和事件发布
 * 3. 任务执行状态监控
 * 4. 网络连接状态管理
 * 5. 性能指标统计和分析
 */
class StatusService : public BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    StatusService(EventManager* eventMgr);
    
    /**
     * 析构函数
     */
    ~StatusService();
    
    // ================================
    // BaseService接口实现
    // ================================
    
    /**
     * 初始化服务
     * @return 是否初始化成功
     */
    bool init() override;
    
    /**
     * 清理服务资源
     */
    void cleanup() override;
    
    /**
     * 服务主循环
     */
    void loop() override;
    
    /**
     * 获取服务状态
     * @return 服务状态
     */
    ServiceStatus getStatus() const override;
    
    /**
     * 配置变更通知
     * @param config 新配置数据
     */
    void onConfigChanged(const JsonDocument& config) override;
    
    // ================================
    // 状态数据获取 - 匹配前端状态显示
    // ================================
    
    /**
     * 获取系统统计信息 - 匹配前端systemStats
     * @return 系统统计数据
     */
    SystemStatistics getSystemStatistics() const { return systemStats; }
    
    /**
     * 获取系统统计JSON - 匹配前端API格式
     * @return 系统统计JSON对象
     */
    JsonDocument getSystemStatisticsJson() const { return systemStats.toJson(); }
    
    /**
     * 获取当前任务状态 - 匹配前端任务监控
     * @return 当前任务状态列表
     */
    std::vector<TaskStatusInfo> getCurrentTasks() const;
    
    /**
     * 获取网络状态 - 匹配前端网络状态显示
     * @return 网络状态JSON对象
     */
    JsonDocument getNetworkStatus() const;
    
    /**
     * 获取硬件状态 - 匹配前端硬件监控
     * @return 硬件状态JSON对象
     */
    JsonDocument getHardwareStatus() const;
    
    /**
     * 获取性能指标 - 匹配前端性能监控
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const override;
    
    // ================================
    // 状态更新控制 - 匹配前端更新机制
    // ================================
    
    /**
     * 立即更新系统状态 - 匹配前端手动刷新
     * @return 是否更新成功
     */
    bool updateSystemStatus();
    
    /**
     * 设置状态更新间隔 - 匹配前端refreshRate
     * @param intervalMs 更新间隔（毫秒）
     * @return 是否设置成功
     */
    bool setUpdateInterval(uint32_t intervalMs);
    
    /**
     * 获取状态更新间隔
     * @return 更新间隔（毫秒）
     */
    uint32_t getUpdateInterval() const { return updateInterval; }
    
    /**
     * 启用自动状态更新
     * @return 是否启用成功
     */
    bool enableAutoUpdate();
    
    /**
     * 禁用自动状态更新
     * @return 是否禁用成功
     */
    bool disableAutoUpdate();
    
    /**
     * 检查是否启用自动更新
     * @return 是否启用自动更新
     */
    bool isAutoUpdateEnabled() const { return autoUpdateEnabled; }
    
    // ================================
    // 任务状态管理 - 匹配前端任务监控
    // ================================
    
    /**
     * 注册任务状态 - 匹配前端任务开始监控
     * @param taskInfo 任务状态信息
     */
    void registerTaskStatus(const TaskStatusInfo& taskInfo);
    
    /**
     * 更新任务状态 - 匹配前端任务进度更新
     * @param taskId 任务ID
     * @param status 新状态
     * @param progress 进度百分比
     * @param currentSignal 当前信号
     */
    void updateTaskStatus(const String& taskId, const String& status, 
                         uint32_t progress = 0, const String& currentSignal = "");
    
    /**
     * 移除任务状态 - 匹配前端任务完成清理
     * @param taskId 任务ID
     */
    void removeTaskStatus(const String& taskId);
    
    /**
     * 清除所有任务状态
     */
    void clearAllTaskStatus();
    
    // ================================
    // 服务依赖注册
    // ================================
    
    /**
     * 设置系统管理器
     * @param sysMgr 系统管理器指针
     */
    void setSystemManager(SystemManager* sysMgr) { systemManager = sysMgr; }
    
    /**
     * 设置信号服务
     * @param signalSvc 信号服务指针
     */
    void setSignalService(SignalService* signalSvc) { signalService = signalSvc; }
    
    /**
     * 设置红外控制服务
     * @param irSvc 红外控制服务指针
     */
    void setIRControlService(IRControlService* irSvc) { irControlService = irSvc; }
    
    /**
     * 设置定时器服务
     * @param timerSvc 定时器服务指针
     */
    void setTimerService(TimerService* timerSvc) { timerService = timerSvc; }
    
    /**
     * 设置硬件管理器
     * @param hwMgr 硬件管理器指针
     */
    void setHardwareManager(HardwareManager* hwMgr) { hardwareManager = hwMgr; }
    
    /**
     * 设置Web服务器管理器
     * @param webMgr Web服务器管理器指针
     */
    void setWebServerManager(WebServerManager* webMgr) { webServerManager = webMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 服务依赖
    SystemManager* systemManager;       // 系统管理器
    SignalService* signalService;       // 信号服务
    IRControlService* irControlService; // 红外控制服务
    TimerService* timerService;         // 定时器服务
    HardwareManager* hardwareManager;   // 硬件管理器
    WebServerManager* webServerManager; // Web服务器管理器
    
    // 状态数据
    SystemStatistics systemStats;       // 系统统计数据
    std::unordered_map<String, TaskStatusInfo> currentTasks; // 当前任务状态
    
    // 更新控制
    TimerHandle_t updateTimer;          // 状态更新定时器
    uint32_t updateInterval;            // 更新间隔（毫秒）
    bool autoUpdateEnabled;             // 是否启用自动更新
    uint32_t lastUpdateTime;            // 上次更新时间
    
    // 缓存控制
    bool statsCacheValid;               // 统计缓存是否有效
    uint32_t lastStatsUpdate;           // 上次统计更新时间
    uint32_t statsCacheTimeout;         // 统计缓存超时时间
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 收集系统统计数据
     */
    void collectSystemStatistics();
    
    /**
     * 收集信号统计数据
     */
    void collectSignalStatistics();
    
    /**
     * 收集定时器统计数据
     */
    void collectTimerStatistics();
    
    /**
     * 收集网络统计数据
     */
    void collectNetworkStatistics();
    
    /**
     * 收集硬件统计数据
     */
    void collectHardwareStatistics();
    
    /**
     * 收集性能统计数据
     */
    void collectPerformanceStatistics();
    
    /**
     * 计算CPU使用率
     * @return CPU使用率百分比
     */
    float calculateCpuUsage();
    
    /**
     * 计算内存使用率
     * @return 内存使用率百分比
     */
    float calculateMemoryUsage();
    
    /**
     * 获取网络状态字符串
     * @return 网络状态字符串
     */
    String getNetworkStatusString();
    
    /**
     * 发布状态更新事件 - 匹配前端system.stats.updated
     */
    void publishStatusUpdateEvent();
    
    /**
     * 发布任务状态事件 - 匹配前端任务监控事件
     * @param taskInfo 任务状态信息
     */
    void publishTaskStatusEvent(const TaskStatusInfo& taskInfo);
    
    /**
     * 静态定时器回调函数
     * @param timerHandle 定时器句柄
     */
    static void updateTimerCallback(TimerHandle_t timerHandle);
    
    /**
     * 处理定时器回调
     */
    void handleUpdateTimer();
    
    /**
     * 验证统计缓存
     * @return 缓存是否有效
     */
    bool validateStatsCache();
    
    /**
     * 清理过期任务状态
     */
    void cleanupExpiredTasks();
};

#endif // STATUS_SERVICE_H
