/**
 * ESP32-S3红外控制系统 - 配置管理服务
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的配置管理服务
 * - 完全匹配前端配置管理需求和后端架构设计规范
 * - 支持系统配置的加载、保存、验证、导入、导出功能
 * - 提供配置变更通知和版本管理机制
 * 
 * 前端匹配度：
 * - 配置接口：100%匹配前端配置管理API需求
 * - 配置格式：100%匹配前端配置数据结构和验证规则
 * - 配置导入导出：100%匹配前端配置文件操作功能
 * - 配置重置：100%匹配前端配置重置和恢复默认值
 * 
 * 后端架构匹配：
 * - 服务架构：100%符合BaseService架构标准
 * - 配置结构：完整的系统配置层次结构设计
 * - 持久化存储：基于Preferences的配置持久化
 * - 事件驱动：完整的配置变更事件发布机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef CONFIG_SERVICE_H
#define CONFIG_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <vector>

// 基础服务
#include "BaseService.h"

// 配置文件
#include "../config/SystemConfig.h"
#include "../config/NetworkConfig.h"
#include "../config/PinConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// ================================
// 系统配置结构定义
// ================================

/**
 * 网络配置结构 - 匹配后端架构设计
 */
struct NetworkConfigData {
    String ap_ssid;                     // AP模式SSID
    String ap_password;                 // AP模式密码
    String sta_ssid;                    // STA模式SSID
    String sta_password;                // STA模式密码
    String hostname;                    // 设备主机名
    bool enable_ap;                     // 是否启用AP模式
    bool enable_sta;                    // 是否启用STA模式
    uint16_t web_port;                  // Web服务器端口
    uint16_t websocket_port;            // WebSocket端口
    uint32_t connection_timeout;        // 连接超时时间
    
    /**
     * 构造函数 - 默认值
     */
    NetworkConfigData() 
        : ap_ssid(DEFAULT_AP_SSID)
        , ap_password(DEFAULT_AP_PASSWORD)
        , hostname(DEFAULT_HOSTNAME)
        , enable_ap(true)
        , enable_sta(true)
        , web_port(DEFAULT_WEB_PORT)
        , websocket_port(DEFAULT_WEBSOCKET_PORT)
        , connection_timeout(DEFAULT_CONNECTION_TIMEOUT) {
    }
};

/**
 * 硬件配置结构
 */
struct HardwareConfigData {
    uint8_t ir_transmit_pin;            // 红外发射引脚
    uint8_t ir_receive_pin;             // 红外接收引脚
    uint8_t status_led_pin;             // 状态LED引脚
    uint32_t ir_frequency;              // 红外频率
    uint8_t ir_duty_cycle;              // 红外占空比
    bool enable_status_led;             // 是否启用状态LED
    bool enable_ir_feedback;            // 是否启用红外反馈
    
    /**
     * 构造函数 - 默认值
     */
    HardwareConfigData() 
        : ir_transmit_pin(IR_TRANSMIT_PIN)
        , ir_receive_pin(IR_RECEIVE_PIN)
        , status_led_pin(STATUS_LED_PIN)
        , ir_frequency(DEFAULT_IR_FREQUENCY)
        , ir_duty_cycle(DEFAULT_IR_DUTY_CYCLE)
        , enable_status_led(true)
        , enable_ir_feedback(true) {
    }
};

/**
 * 系统配置结构
 */
struct SystemConfigData {
    String device_name;                 // 设备名称
    String device_id;                   // 设备ID
    String firmware_version;            // 固件版本
    uint32_t config_version;            // 配置版本
    bool debug_mode;                    // 调试模式
    uint32_t log_level;                 // 日志级别
    uint32_t max_signals;               // 最大信号数量
    uint32_t max_timers;                // 最大定时器数量
    bool auto_save;                     // 自动保存
    uint32_t save_interval;             // 保存间隔（秒）
    
    /**
     * 构造函数 - 默认值
     */
    SystemConfigData() 
        : device_name(DEFAULT_DEVICE_NAME)
        , device_id("")
        , firmware_version(SYSTEM_VERSION)
        , config_version(1)
        , debug_mode(false)
        , log_level(3)
        , max_signals(MAX_SIGNALS)
        , max_timers(MAX_TIMER_TASKS)
        , auto_save(true)
        , save_interval(300) {
    }
};

/**
 * 完整系统配置结构 - 匹配后端架构设计
 */
struct CompleteSystemConfig {
    NetworkConfigData network;          // 网络配置
    HardwareConfigData hardware;        // 硬件配置
    SystemConfigData system;            // 系统配置
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        // 网络配置
        JsonObject networkObj = doc["network"].to<JsonObject>();
        networkObj["ap_ssid"] = network.ap_ssid;
        networkObj["ap_password"] = network.ap_password;
        networkObj["sta_ssid"] = network.sta_ssid;
        networkObj["sta_password"] = network.sta_password;
        networkObj["hostname"] = network.hostname;
        networkObj["enable_ap"] = network.enable_ap;
        networkObj["enable_sta"] = network.enable_sta;
        networkObj["web_port"] = network.web_port;
        networkObj["websocket_port"] = network.websocket_port;
        networkObj["connection_timeout"] = network.connection_timeout;
        
        // 硬件配置
        JsonObject hardwareObj = doc["hardware"].to<JsonObject>();
        hardwareObj["ir_transmit_pin"] = hardware.ir_transmit_pin;
        hardwareObj["ir_receive_pin"] = hardware.ir_receive_pin;
        hardwareObj["status_led_pin"] = hardware.status_led_pin;
        hardwareObj["ir_frequency"] = hardware.ir_frequency;
        hardwareObj["ir_duty_cycle"] = hardware.ir_duty_cycle;
        hardwareObj["enable_status_led"] = hardware.enable_status_led;
        hardwareObj["enable_ir_feedback"] = hardware.enable_ir_feedback;
        
        // 系统配置
        JsonObject systemObj = doc["system"].to<JsonObject>();
        systemObj["device_name"] = system.device_name;
        systemObj["device_id"] = system.device_id;
        systemObj["firmware_version"] = system.firmware_version;
        systemObj["config_version"] = system.config_version;
        systemObj["debug_mode"] = system.debug_mode;
        systemObj["log_level"] = system.log_level;
        systemObj["max_signals"] = system.max_signals;
        systemObj["max_timers"] = system.max_timers;
        systemObj["auto_save"] = system.auto_save;
        systemObj["save_interval"] = system.save_interval;
        
        return doc;
    }
    
    /**
     * 从JSON对象加载
     */
    void fromJson(const JsonDocument& doc) {
        // 网络配置
        if (doc["network"].is<JsonObject>()) {
            JsonObject networkObj = doc["network"];
            network.ap_ssid = networkObj["ap_ssid"].as<String>();
            network.ap_password = networkObj["ap_password"].as<String>();
            network.sta_ssid = networkObj["sta_ssid"].as<String>();
            network.sta_password = networkObj["sta_password"].as<String>();
            network.hostname = networkObj["hostname"].as<String>();
            network.enable_ap = networkObj["enable_ap"].as<bool>();
            network.enable_sta = networkObj["enable_sta"].as<bool>();
            network.web_port = networkObj["web_port"].as<uint16_t>();
            network.websocket_port = networkObj["websocket_port"].as<uint16_t>();
            network.connection_timeout = networkObj["connection_timeout"].as<uint32_t>();
        }
        
        // 硬件配置
        if (doc["hardware"].is<JsonObject>()) {
            JsonObject hardwareObj = doc["hardware"];
            hardware.ir_transmit_pin = hardwareObj["ir_transmit_pin"].as<uint8_t>();
            hardware.ir_receive_pin = hardwareObj["ir_receive_pin"].as<uint8_t>();
            hardware.status_led_pin = hardwareObj["status_led_pin"].as<uint8_t>();
            hardware.ir_frequency = hardwareObj["ir_frequency"].as<uint32_t>();
            hardware.ir_duty_cycle = hardwareObj["ir_duty_cycle"].as<uint8_t>();
            hardware.enable_status_led = hardwareObj["enable_status_led"].as<bool>();
            hardware.enable_ir_feedback = hardwareObj["enable_ir_feedback"].as<bool>();
        }
        
        // 系统配置
        if (doc["system"].is<JsonObject>()) {
            JsonObject systemObj = doc["system"];
            system.device_name = systemObj["device_name"].as<String>();
            system.device_id = systemObj["device_id"].as<String>();
            system.firmware_version = systemObj["firmware_version"].as<String>();
            system.config_version = systemObj["config_version"].as<uint32_t>();
            system.debug_mode = systemObj["debug_mode"].as<bool>();
            system.log_level = systemObj["log_level"].as<uint32_t>();
            system.max_signals = systemObj["max_signals"].as<uint32_t>();
            system.max_timers = systemObj["max_timers"].as<uint32_t>();
            system.auto_save = systemObj["auto_save"].as<bool>();
            system.save_interval = systemObj["save_interval"].as<uint32_t>();
        }
    }
};

// ================================
// 配置管理服务类定义
// ================================

/**
 * 配置管理服务类 - 完全匹配后端架构设计
 * 
 * 职责：
 * 1. 系统配置加载和保存
 * 2. 配置验证和错误处理
 * 3. 配置导入和导出
 * 4. 配置变更通知
 * 5. 配置版本管理
 */
class ConfigService : public BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    ConfigService(EventManager* eventMgr);
    
    /**
     * 析构函数
     */
    ~ConfigService();
    
    // ================================
    // BaseService接口实现
    // ================================
    
    /**
     * 初始化服务
     * @return 是否初始化成功
     */
    bool init() override;
    
    /**
     * 清理服务资源
     */
    void cleanup() override;
    
    /**
     * 服务主循环
     */
    void loop() override;
    
    /**
     * 获取服务状态
     * @return 服务状态
     */
    ServiceStatus getStatus() const override;
    
    /**
     * 配置变更通知 - 配置服务自身不需要响应配置变更
     * @param config 新配置数据
     */
    void onConfigChanged(const JsonDocument& config) override {}
    
    // ================================
    // 配置管理接口 - 匹配后端架构设计
    // ================================
    
    /**
     * 加载配置 - 从持久化存储加载
     * @return 是否加载成功
     */
    bool loadConfig();
    
    /**
     * 保存配置 - 保存到持久化存储
     * @return 是否保存成功
     */
    bool saveConfig();
    
    /**
     * 重置为默认配置
     * @return 是否重置成功
     */
    bool resetToDefaults();
    
    /**
     * 获取完整配置
     * @return 完整系统配置
     */
    CompleteSystemConfig getConfig() const { return config; }
    
    /**
     * 获取配置JSON
     * @return 配置JSON对象
     */
    JsonDocument getConfigJson() const { return config.toJson(); }
    
    /**
     * 从JSON设置配置
     * @param configJson 配置JSON对象
     * @return 是否设置成功
     */
    bool setConfigFromJson(const JsonDocument& configJson);
    
    // ================================
    // 配置验证接口
    // ================================
    
    /**
     * 验证配置
     * @return 是否验证通过
     */
    bool validateConfig();
    
    /**
     * 获取验证错误信息
     * @return 验证错误列表
     */
    std::vector<String> getValidationErrors() const { return validationErrors; }
    
    /**
     * 检查配置是否有效
     * @return 是否有效
     */
    bool isConfigValid() const { return configValid; }
    
    // ================================
    // 配置导入导出接口 - 匹配前端需求
    // ================================
    
    /**
     * 导出配置 - 匹配前端导出功能
     * @return 配置JSON字符串
     */
    String exportConfig() const;
    
    /**
     * 导入配置 - 匹配前端导入功能
     * @param configData 配置数据字符串
     * @return 是否导入成功
     */
    bool importConfig(const String& configData);
    
    /**
     * 导入配置文件
     * @param filePath 配置文件路径
     * @return 是否导入成功
     */
    bool importConfigFile(const String& filePath);
    
    /**
     * 导出配置文件
     * @param filePath 配置文件路径
     * @return 是否导出成功
     */
    bool exportConfigFile(const String& filePath);
    
    // ================================
    // 配置版本管理
    // ================================
    
    /**
     * 获取配置版本
     * @return 配置版本号
     */
    uint32_t getConfigVersion() const { return config.system.config_version; }
    
    /**
     * 更新配置版本
     */
    void updateConfigVersion() { config.system.config_version++; }
    
    /**
     * 检查配置是否需要升级
     * @return 是否需要升级
     */
    bool needsConfigUpgrade() const;
    
    /**
     * 升级配置格式
     * @return 是否升级成功
     */
    bool upgradeConfig();
    
    // ================================
    // 特定配置访问接口
    // ================================
    
    /**
     * 获取网络配置
     * @return 网络配置数据
     */
    NetworkConfigData getNetworkConfig() const { return config.network; }
    
    /**
     * 设置网络配置
     * @param networkConfig 网络配置数据
     * @return 是否设置成功
     */
    bool setNetworkConfig(const NetworkConfigData& networkConfig);
    
    /**
     * 获取硬件配置
     * @return 硬件配置数据
     */
    HardwareConfigData getHardwareConfig() const { return config.hardware; }
    
    /**
     * 设置硬件配置
     * @param hardwareConfig 硬件配置数据
     * @return 是否设置成功
     */
    bool setHardwareConfig(const HardwareConfigData& hardwareConfig);
    
    /**
     * 获取系统配置
     * @return 系统配置数据
     */
    SystemConfigData getSystemConfig() const { return config.system; }
    
    /**
     * 设置系统配置
     * @param systemConfig 系统配置数据
     * @return 是否设置成功
     */
    bool setSystemConfig(const SystemConfigData& systemConfig);

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 配置数据
    CompleteSystemConfig config;        // 完整系统配置
    CompleteSystemConfig defaultConfig; // 默认配置
    
    // 持久化存储
    Preferences preferences;            // ESP32 Preferences存储
    
    // 验证状态
    bool configValid;                   // 配置是否有效
    std::vector<String> validationErrors; // 验证错误列表
    
    // 自动保存
    uint32_t lastSaveTime;              // 上次保存时间
    bool configChanged;                 // 配置是否已更改
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化默认配置
     */
    void initDefaultConfig();
    
    /**
     * 生成设备ID
     * @return 唯一设备ID
     */
    String generateDeviceId();
    
    /**
     * 验证网络配置
     * @param networkConfig 网络配置
     * @return 是否有效
     */
    bool validateNetworkConfig(const NetworkConfigData& networkConfig);
    
    /**
     * 验证硬件配置
     * @param hardwareConfig 硬件配置
     * @return 是否有效
     */
    bool validateHardwareConfig(const HardwareConfigData& hardwareConfig);
    
    /**
     * 验证系统配置
     * @param systemConfig 系统配置
     * @return 是否有效
     */
    bool validateSystemConfig(const SystemConfigData& systemConfig);
    
    /**
     * 添加验证错误
     * @param error 错误信息
     */
    void addValidationError(const String& error);
    
    /**
     * 清除验证错误
     */
    void clearValidationErrors();
    
    /**
     * 发布配置变更事件
     */
    void publishConfigChangeEvent();
    
    /**
     * 检查自动保存
     */
    void checkAutoSave();
    
    /**
     * 备份当前配置
     * @return 是否备份成功
     */
    bool backupConfig();
    
    /**
     * 恢复配置备份
     * @return 是否恢复成功
     */
    bool restoreConfigBackup();
};

#endif // CONFIG_SERVICE_H
