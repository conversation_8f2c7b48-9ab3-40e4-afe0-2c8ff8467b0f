/**
 * ESP32-S3红外控制系统 - 硬件引脚配置
 * 
 * 功能说明：
 * - 基于ESP32-S3-DevKitC-1开发板的引脚配置定义
 * - 基于双核并行后端架构设计的硬件引脚分配
 * - 完整的红外控制、状态指示、调试和电源管理引脚定义
 * - 支持前端硬件控制功能的完整引脚映射
 * 
 * 硬件平台：
 * - 开发板：ESP32-S3-DevKitC-1
 * - 芯片：ESP32-S3 (无PSRAM版本)
 * - 封装：QFN56
 * - GPIO数量：45个可用GPIO
 * 
 * 引脚分配原则：
 * - 红外控制：使用支持硬件PWM的引脚
 * - 状态指示：使用内置LED引脚和独立GPIO
 * - 调试接口：使用USB-JTAG专用引脚
 * - 电源管理：使用高驱动能力引脚
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef PIN_CONFIG_H
#define PIN_CONFIG_H

// ================================
// ESP32-S3引脚能力说明
// ================================

/*
ESP32-S3-DevKitC-1引脚分配说明：

GPIO分类：
- GPIO0-21: 通用GPIO，支持所有数字功能
- GPIO26-48: 通用GPIO，支持所有数字功能
- GPIO19-20: 支持USB功能（开发板已用于USB-JTAG）
- GPIO43-44: 支持UART0（开发板已用于USB-Serial）

特殊功能引脚：
- GPIO0: 支持ADC1_CH0，Boot按键
- GPIO1-14: 支持ADC1_CH1-14
- GPIO15-20: 支持ADC2_CH4-9
- GPIO2: 内置RGB LED（WS2812）
- GPIO18: 支持LEDC PWM
- GPIO19-20: USB D-/D+
- GPIO43-44: UART0 TX/RX

PWM支持：
- LEDC: 所有GPIO都支持
- MCPWM: GPIO0-21, GPIO26-48

注意事项：
- GPIO26-32在某些封装中不可用
- GPIO33-37为SPI Flash专用（不可用）
- GPIO19-20已用于USB-JTAG调试
- GPIO43-44已用于USB-Serial通信
*/

// ================================
// 红外控制引脚配置
// ================================

// 红外发射引脚 - 使用支持LEDC PWM的GPIO
#define IR_TRANSMIT_PIN         18          // GPIO18 - 红外发射引脚
#define IR_TRANSMIT_CHANNEL     0           // LEDC通道0
#define IR_TRANSMIT_RESOLUTION  10          // PWM分辨率10位
#define IR_TRANSMIT_FREQUENCY   38000       // 载波频率38kHz

// 红外接收引脚 - 使用支持中断的GPIO
#define IR_RECEIVE_PIN          19          // GPIO19 - 红外接收引脚
#define IR_RECEIVE_INTERRUPT    digitalPinToInterrupt(IR_RECEIVE_PIN)

// 红外功率控制引脚
#define IR_POWER_PIN            21          // GPIO21 - 红外模块电源控制
#define IR_POWER_ACTIVE_LEVEL   1           // 高电平有效

// ================================
// 状态指示引脚配置
// ================================

// 主状态LED引脚 - 使用内置RGB LED
#define STATUS_LED_PIN          2           // GPIO2 - 内置RGB LED (WS2812)
#define STATUS_LED_TYPE         WS2812      // LED类型
#define STATUS_LED_COUNT        1           // LED数量
#define STATUS_LED_BRIGHTNESS   50          // LED亮度 (0-255)

// 辅助状态LED引脚 - 外接单色LED
#define AUX_LED_PIN             1           // GPIO1 - 辅助状态LED
#define AUX_LED_ACTIVE_LEVEL    1           // 高电平点亮

// 状态LED颜色定义 (RGB值)
#define LED_COLOR_OFF           0x000000    // 关闭 - 黑色
#define LED_COLOR_READY         0x00FF00    // 就绪 - 绿色
#define LED_COLOR_LEARNING      0x0000FF    // 学习 - 蓝色
#define LED_COLOR_EMITTING      0xFF0000    // 发射 - 红色
#define LED_COLOR_ERROR         0xFF00FF    // 错误 - 紫色
#define LED_COLOR_WARNING       0xFFFF00    // 警告 - 黄色
#define LED_COLOR_CONNECTING    0x00FFFF    // 连接 - 青色

// ================================
// 用户交互引脚配置
// ================================

// 学习按键引脚 - 使用Boot按键
#define LEARN_BUTTON_PIN        0           // GPIO0 - Boot按键
#define BUTTON_ACTIVE_LEVEL     0           // 低电平有效
#define BUTTON_PULLUP_ENABLED   true        // 启用内部上拉
#define BUTTON_DEBOUNCE_MS      50          // 按键防抖时间50ms

// 复位按键引脚 - 使用EN按键（硬件复位）
#define RESET_BUTTON_PIN        EN          // EN引脚 - 硬件复位按键

// 模式切换按键引脚 - 可选外接按键
#define MODE_BUTTON_PIN         3           // GPIO3 - 模式切换按键
#define MODE_BUTTON_ACTIVE      0           // 低电平有效
#define MODE_BUTTON_PULLUP      true        // 启用内部上拉

// ================================
// 调试接口引脚配置
// ================================

// USB-JTAG调试接口 (开发板内置)
#define DEBUG_USB_DP_PIN        20          // GPIO20 - USB D+ (JTAG)
#define DEBUG_USB_DN_PIN        19          // GPIO19 - USB D- (JTAG)

// USB-Serial调试接口 (开发板内置)
#define DEBUG_UART_TX_PIN       43          // GPIO43 - UART0 TX
#define DEBUG_UART_RX_PIN       44          // GPIO44 - UART0 RX
#define DEBUG_UART_BAUD         115200      // 调试串口波特率

// 外部调试引脚 - 可选的额外调试输出
#define DEBUG_OUTPUT_PIN        4           // GPIO4 - 调试输出引脚
#define DEBUG_TRIGGER_PIN       5           // GPIO5 - 调试触发引脚

// ================================
// 电源管理引脚配置
// ================================

// 主电源控制引脚
#define POWER_ENABLE_PIN        21          // GPIO21 - 主电源使能
#define POWER_ENABLE_LEVEL      1           // 高电平使能

// 电源监控引脚
#define POWER_MONITOR_PIN       6           // GPIO6 - 电源监控 (ADC)
#define POWER_MONITOR_CHANNEL   ADC1_CHANNEL_5  // ADC通道

// 低功耗模式控制
#define SLEEP_ENABLE_PIN        7           // GPIO7 - 睡眠使能
#define WAKEUP_PIN              8           // GPIO8 - 唤醒引脚

// ================================
// 扩展接口引脚配置
// ================================

// I2C接口引脚 - 用于扩展传感器
#define I2C_SDA_PIN             9           // GPIO9 - I2C数据线
#define I2C_SCL_PIN             10          // GPIO10 - I2C时钟线
#define I2C_FREQUENCY           100000      // I2C频率100kHz

// SPI接口引脚 - 用于扩展存储或显示
#define SPI_MOSI_PIN            11          // GPIO11 - SPI MOSI
#define SPI_MISO_PIN            12          // GPIO12 - SPI MISO
#define SPI_SCK_PIN             13          // GPIO13 - SPI SCK
#define SPI_CS_PIN              14          // GPIO14 - SPI CS

// UART扩展接口 - 用于外部模块通信
#define UART_EXT_TX_PIN         15          // GPIO15 - 扩展UART TX
#define UART_EXT_RX_PIN         16          // GPIO16 - 扩展UART RX
#define UART_EXT_BAUD           9600        // 扩展UART波特率

// ================================
// 模拟输入引脚配置
// ================================

// 环境光传感器 - 用于自动调节LED亮度
#define LIGHT_SENSOR_PIN        17          // GPIO17 - 光敏电阻 (ADC)
#define LIGHT_SENSOR_CHANNEL    ADC1_CHANNEL_6  // ADC通道

// 温度传感器 - 用于系统温度监控
#define TEMP_SENSOR_PIN         18          // GPIO18 - 温度传感器 (ADC)
#define TEMP_SENSOR_CHANNEL     ADC1_CHANNEL_7  // ADC通道

// 电池电压监测 - 用于电池供电版本
#define BATTERY_MONITOR_PIN     1           // GPIO1 - 电池电压 (ADC)
#define BATTERY_MONITOR_CHANNEL ADC1_CHANNEL_0  // ADC通道

// ================================
// 引脚功能验证宏
// ================================

// 验证引脚是否可用
#define IS_VALID_GPIO(pin)      ((pin >= 0 && pin <= 21) || (pin >= 26 && pin <= 48))

// 验证引脚是否支持ADC
#define IS_ADC_PIN(pin)         ((pin >= 1 && pin <= 10) || (pin >= 11 && pin <= 20))

// 验证引脚是否支持PWM
#define IS_PWM_PIN(pin)         IS_VALID_GPIO(pin)

// 验证引脚是否支持中断
#define IS_INTERRUPT_PIN(pin)   IS_VALID_GPIO(pin)

// ================================
// 引脚初始化宏
// ================================

// 初始化输出引脚
#define INIT_OUTPUT_PIN(pin, level) do { \
    pinMode(pin, OUTPUT); \
    digitalWrite(pin, level); \
} while(0)

// 初始化输入引脚（带上拉）
#define INIT_INPUT_PULLUP_PIN(pin) do { \
    pinMode(pin, INPUT_PULLUP); \
} while(0)

// 初始化输入引脚（带下拉）
#define INIT_INPUT_PULLDOWN_PIN(pin) do { \
    pinMode(pin, INPUT_PULLDOWN); \
} while(0)

// 初始化模拟输入引脚
#define INIT_ANALOG_PIN(pin) do { \
    pinMode(pin, INPUT); \
} while(0)

// ================================
// 引脚状态检查宏
// ================================

// 检查按键是否按下
#define IS_BUTTON_PRESSED(pin, active_level) \
    (digitalRead(pin) == active_level)

// 检查电源是否使能
#define IS_POWER_ENABLED() \
    (digitalRead(POWER_ENABLE_PIN) == POWER_ENABLE_LEVEL)

// 检查红外功率是否开启
#define IS_IR_POWER_ON() \
    (digitalRead(IR_POWER_PIN) == IR_POWER_ACTIVE_LEVEL)

// ================================
// 引脚操作宏
// ================================

// 控制状态LED
#define SET_STATUS_LED(state) \
    digitalWrite(STATUS_LED_PIN, state)

// 控制辅助LED
#define SET_AUX_LED(state) \
    digitalWrite(AUX_LED_PIN, (state) ? AUX_LED_ACTIVE_LEVEL : !AUX_LED_ACTIVE_LEVEL)

// 控制红外功率
#define SET_IR_POWER(state) \
    digitalWrite(IR_POWER_PIN, (state) ? IR_POWER_ACTIVE_LEVEL : !IR_POWER_ACTIVE_LEVEL)

// 控制主电源
#define SET_MAIN_POWER(state) \
    digitalWrite(POWER_ENABLE_PIN, (state) ? POWER_ENABLE_LEVEL : !POWER_ENABLE_LEVEL)

// ================================
// 编译时引脚检查
// ================================

// 检查关键引脚是否有效
#if !IS_VALID_GPIO(IR_TRANSMIT_PIN)
#error "红外发射引脚无效"
#endif

#if !IS_VALID_GPIO(IR_RECEIVE_PIN)
#error "红外接收引脚无效"
#endif

#if !IS_VALID_GPIO(STATUS_LED_PIN)
#error "状态LED引脚无效"
#endif

#if !IS_VALID_GPIO(LEARN_BUTTON_PIN)
#error "学习按键引脚无效"
#endif

// 检查引脚冲突
#if IR_TRANSMIT_PIN == IR_RECEIVE_PIN
#error "红外发射和接收引脚冲突"
#endif

#if STATUS_LED_PIN == LEARN_BUTTON_PIN
#error "状态LED和学习按键引脚冲突"
#endif

// 检查特殊引脚使用
#if IR_TRANSMIT_PIN == 19 || IR_TRANSMIT_PIN == 20
#warning "红外发射引脚使用了USB-JTAG引脚，可能影响调试功能"
#endif

#if IR_RECEIVE_PIN == 43 || IR_RECEIVE_PIN == 44
#warning "红外接收引脚使用了UART0引脚，可能影响串口通信"
#endif

#endif // PIN_CONFIG_H
