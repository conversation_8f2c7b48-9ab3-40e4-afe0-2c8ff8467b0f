/**
 * ESP32-S3红外控制系统 - 网络配置定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的网络配置
 * - 完整的WiFi AP和STA模式配置，支持前端网络连接
 * - HTTP和WebSocket服务器配置，匹配前端API调用
 * - 网络安全和性能优化配置
 * 
 * 前端匹配度：
 * - HTTP服务器：端口80，匹配前端API调用地址
 * - WebSocket服务器：端口81，匹配前端实时通信
 * - CORS配置：支持前端跨域访问
 * - 超时配置：匹配前端网络请求超时设置
 * 
 * 后端架构匹配：
 * - 双模式支持：AP模式（配置）+ STA模式（使用）
 * - 连接管理：自动重连、连接状态监控
 * - 性能优化：连接池、请求缓存、批量处理
 * - 安全配置：WPA2加密、访问控制、防护机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef NETWORK_CONFIG_H
#define NETWORK_CONFIG_H

#include <WiFi.h>
#include <IPAddress.h>

// ================================
// WiFi基础配置
// ================================

// WiFi模式配置
#define WIFI_MODE_DEFAULT       WIFI_AP_STA     // 默认AP+STA模式
#define WIFI_AUTO_CONNECT       true            // 自动连接已保存的WiFi
#define WIFI_AUTO_RECONNECT     true            // 自动重连
#define WIFI_RECONNECT_INTERVAL 5000            // 重连间隔5秒
#define WIFI_MAX_RECONNECT      10              // 最大重连次数

// WiFi功率配置
#define WIFI_POWER_LEVEL        WIFI_POWER_19_5dBm  // WiFi发射功率
#define WIFI_SLEEP_MODE         WIFI_PS_NONE        // WiFi睡眠模式（不睡眠）

// ================================
// WiFi AP模式配置（热点模式）
// ================================

// AP基础配置
#define WIFI_AP_SSID            "ESP32_IR_Controller"   // AP热点名称
#define WIFI_AP_PASSWORD        "12345678"             // AP密码（8位以上）
#define WIFI_AP_CHANNEL         1                      // AP信道
#define WIFI_AP_MAX_CONNECTIONS 4                      // 最大连接数
#define WIFI_AP_HIDDEN          false                  // 是否隐藏SSID

// AP安全配置
#define WIFI_AP_AUTH_MODE       WIFI_AUTH_WPA2_PSK     // WPA2-PSK加密
#define WIFI_AP_BEACON_INTERVAL 100                    // 信标间隔100ms
#define WIFI_AP_DTIM_PERIOD     2                      // DTIM周期

// AP IP配置
#define AP_IP_ADDRESS           IPAddress(192, 168, 4, 1)   // AP IP地址
#define AP_GATEWAY              IPAddress(192, 168, 4, 1)   // AP网关
#define AP_SUBNET               IPAddress(255, 255, 255, 0) // AP子网掩码
#define AP_DNS_SERVER           IPAddress(192, 168, 4, 1)   // AP DNS服务器

// AP DHCP配置
#define AP_DHCP_ENABLED         true                    // 启用DHCP服务器
#define AP_DHCP_START_IP        IPAddress(192, 168, 4, 2)   // DHCP起始IP
#define AP_DHCP_END_IP          IPAddress(192, 168, 4, 10)  // DHCP结束IP
#define AP_DHCP_LEASE_TIME      7200                    // DHCP租期2小时

// ================================
// WiFi STA模式配置（客户端模式）
// ================================

// STA连接配置
#define WIFI_STA_SSID           ""                      // STA连接的WiFi名称（空=未配置）
#define WIFI_STA_PASSWORD       ""                      // STA连接的WiFi密码
#define WIFI_STA_TIMEOUT        10000                   // STA连接超时10秒
#define WIFI_STA_RETRY_COUNT    3                       // STA连接重试次数

// STA IP配置
#define STA_USE_STATIC_IP       false                   // 是否使用静态IP
#define STA_STATIC_IP           IPAddress(192, 168, 1, 100) // 静态IP地址
#define STA_GATEWAY             IPAddress(192, 168, 1, 1)   // 网关地址
#define STA_SUBNET              IPAddress(255, 255, 255, 0) // 子网掩码
#define STA_DNS1                IPAddress(8, 8, 8, 8)       // 主DNS服务器
#define STA_DNS2                IPAddress(8, 8, 4, 4)       // 备DNS服务器

// STA高级配置
#define STA_HOSTNAME            "ESP32-IR-Controller"   // 主机名
#define STA_MAC_RANDOMIZE       false                   // 是否随机化MAC地址
#define STA_FAST_CONNECT        true                    // 快速连接模式

// ================================
// HTTP服务器配置
// ================================

// HTTP基础配置
#define HTTP_SERVER_PORT        80                      // HTTP服务器端口
#define HTTP_SERVER_ENABLED     true                    // 启用HTTP服务器
#define HTTP_MAX_CONNECTIONS    10                      // 最大HTTP连接数
#define HTTP_KEEPALIVE_TIMEOUT  30000                   // HTTP保持连接超时30秒

// HTTP请求配置
#define HTTP_REQUEST_TIMEOUT    5000                    // HTTP请求超时5秒
#define HTTP_MAX_REQUEST_SIZE   8192                    // 最大请求大小8KB
#define HTTP_MAX_RESPONSE_SIZE  16384                   // 最大响应大小16KB
#define HTTP_BUFFER_SIZE        2048                    // HTTP缓冲区大小2KB

// HTTP安全配置
#define HTTP_CORS_ENABLED       true                    // 启用CORS跨域
#define HTTP_CORS_ORIGIN        "*"                     // CORS允许的源（开发阶段允许所有）
#define HTTP_CORS_METHODS       "GET,POST,PUT,DELETE,OPTIONS"  // CORS允许的方法
#define HTTP_CORS_HEADERS       "Content-Type,Authorization,X-Requested-With"  // CORS允许的头

// HTTP缓存配置
#define HTTP_CACHE_ENABLED      true                    // 启用HTTP缓存
#define HTTP_CACHE_MAX_AGE      3600                    // 缓存最大时间1小时
#define HTTP_ETAG_ENABLED       true                    // 启用ETag
#define HTTP_GZIP_ENABLED       true                    // 启用GZIP压缩

// ================================
// WebSocket服务器配置
// ================================

// WebSocket基础配置
#define WEBSOCKET_PORT          81                      // WebSocket端口
#define WEBSOCKET_ENABLED       true                    // 启用WebSocket服务器
#define MAX_WEBSOCKET_CLIENTS   5                       // 最大WebSocket连接数
#define WEBSOCKET_BUFFER_SIZE   2048                    // WebSocket缓冲区大小

// WebSocket连接配置
#define WEBSOCKET_TIMEOUT       60000                   // WebSocket超时60秒
#define WEBSOCKET_PING_INTERVAL 30000                   // WebSocket心跳间隔30秒
#define WEBSOCKET_PONG_TIMEOUT  10000                   // WebSocket心跳响应超时10秒
#define WEBSOCKET_RECONNECT_DELAY 3000                  // WebSocket重连延迟3秒

// WebSocket消息配置
#define WEBSOCKET_MAX_MESSAGE_SIZE 4096                 // 最大消息大小4KB
#define WEBSOCKET_QUEUE_SIZE    32                      // 消息队列大小
#define WEBSOCKET_SEND_TIMEOUT  5000                    // 发送超时5秒

// WebSocket安全配置
#define WEBSOCKET_ORIGIN_CHECK  false                   // 检查Origin头（开发阶段关闭）
#define WEBSOCKET_SUBPROTOCOL   "ir-control-v2"         // WebSocket子协议
#define WEBSOCKET_COMPRESSION   false                   // WebSocket压缩（ESP32性能考虑关闭）

// ================================
// 网络性能配置
// ================================

// TCP配置
#define TCP_MSS                 1460                    // TCP最大段大小
#define TCP_WINDOW_SIZE         8192                    // TCP窗口大小
#define TCP_KEEPALIVE_ENABLED   true                    // 启用TCP保活
#define TCP_KEEPALIVE_IDLE      7200                    // TCP保活空闲时间2小时
#define TCP_KEEPALIVE_INTERVAL  75                      // TCP保活间隔75秒
#define TCP_KEEPALIVE_COUNT     9                       // TCP保活重试次数

// 连接池配置
#define CONNECTION_POOL_SIZE    8                       // 连接池大小
#define CONNECTION_REUSE        true                    // 连接复用
#define CONNECTION_TIMEOUT      30000                   // 连接超时30秒

// 批量处理配置
#define BATCH_REQUEST_ENABLED   true                    // 启用批量请求
#define BATCH_REQUEST_TIMEOUT   50                      // 批量请求超时50ms
#define MAX_BATCH_REQUESTS      10                      // 最大批量请求数
#define BATCH_RESPONSE_SIZE     8192                    // 批量响应缓冲区大小

// ================================
// 网络安全配置
// ================================

// 访问控制配置
#define ACCESS_CONTROL_ENABLED  false                   // 启用访问控制（开发阶段关闭）
#define WHITELIST_ENABLED       false                   // 启用IP白名单
#define BLACKLIST_ENABLED       false                   // 启用IP黑名单
#define RATE_LIMIT_ENABLED      false                   // 启用速率限制

// 防护配置
#define DDoS_PROTECTION         false                   // DDoS防护（开发阶段关闭）
#define MAX_REQUESTS_PER_SECOND 100                     // 每秒最大请求数
#define MAX_CONNECTIONS_PER_IP  5                       // 每IP最大连接数
#define BLOCK_DURATION          300                     // 阻止持续时间5分钟

// SSL/TLS配置（未来扩展）
#define SSL_ENABLED             false                   // 启用SSL/TLS
#define SSL_PORT                443                     // HTTPS端口
#define SSL_CERT_SIZE           2048                    // SSL证书大小
#define SSL_KEY_SIZE            2048                    // SSL密钥大小

// ================================
// 网络监控配置
// ================================

// 连接监控
#define NETWORK_MONITOR_ENABLED true                    // 启用网络监控
#define MONITOR_INTERVAL        5000                    // 监控间隔5秒
#define SIGNAL_STRENGTH_MONITOR true                    // 监控WiFi信号强度
#define BANDWIDTH_MONITOR       true                    // 监控带宽使用

// 统计配置
#define NETWORK_STATS_ENABLED   true                    // 启用网络统计
#define STATS_HISTORY_SIZE      100                     // 统计历史记录数量
#define STATS_RESET_INTERVAL    86400                   // 统计重置间隔24小时

// 日志配置
#define NETWORK_LOG_ENABLED     true                    // 启用网络日志
#define LOG_CONNECTION_EVENTS   true                    // 记录连接事件
#define LOG_REQUEST_DETAILS     false                   // 记录请求详情（开发阶段关闭）
#define LOG_ERROR_DETAILS       true                    // 记录错误详情

// ================================
// 网络工具配置
// ================================

// mDNS配置
#define MDNS_ENABLED            true                    // 启用mDNS
#define MDNS_HOSTNAME           "esp32-ir-controller"   // mDNS主机名
#define MDNS_SERVICE_NAME       "红外控制器"             // mDNS服务名
#define MDNS_SERVICE_TYPE       "_http"                 // mDNS服务类型
#define MDNS_SERVICE_PROTOCOL   "_tcp"                  // mDNS服务协议

// NTP配置
#define NTP_ENABLED             true                    // 启用NTP时间同步
#define NTP_SERVER1             "pool.ntp.org"          // 主NTP服务器
#define NTP_SERVER2             "time.nist.gov"         // 备NTP服务器
#define NTP_TIMEZONE            8                       // 时区UTC+8
#define NTP_SYNC_INTERVAL       3600                    // 同步间隔1小时

// OTA网络配置
#define OTA_NETWORK_ENABLED     true                    // 启用网络OTA
#define OTA_HTTP_PORT           8080                    // OTA HTTP端口
#define OTA_BUFFER_SIZE         1024                    // OTA缓冲区大小
#define OTA_TIMEOUT             300000                  // OTA超时5分钟

// ================================
// 网络调试配置
// ================================

// 调试输出
#define NETWORK_DEBUG_ENABLED   true                    // 启用网络调试
#define DEBUG_WIFI_EVENTS       true                    // 调试WiFi事件
#define DEBUG_HTTP_REQUESTS     false                   // 调试HTTP请求（开发阶段关闭）
#define DEBUG_WEBSOCKET_MESSAGES false                  // 调试WebSocket消息（开发阶段关闭）

// 性能调试
#define NETWORK_PERF_DEBUG      true                    // 启用网络性能调试
#define MEASURE_REQUEST_TIME    true                    // 测量请求处理时间
#define MEASURE_RESPONSE_TIME   true                    // 测量响应时间
#define LOG_SLOW_REQUESTS       true                    // 记录慢请求（>100ms）

// ================================
// 网络状态定义
// ================================

// WiFi状态枚举
typedef enum {
    WIFI_STATE_DISCONNECTED = 0,   // 未连接
    WIFI_STATE_CONNECTING = 1,     // 连接中
    WIFI_STATE_CONNECTED = 2,      // 已连接
    WIFI_STATE_AP_MODE = 3,        // AP模式
    WIFI_STATE_ERROR = 4           // 错误状态
} wifi_state_t;

// 网络服务状态枚举
typedef enum {
    NET_SERVICE_STOPPED = 0,       // 已停止
    NET_SERVICE_STARTING = 1,      // 启动中
    NET_SERVICE_RUNNING = 2,       // 运行中
    NET_SERVICE_ERROR = 3          // 错误状态
} net_service_state_t;

// ================================
// 网络工具宏
// ================================

// IP地址转字符串
#define IP_TO_STRING(ip) \
    String(ip[0]) + "." + String(ip[1]) + "." + String(ip[2]) + "." + String(ip[3])

// 检查IP地址是否有效
#define IS_VALID_IP(ip) \
    (ip != IPAddress(0, 0, 0, 0) && ip != IPAddress(255, 255, 255, 255))

// 检查WiFi是否连接
#define IS_WIFI_CONNECTED() \
    (WiFi.status() == WL_CONNECTED)

// 检查AP模式是否启用
#define IS_AP_MODE_ENABLED() \
    (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA)

// ================================
// 编译时网络配置检查
// ================================

// 检查端口配置
#if HTTP_SERVER_PORT == WEBSOCKET_PORT
#error "HTTP和WebSocket端口不能相同"
#endif

#if HTTP_SERVER_PORT < 1 || HTTP_SERVER_PORT > 65535
#error "HTTP服务器端口无效"
#endif

#if WEBSOCKET_PORT < 1 || WEBSOCKET_PORT > 65535
#error "WebSocket端口无效"
#endif

// 检查连接数配置
#if WIFI_AP_MAX_CONNECTIONS > 8
#error "AP最大连接数不能超过8"
#endif

#if MAX_WEBSOCKET_CLIENTS > WIFI_AP_MAX_CONNECTIONS
#error "WebSocket最大连接数不能超过WiFi AP最大连接数"
#endif

// 检查缓冲区大小
#if HTTP_BUFFER_SIZE < 512
#error "HTTP缓冲区大小不能小于512字节"
#endif

#if WEBSOCKET_BUFFER_SIZE < 512
#error "WebSocket缓冲区大小不能小于512字节"
#endif

#endif // NETWORK_CONFIG_H
