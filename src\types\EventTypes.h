/**
 * ESP32-S3红外控制系统 - 事件类型定义
 * 
 * 功能说明：
 * - 基于前端完整数据文档的101个事件类型100%匹配实现
 * - 基于后端架构设计的双核并行事件处理系统
 * - 完整的事件优先级管理，支持高优先级事件<1ms响应
 * - 支持EventBus事件总线和WebSocket事件广播
 * 
 * 前端匹配度：
 * - 事件类型：101个事件类型100%匹配前端EventBus定义
 * - 事件优先级：完全匹配前端高优先级事件集合
 * - 事件数据：匹配前端事件数据结构和传递格式
 * - 事件命名：严格按照前端事件命名规范
 * 
 * 后端架构匹配：
 * - 双核处理：高优先级事件在核心0处理，普通事件在核心1批处理
 * - 环形缓冲：避免动态内存分配，提升实时性能
 * - 事件合并：可合并事件替换队列中的旧事件
 * - 时间片处理：8ms时间片避免阻塞UI
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef EVENT_TYPES_H
#define EVENT_TYPES_H

#include <Arduino.h>
#include <ArduinoJson.h>

// ================================
// 事件类型枚举定义
// ================================

/**
 * 事件类型枚举 - 100%匹配前端101个事件类型
 * 
 * 基于前端完整数据文档分析，事件系统包含101个事件类型，分为以下几类：
 * - 系统级事件（15个）：system.*, module.*, esp32.*
 * - 信号管理事件（25个）：signal.*
 * - 控制模块事件（23个）：control.*
 * - 定时器事件（12个）：timer.*
 * - 网络事件（8个）：network.*, websocket.*
 * - 硬件事件（10个）：hardware.*, ir.*
 * - 配置事件（8个）：config.*
 */
enum class EventType : uint8_t {
    // ================================
    // 系统级事件（15个）- 匹配前端系统事件
    // ================================
    
    // 系统生命周期事件
    SYSTEM_READY = 0,               // system.ready - 系统就绪
    SYSTEM_ERROR = 1,               // system.error - 系统错误
    SYSTEM_REFRESH = 2,             // system.refresh - 系统刷新
    SYSTEM_RESTART = 3,             // system.restart - 系统重启
    SYSTEM_SHUTDOWN = 4,            // system.shutdown - 系统关闭
    
    // 模块生命周期事件
    MODULE_READY = 5,               // module.ready - 模块就绪
    MODULE_ERROR = 6,               // module.error - 模块错误
    MODULE_SUCCESS = 7,             // module.success - 模块成功
    MODULE_SWITCH = 8,              // module.switch - 模块切换
    MODULE_REFRESH = 9,             // module.refresh - 模块刷新
    
    // ESP32连接事件
    ESP32_CONNECTED = 10,           // esp32.connected - ESP32连接成功
    ESP32_DISCONNECTED = 11,        // esp32.disconnected - ESP32连接断开
    ESP32_ERROR = 12,               // esp32.error - ESP32通信错误
    ESP32_REQUEST_SUCCESS = 13,     // esp32.request.success - 请求成功
    ESP32_REQUEST_ERROR = 14,       // esp32.request.error - 请求失败
    
    // ================================
    // 信号管理事件（25个）- 匹配前端信号事件
    // ================================
    
    // 信号请求事件
    SIGNAL_REQUEST_ALL = 15,        // signal.request.all - 请求所有信号
    SIGNAL_REQUEST_BY_IDS = 16,     // signal.request.by-ids - 按ID请求信号
    SIGNAL_REQUEST_SELECTED = 17,   // signal.request.selected - 请求选中信号
    SIGNAL_REQUEST_COUNT = 18,      // signal.request.count - 请求信号数量
    
    // 信号CRUD事件
    SIGNAL_ADDED = 19,              // signal.added - 信号已添加
    SIGNAL_DELETED = 20,            // signal.deleted - 信号已删除
    SIGNAL_UPDATED = 21,            // signal.updated - 信号已更新
    SIGNAL_CLEARED = 22,            // signal.cleared - 信号已清空
    
    // 信号发射事件
    SIGNAL_EMIT_SUCCESS = 23,       // signal.emit.success - 发射成功
    SIGNAL_EMIT_FAILED = 24,        // signal.emit.failed - 发射失败
    SIGNAL_EMIT_PROGRESS = 25,      // signal.emit.progress - 发射进度
    SIGNAL_EMIT_COMPLETED = 26,     // signal.emit.completed - 发射完成
    
    // 信号学习事件
    SIGNAL_LEARNING_STARTED = 27,   // signal.learning.started - 学习开始
    SIGNAL_LEARNING_STOPPED = 28,   // signal.learning.stopped - 学习停止
    SIGNAL_LEARNING_STATUS_CHANGED = 29, // signal.learning.status.changed - 学习状态变化
    SIGNAL_LEARNING_STATUS_REQUEST = 30, // signal.learning.status.request - 学习状态请求
    SIGNAL_LEARNED = 31,            // signal.learned - 信号已学习
    
    // 信号选择事件
    SIGNAL_SELECTION_COMPLETED = 32, // signal.selection.completed - 选择完成
    SIGNAL_SELECTION_CANCELLED = 33, // signal.selection.cancelled - 选择取消
    SIGNAL_SELECTION_CHANGED = 34,  // signal.selection.changed - 选择变化
    
    // 批量信号事件
    SIGNAL_BATCH_EMIT_REQUEST = 35, // signal.batch.emit.request - 批量发射请求
    SIGNAL_BATCH_EMIT_RESPONSE = 36, // signal.batch.emit.response - 批量发射响应
    SIGNAL_BATCH_DELETE = 37,       // signal.batch.delete - 批量删除
    SIGNAL_BATCH_EXPORT = 38,       // signal.batch.export - 批量导出
    SIGNAL_BATCH_IMPORT = 39,       // signal.batch.import - 批量导入
    
    // ================================
    // 控制模块事件（23个）- 匹配前端控制事件
    // ================================
    
    // 控制发射事件
    CONTROL_EMIT_STARTED = 40,      // control.emit.started - 发射开始
    CONTROL_EMIT_PROGRESS = 41,     // control.emit.progress - 发射进度（高优先级）
    CONTROL_EMIT_COMPLETED = 42,    // control.emit.completed - 发射完成
    CONTROL_EMIT_FAILED = 43,       // control.emit.failed - 发射失败
    
    // 控制信号事件
    CONTROL_SIGNAL_EMITTING = 44,   // control.signal.emitting - 信号发射中（高优先级）
    CONTROL_SIGNAL_EMITTED = 45,    // control.signal.emitted - 信号已发射（高优先级）
    
    // 控制请求事件
    CONTROL_REQUEST_SEND_SIGNAL = 46, // control.request.send-signal - 请求发送信号
    CONTROL_REQUEST_SELECTED_SIGNALS = 47, // control.request.selected-signals - 请求选中信号
    CONTROL_REQUEST_SIGNALS = 48,   // control.request.signals - 请求信号
    
    // 控制暂停恢复事件
    CONTROL_PAUSE_REQUEST = 49,     // control.pause.request - 暂停请求
    CONTROL_RESUME_REQUEST = 50,    // control.resume.request - 恢复请求
    CONTROL_PAUSED_TASKS_STATUS_REQUEST = 51, // control.paused-tasks.status.request - 暂停任务状态请求
    
    // 控制任务事件
    CONTROL_TASK_STATUS_REQUEST = 52, // control.task.status.request - 任务状态请求
    CONTROL_TASK_STARTED = 53,      // control.task.started - 任务开始
    CONTROL_TASK_COMPLETED = 54,    // control.task.completed - 任务完成
    CONTROL_TASK_ERROR = 55,        // control.task.error - 任务错误
    
    // 控制调试事件
    CONTROL_DEBUG_REPORT_REQUEST = 56, // control.debug.report.request - 调试报告请求
    CONTROL_DEBUG_STATE_REQUEST = 57, // control.debug.state.request - 调试状态请求
    
    // 控制信号同步事件
    CONTROL_SIGNALS_APPENDED = 58,  // control.signals.appended - 信号已追加
    CONTROL_SIGNALS_UPDATED = 59,   // control.signals.updated - 信号已更新
    CONTROL_SIGNALS_REMOVED = 60,   // control.signals.removed - 信号已移除
    
    // 控制模式事件
    CONTROL_MODE_CHANGED = 61,      // control.mode.changed - 模式变化
    CONTROL_RATE_CHANGED = 62,      // control.rate.changed - 速率变化
    
    // ================================
    // 定时器事件（12个）- 匹配前端定时器事件
    // ================================
    
    // 定时任务执行事件
    TIMER_TASK_DUE = 63,            // timer.task.due - 定时任务到期（高优先级）
    TIMER_TASK_EXECUTION_REQUEST = 64, // timer.task.execution.request - 任务执行请求（高优先级）
    TIMER_TASK_EXECUTION_STARTED = 65, // timer.task.execution.started - 任务执行开始
    TIMER_TASK_EXECUTION_COMPLETED = 66, // timer.task.execution.completed - 任务执行完成
    TIMER_TASK_EXECUTION_FAILED = 67, // timer.task.execution.failed - 任务执行失败
    
    // 定时任务管理事件
    TIMER_TASK_CREATED = 68,        // timer.task.created - 任务创建
    TIMER_TASK_UPDATED = 69,        // timer.task.updated - 任务更新
    TIMER_TASK_DELETED = 70,        // timer.task.deleted - 任务删除
    TIMER_TASK_ENABLED = 71,        // timer.task.enabled - 任务启用
    TIMER_TASK_DISABLED = 72,       // timer.task.disabled - 任务禁用
    
    // 定时器信息事件
    TIMER_TASK_INFO_REQUEST = 73,   // timer.task.info.request - 任务信息请求
    TIMER_STATUS_CHANGED = 74,      // timer.status.changed - 定时器状态变化
    
    // ================================
    // 网络事件（8个）- 匹配前端网络事件
    // ================================
    
    // WiFi连接事件
    NETWORK_WIFI_CONNECTED = 75,    // network.wifi.connected - WiFi连接成功
    NETWORK_WIFI_DISCONNECTED = 76, // network.wifi.disconnected - WiFi连接断开
    NETWORK_WIFI_ERROR = 77,        // network.wifi.error - WiFi连接错误
    
    // WebSocket事件
    WEBSOCKET_CONNECTED = 78,       // websocket.connected - WebSocket连接
    WEBSOCKET_DISCONNECTED = 79,    // websocket.disconnected - WebSocket断开
    WEBSOCKET_ERROR = 80,           // websocket.error - WebSocket错误
    WEBSOCKET_MESSAGE = 81,         // websocket.message - WebSocket消息
    
    // HTTP服务器事件
    HTTP_SERVER_STARTED = 82,       // http.server.started - HTTP服务器启动
    
    // ================================
    // 硬件事件（10个）- 匹配前端硬件事件
    // ================================
    
    // 硬件初始化事件
    HARDWARE_INITIALIZED = 83,      // hardware.initialized - 硬件初始化
    HARDWARE_ERROR = 84,            // hardware.error - 硬件错误
    
    // 红外硬件事件
    IR_TRANSMITTER_READY = 85,      // ir.transmitter.ready - 红外发射器就绪
    IR_TRANSMITTER_ERROR = 86,      // ir.transmitter.error - 红外发射器错误
    IR_RECEIVER_READY = 87,         // ir.receiver.ready - 红外接收器就绪
    IR_RECEIVER_ERROR = 88,         // ir.receiver.error - 红外接收器错误
    IR_SIGNAL_DETECTED = 89,        // ir.signal.detected - 红外信号检测
    
    // LED状态事件
    LED_STATUS_CHANGED = 90,        // led.status.changed - LED状态变化
    LED_PATTERN_CHANGED = 91,       // led.pattern.changed - LED模式变化
    
    // 按键事件
    BUTTON_PRESSED = 92,            // button.pressed - 按键按下
    
    // ================================
    // 配置事件（8个）- 匹配前端配置事件
    // ================================
    
    // 配置管理事件
    CONFIG_LOADED = 93,             // config.loaded - 配置加载
    CONFIG_SAVED = 94,              // config.saved - 配置保存
    CONFIG_CHANGED = 95,            // config.changed - 配置变化
    CONFIG_RESET = 96,              // config.reset - 配置重置
    CONFIG_VALIDATED = 97,          // config.validated - 配置验证
    CONFIG_ERROR = 98,              // config.error - 配置错误
    
    // OTA事件
    OTA_STARTED = 99,               // ota.started - OTA开始
    OTA_COMPLETED = 100,            // ota.completed - OTA完成
    
    // 事件总数：101个事件类型
    EVENT_TYPE_COUNT = 101
};

// ================================
// 事件优先级定义
// ================================

/**
 * 事件优先级枚举 - 匹配前端高优先级事件集合
 */
enum class EventPriority : uint8_t {
    PRIORITY_NORMAL = 0,     // 普通事件，批处理
    PRIORITY_URGENT = 1      // 高优先级事件，立即处理
};

// ================================
// 事件数据结构定义
// ================================

/**
 * 事件数据结构 - 匹配前端事件数据格式
 */
struct EventData {
    EventType type;                 // 事件类型
    EventPriority priority;         // 事件优先级
    JsonDocument data;              // 事件数据
    String source;                  // 事件源（自动添加）
    uint64_t timestamp;             // 事件时间戳
    uint32_t id;                    // 事件ID（用于去重）
    
    /**
     * 构造函数
     */
    EventData(EventType eventType, const JsonDocument& eventData, const String& eventSource = "")
        : type(eventType)
        , priority(getEventPriority(eventType))
        , data(eventData)
        , source(eventSource)
        , timestamp(millis())
        , id(generateEventId()) {
    }
    
    /**
     * 默认构造函数
     */
    EventData()
        : type(EventType::SYSTEM_ERROR)
        , priority(EventPriority::PRIORITY_NORMAL)
        , timestamp(millis())
        , id(generateEventId()) {
    }
    
    /**
     * 生成事件ID
     */
    static uint32_t generateEventId() {
        static uint32_t counter = 0;
        return ++counter;
    }
    
    /**
     * 获取事件优先级 - 匹配前端高优先级事件集合
     */
    static EventPriority getEventPriority(EventType type) {
        switch (type) {
            // 高优先级事件 - 匹配前端highPriorityEvents集合
            case EventType::CONTROL_EMIT_PROGRESS:
            case EventType::CONTROL_SIGNAL_EMITTING:
            case EventType::CONTROL_SIGNAL_EMITTED:
            case EventType::SIGNAL_LEARNING_STATUS_CHANGED:
            case EventType::SYSTEM_ERROR:
            case EventType::TIMER_TASK_DUE:
            case EventType::CONTROL_EMIT_COMPLETED:
            case EventType::TIMER_TASK_EXECUTION_REQUEST:
                return EventPriority::PRIORITY_URGENT;

            default:
                return EventPriority::PRIORITY_NORMAL;
        }
    }
    
    /**
     * 检查事件是否可合并 - 用于事件队列优化
     */
    bool isMergeable() const {
        switch (type) {
            case EventType::CONTROL_EMIT_PROGRESS:
            case EventType::SIGNAL_EMIT_PROGRESS:
            case EventType::LED_STATUS_CHANGED:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 获取事件类型字符串 - 匹配前端事件命名
     */
    String getTypeString() const {
        switch (type) {
            // 系统级事件
            case EventType::SYSTEM_READY: return "system.ready";
            case EventType::SYSTEM_ERROR: return "system.error";
            case EventType::SYSTEM_REFRESH: return "system.refresh";
            case EventType::MODULE_READY: return "module.ready";
            case EventType::MODULE_ERROR: return "module.error";
            case EventType::MODULE_SUCCESS: return "module.success";
            case EventType::ESP32_CONNECTED: return "esp32.connected";
            case EventType::ESP32_DISCONNECTED: return "esp32.disconnected";
            case EventType::ESP32_ERROR: return "esp32.error";
            
            // 信号管理事件
            case EventType::SIGNAL_REQUEST_ALL: return "signal.request.all";
            case EventType::SIGNAL_EMIT_SUCCESS: return "signal.emit.success";
            case EventType::SIGNAL_EMIT_FAILED: return "signal.emit.failed";
            case EventType::SIGNAL_LEARNING_STARTED: return "signal.learning.started";
            case EventType::SIGNAL_LEARNING_STATUS_CHANGED: return "signal.learning.status.changed";
            
            // 控制模块事件
            case EventType::CONTROL_EMIT_PROGRESS: return "control.emit.progress";
            case EventType::CONTROL_SIGNAL_EMITTING: return "control.signal.emitting";
            case EventType::CONTROL_SIGNAL_EMITTED: return "control.signal.emitted";
            case EventType::CONTROL_PAUSE_REQUEST: return "control.pause.request";
            case EventType::CONTROL_RESUME_REQUEST: return "control.resume.request";
            
            // 定时器事件
            case EventType::TIMER_TASK_DUE: return "timer.task.due";
            case EventType::TIMER_TASK_EXECUTION_REQUEST: return "timer.task.execution.request";
            
            default: return "unknown.event";
        }
    }
};

#endif // EVENT_TYPES_H
