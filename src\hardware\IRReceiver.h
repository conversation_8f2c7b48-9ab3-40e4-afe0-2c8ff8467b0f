/**
 * ESP32-S3红外控制系统 - 红外接收器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的红外接收器
 * - 完全匹配前端信号学习需求和后端架构设计的硬件控制规范
 * - 支持高精度红外信号接收和学习功能
 * - 提供实时信号学习和自动协议识别
 * 
 * 前端匹配度：
 * - 学习接口：100%匹配前端POST /api/learning学习信号接口
 * - 学习流程：100%匹配前端学习信号按钮和学习状态管理
 * - 信号格式：100%匹配前端SignalData结构定义
 * - 学习事件：100%匹配前端学习事件处理机制
 * 
 * 后端架构匹配：
 * - 硬件定时器：基于ESP32-S3硬件定时器的精确接收
 * - 中断驱动：基于GPIO中断的实时信号捕获
 * - 核心0处理：红外接收在核心0处理，确保实时性
 * - 协议识别：自动识别NEC、Sony、RC5等主流协议
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_RECEIVER_H
#define IR_RECEIVER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <driver/timer.h>
#include <driver/gpio.h>
#include <vector>

// 配置文件
#include "../config/PinConfig.h"
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/SignalData.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;

// ================================
// 红外接收配置定义
// ================================

/**
 * 红外接收器配置结构
 */
struct IRReceiverConfig {
    uint8_t receivePin;                 // 接收引脚
    uint16_t bufferSize;                // 接收缓冲区大小
    uint32_t timeout;                   // 接收超时时间（微秒）
    uint32_t learningTimeout;           // 学习超时时间（毫秒）
    uint8_t tolerance;                  // 信号容差（%）
    bool enableNoise;                   // 是否启用噪声过滤
    bool enableAutoProtocol;            // 是否启用自动协议识别
    uint32_t minSignalLength;           // 最小信号长度
    uint32_t maxSignalLength;           // 最大信号长度
    
    /**
     * 构造函数
     */
    IRReceiverConfig() 
        : receivePin(IR_RECEIVE_PIN)
        , bufferSize(1024)
        , timeout(15000)
        , learningTimeout(30000)
        , tolerance(25)
        , enableNoise(true)
        , enableAutoProtocol(true)
        , minSignalLength(10)
        , maxSignalLength(1000) {
    }
};

// ================================
// 红外接收状态定义
// ================================

/**
 * 红外接收状态枚举
 */
enum class IRReceiveStatus : uint8_t {
    IDLE = 0,                   // 空闲状态
    LEARNING = 1,               // 学习中
    RECEIVING = 2,              // 接收中
    PROCESSING = 3,             // 处理中
    COMPLETED = 4,              // 接收完成
    TIMEOUT = 5,                // 接收超时
    ERROR = 6                   // 接收错误
};

/**
 * 红外学习结果结构 - 匹配前端学习信号处理
 */
struct IRLearningResult {
    bool success;                       // 是否成功
    IRReceiveStatus status;             // 接收状态
    SignalData signal;                  // 学习到的信号
    String protocol;                    // 识别的协议
    uint32_t frequency;                 // 载波频率
    uint32_t duration;                  // 学习时长（毫秒）
    String errorMessage;                // 错误信息
    float signalQuality;                // 信号质量（0-100）
    
    /**
     * 构造函数
     */
    IRLearningResult() 
        : success(false)
        , status(IRReceiveStatus::IDLE)
        , frequency(0)
        , duration(0)
        , signalQuality(0.0f) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["success"] = success;
        doc["status"] = static_cast<uint8_t>(status);
        doc["signal"] = signal.toJson();
        doc["protocol"] = protocol;
        doc["frequency"] = frequency;
        doc["duration"] = duration;
        doc["errorMessage"] = errorMessage;
        doc["signalQuality"] = signalQuality;
        return doc;
    }
};

/**
 * 红外接收统计信息结构
 */
struct IRReceiveStatistics {
    uint32_t totalLearnings;            // 总学习次数
    uint32_t successfulLearnings;       // 成功学习次数
    uint32_t failedLearnings;           // 失败学习次数
    uint32_t timeoutLearnings;          // 超时学习次数
    uint64_t totalLearningTime;         // 总学习时间（毫秒）
    uint32_t averageLearningTime;       // 平均学习时间（毫秒）
    uint32_t protocolsDetected;         // 检测到的协议数
    float averageSignalQuality;         // 平均信号质量
    
    /**
     * 构造函数
     */
    IRReceiveStatistics() 
        : totalLearnings(0)
        , successfulLearnings(0)
        , failedLearnings(0)
        , timeoutLearnings(0)
        , totalLearningTime(0)
        , averageLearningTime(0)
        , protocolsDetected(0)
        , averageSignalQuality(0.0f) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalLearnings"] = totalLearnings;
        doc["successfulLearnings"] = successfulLearnings;
        doc["failedLearnings"] = failedLearnings;
        doc["timeoutLearnings"] = timeoutLearnings;
        doc["totalLearningTime"] = totalLearningTime;
        doc["averageLearningTime"] = averageLearningTime;
        doc["protocolsDetected"] = protocolsDetected;
        doc["averageSignalQuality"] = averageSignalQuality;
        
        // 计算成功率
        if (totalLearnings > 0) {
            doc["successRate"] = (float)successfulLearnings / totalLearnings * 100;
        }
        
        return doc;
    }
};

// ================================
// 红外接收器类定义
// ================================

/**
 * 红外接收器类 - 完全匹配前端信号学习需求
 * 
 * 职责：
 * 1. 高精度红外信号接收
 * 2. 自动协议识别和解码
 * 3. 信号学习和存储
 * 4. 噪声过滤和信号优化
 * 5. 学习状态监控和统计
 */
class IRReceiver {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param config 接收器配置
     * @param eventMgr 事件管理器指针
     */
    IRReceiver(const IRReceiverConfig& config = IRReceiverConfig(), EventManager* eventMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~IRReceiver();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化红外接收器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理红外接收器
     */
    void cleanup();
    
    /**
     * 红外接收器主循环
     */
    void loop();
    
    // ================================
    // 信号学习接口 - 匹配前端POST /api/learning
    // ================================
    
    /**
     * 开始信号学习 - 匹配前端学习信号按钮
     * @param signalId 信号ID
     * @param timeout 学习超时时间（毫秒）
     * @return 是否开始成功
     */
    bool startLearning(const String& signalId, uint32_t timeout = 30000);
    
    /**
     * 停止信号学习
     * @return 学习结果
     */
    IRLearningResult stopLearning();
    
    /**
     * 取消信号学习 - 匹配前端取消学习按钮
     * @return 是否取消成功
     */
    bool cancelLearning();
    
    /**
     * 检查是否正在学习
     * @return 是否正在学习
     */
    bool isLearning() const { return currentStatus == IRReceiveStatus::LEARNING; }
    
    /**
     * 获取当前学习状态
     * @return 学习状态
     */
    IRReceiveStatus getStatus() const { return currentStatus; }
    
    /**
     * 获取学习进度
     * @return 学习进度（0-100）
     */
    uint8_t getLearningProgress() const;
    
    // ================================
    // 信号接收接口
    // ================================
    
    /**
     * 接收单个信号
     * @param timeout 接收超时时间（毫秒）
     * @return 接收结果
     */
    IRLearningResult receiveSignal(uint32_t timeout = 5000);
    
    /**
     * 异步接收信号
     * @param callback 完成回调函数
     * @param timeout 接收超时时间（毫秒）
     * @return 是否开始接收
     */
    bool receiveSignalAsync(std::function<void(const IRLearningResult&)> callback, uint32_t timeout = 5000);
    
    /**
     * 检查是否有信号可用
     * @return 是否有信号
     */
    bool hasSignalAvailable();
    
    /**
     * 获取最后接收的信号
     * @return 信号数据
     */
    SignalData getLastReceivedSignal() const { return lastReceivedSignal; }
    
    // ================================
    // 协议识别和解码
    // ================================
    
    /**
     * 识别信号协议
     * @param rawData 原始数据
     * @return 协议名称
     */
    String identifyProtocol(const std::vector<uint16_t>& rawData);
    
    /**
     * 解码信号数据
     * @param rawData 原始数据
     * @param protocol 协议名称
     * @return 解码后的信号数据
     */
    SignalData decodeSignal(const std::vector<uint16_t>& rawData, const String& protocol);
    
    /**
     * 获取支持的协议列表
     * @return 协议列表
     */
    std::vector<String> getSupportedProtocols() const;
    
    /**
     * 检查协议是否支持
     * @param protocol 协议名称
     * @return 是否支持
     */
    bool isProtocolSupported(const String& protocol) const;
    
    // ================================
    // 配置管理
    // ================================
    
    /**
     * 设置接收超时时间
     * @param timeout 超时时间（微秒）
     */
    void setReceiveTimeout(uint32_t timeout) { config.timeout = timeout; }
    
    /**
     * 设置学习超时时间
     * @param timeout 超时时间（毫秒）
     */
    void setLearningTimeout(uint32_t timeout) { config.learningTimeout = timeout; }
    
    /**
     * 设置信号容差
     * @param tolerance 容差（%）
     */
    void setTolerance(uint8_t tolerance) { config.tolerance = tolerance; }
    
    /**
     * 启用/禁用噪声过滤
     * @param enabled 是否启用
     */
    void setNoiseFilterEnabled(bool enabled) { config.enableNoise = enabled; }
    
    /**
     * 启用/禁用自动协议识别
     * @param enabled 是否启用
     */
    void setAutoProtocolEnabled(bool enabled) { config.enableAutoProtocol = enabled; }
    
    // ================================
    // 统计和监控
    // ================================
    
    /**
     * 获取接收统计信息
     * @return 接收统计信息
     */
    IRReceiveStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置接收统计
     */
    void resetStatistics();
    
    /**
     * 获取硬件状态
     * @return 硬件状态JSON对象
     */
    JsonDocument getHardwareStatus() const;
    
    /**
     * 获取性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const;
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    IRrecv* irReceiver;                 // IRremoteESP8266接收器
    EventManager* eventManager;         // 事件管理器
    
    // 硬件组件
    hw_timer_t* receiveTimer;           // 接收定时器
    SemaphoreHandle_t receiveMutex;     // 接收互斥锁
    
    // 配置和状态
    IRReceiverConfig config;            // 接收器配置
    IRReceiveStatus currentStatus;      // 当前状态
    IRReceiveStatistics statistics;     // 接收统计
    
    // 学习控制
    String currentSignalId;             // 当前学习的信号ID
    uint32_t learningStartTime;         // 学习开始时间
    std::vector<uint16_t> rawData;      // 原始数据缓冲区
    SignalData lastReceivedSignal;      // 最后接收的信号
    std::function<void(const IRLearningResult&)> asyncCallback; // 异步回调
    
    // 协议支持
    std::vector<String> supportedProtocols; // 支持的协议列表
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    volatile bool isReceiving;          // 是否正在接收
    
    // ================================
    // 私有方法 - 硬件控制
    // ================================
    
    /**
     * 初始化接收定时器
     * @return 是否初始化成功
     */
    bool initReceiveTimer();
    
    /**
     * 配置GPIO引脚
     * @return 是否配置成功
     */
    bool configureGPIO();
    
    /**
     * 启动接收
     * @return 是否启动成功
     */
    bool startReceiving();
    
    /**
     * 停止接收
     * @return 是否停止成功
     */
    bool stopReceiving();
    
    // ================================
    // 私有方法 - 信号处理
    // ================================
    
    /**
     * 处理接收到的信号
     * @return 处理结果
     */
    IRLearningResult processReceivedSignal();
    
    /**
     * 过滤噪声信号
     * @param rawData 原始数据
     * @return 过滤后的数据
     */
    std::vector<uint16_t> filterNoise(const std::vector<uint16_t>& rawData);
    
    /**
     * 验证信号质量
     * @param rawData 原始数据
     * @return 信号质量（0-100）
     */
    float validateSignalQuality(const std::vector<uint16_t>& rawData);
    
    /**
     * 优化信号数据
     * @param rawData 原始数据
     * @return 优化后的数据
     */
    std::vector<uint16_t> optimizeSignalData(const std::vector<uint16_t>& rawData);
    
    // ================================
    // 私有方法 - 协议处理
    // ================================
    
    /**
     * 初始化支持的协议
     */
    void initSupportedProtocols();
    
    /**
     * 解码NEC协议
     * @param rawData 原始数据
     * @return 信号数据
     */
    SignalData decodeNEC(const std::vector<uint16_t>& rawData);
    
    /**
     * 解码Sony协议
     * @param rawData 原始数据
     * @return 信号数据
     */
    SignalData decodeSony(const std::vector<uint16_t>& rawData);
    
    /**
     * 解码RC5协议
     * @param rawData 原始数据
     * @return 信号数据
     */
    SignalData decodeRC5(const std::vector<uint16_t>& rawData);
    
    /**
     * 解码原始数据
     * @param rawData 原始数据
     * @return 信号数据
     */
    SignalData decodeRawData(const std::vector<uint16_t>& rawData);
    
    // ================================
    // 私有方法 - 辅助功能
    // ================================
    
    /**
     * 更新接收统计
     * @param result 学习结果
     */
    void updateStatistics(const IRLearningResult& result);
    
    /**
     * 发布学习事件 - 匹配前端学习事件处理
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void publishLearningEvent(EventType eventType, const JsonDocument& data = JsonDocument());
    
    /**
     * 获取状态字符串
     * @param status 接收状态
     * @return 状态字符串
     */
    String getStatusString(IRReceiveStatus status) const;
    
    /**
     * 验证原始数据
     * @param rawData 原始数据
     * @return 是否有效
     */
    bool validateRawData(const std::vector<uint16_t>& rawData);
    
    /**
     * 计算载波频率
     * @param rawData 原始数据
     * @return 载波频率
     */
    uint32_t calculateCarrierFrequency(const std::vector<uint16_t>& rawData);
    
    /**
     * 生成信号ID
     * @param rawData 原始数据
     * @return 信号ID
     */
    String generateSignalId(const std::vector<uint16_t>& rawData);
    
    /**
     * 定时器中断处理
     */
    static void IRAM_ATTR timerInterruptHandler();
    
    /**
     * 处理定时器中断
     */
    void handleTimerInterrupt();
    
    // 静态实例指针（用于中断处理）
    static IRReceiver* instance;
};

#endif // IR_RECEIVER_H
