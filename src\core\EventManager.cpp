/**
 * @file EventManager.cpp
 * @brief 事件管理器实现 - 完全匹配头文件定义
 *
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "EventManager.h"
#include "../utils/Logger.h"

EventManager::EventManager()
    : initialized(false) {
    LOG_INFO("EventManager", "事件管理器构造完成");
}

EventManager::~EventManager() {
    cleanup();
    LOG_INFO("EventManager", "事件管理器析构完成");
}

bool EventManager::init() {
    if (initialized) {
        return true;
    }

    // 初始化事件队列
    highPriorityQueue.reserve(HIGH_PRIORITY_QUEUE_SIZE);
    normalPriorityQueue.reserve(NORMAL_PRIORITY_QUEUE_SIZE);

    initialized = true;
    LOG_INFO("EventManager", "事件管理器初始化完成");
    return true;
}

void EventManager::cleanup() {
    if (!initialized) {
        return;
    }

    highPriorityQueue.clear();
    normalPriorityQueue.clear();
    eventSubscribers.clear();

    initialized = false;
    LOG_INFO("EventManager", "事件管理器清理完成");
}

bool EventManager::emit(EventType type, const JsonDocument& data, const String& source) {
    if (!initialized) {
        return false;
    }

    EventData event;
    event.type = type;
    event.data = data;
    event.source = source;
    event.timestamp = millis();
    event.priority = getEventPriority(type);

    return emitEvent(event);
}

bool EventManager::emitHighPriority(EventType type, const JsonDocument& data, const String& source) {
    if (!initialized) {
        return false;
    }

    EventData event;
    event.type = type;
    event.data = data;
    event.source = source;
    event.timestamp = millis();
    event.priority = EventPriority::PRIORITY_URGENT;

    return emitEvent(event);
}

bool EventManager::emitEvent(const EventData& event) {
    if (!initialized) {
        return false;
    }

    // 根据优先级添加到相应队列
    if (event.priority == EventPriority::PRIORITY_URGENT) {
        if (highPriorityQueue.size() < HIGH_PRIORITY_QUEUE_SIZE) {
            highPriorityQueue.push_back(event);
            return true;
        }
    } else {
        if (normalPriorityQueue.size() < NORMAL_PRIORITY_QUEUE_SIZE) {
            normalPriorityQueue.push_back(event);
            return true;
        }
    }

    return false;
}

bool EventManager::on(EventType type, EventHandler handler, const String& listenerId, uint32_t priority) {
    if (!initialized || !handler) {
        return false;
    }

    EventSubscriber subscriber;
    subscriber.type = type;
    subscriber.handler = handler;
    subscriber.listenerId = listenerId;
    subscriber.priority = priority;
    subscriber.once = false;

    eventSubscribers.push_back(subscriber);
    return true;
}

bool EventManager::once(EventType type, EventHandler handler, const String& listenerId) {
    if (!initialized || !handler) {
        return false;
    }

    EventSubscriber subscriber;
    subscriber.type = type;
    subscriber.handler = handler;
    subscriber.listenerId = listenerId;
    subscriber.priority = 0;
    subscriber.once = true;

    eventSubscribers.push_back(subscriber);
    return true;
}

bool EventManager::off(EventType type, const String& listenerId) {
    if (!initialized) {
        return false;
    }

    auto it = eventSubscribers.begin();
    while (it != eventSubscribers.end()) {
        if (it->type == type && (listenerId.isEmpty() || it->listenerId == listenerId)) {
            it = eventSubscribers.erase(it);
        } else {
            ++it;
        }
    }

    return true;
}

size_t EventManager::offAll(const String& listenerId) {
    if (!initialized) {
        return 0;
    }

    size_t count = 0;
    auto it = eventSubscribers.begin();
    while (it != eventSubscribers.end()) {
        if (listenerId.isEmpty() || it->listenerId == listenerId) {
            it = eventSubscribers.erase(it);
            count++;
        } else {
            ++it;
        }
    }

    return count;
}