/**
 * ESP32-S3红外控制系统 - 硬件管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的硬件管理器
 * - 统一管理所有硬件组件的初始化、控制和监控
 * - 提供硬件抽象层，简化上层服务的硬件访问
 * - 支持硬件状态监控、错误检测和自动恢复
 * 
 * 前端匹配度：
 * - 硬件状态：100%匹配前端硬件状态监控和显示
 * - 错误处理：100%匹配前端硬件错误提示和处理
 * - 状态LED：100%匹配前端状态指示和学习模式显示
 * - 红外控制：100%匹配前端红外发射和学习功能
 * 
 * 后端架构匹配：
 * - 核心0任务：红外控制、状态LED在核心0实时处理
 * - 硬件抽象：统一的硬件接口和状态管理
 * - 事件驱动：完整的硬件事件发布和状态同步
 * - 错误恢复：自动硬件错误检测和恢复机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef HARDWARE_MANAGER_H
#define HARDWARE_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <memory>

// 配置文件
#include "../config/SystemConfig.h"
#include "../config/PinConfig.h"

// 数据类型
#include "../types/EventTypes.h"

// 前向声明
class EventManager;
class IRTransmitter;
class IRReceiver;
class StatusLED;

// ================================
// 硬件状态枚举定义
// ================================

/**
 * 硬件组件状态枚举
 */
enum class HardwareState : uint8_t {
    UNINITIALIZED = 0,      // 未初始化
    INITIALIZING = 1,       // 初始化中
    READY = 2,              // 就绪
    ACTIVE = 3,             // 活动中
    ERROR = 4,              // 错误
    DISABLED = 5            // 已禁用
};

/**
 * 硬件组件类型枚举
 */
enum class HardwareType : uint8_t {
    IR_TRANSMITTER = 0,     // 红外发射器
    IR_RECEIVER = 1,        // 红外接收器
    STATUS_LED = 2,         // 状态LED
    BUTTON = 3,             // 按键
    POWER_CONTROL = 4       // 电源控制
};

// ================================
// 硬件组件信息定义
// ================================

/**
 * 硬件组件信息结构
 */
struct HardwareComponentInfo {
    HardwareType type;              // 组件类型
    String name;                    // 组件名称
    HardwareState state;            // 组件状态
    uint8_t pin;                    // 引脚号
    uint32_t lastActivity;          // 最后活动时间
    uint32_t errorCount;            // 错误计数
    String lastError;               // 最后错误信息
    bool enabled;                   // 是否启用
    
    /**
     * 构造函数
     */
    HardwareComponentInfo(HardwareType hwType, const String& hwName, uint8_t hwPin)
        : type(hwType)
        , name(hwName)
        , state(HardwareState::UNINITIALIZED)
        , pin(hwPin)
        , lastActivity(0)
        , errorCount(0)
        , enabled(true) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["type"] = static_cast<uint8_t>(type);
        doc["name"] = name;
        doc["state"] = static_cast<uint8_t>(state);
        doc["pin"] = pin;
        doc["lastActivity"] = lastActivity;
        doc["errorCount"] = errorCount;
        doc["lastError"] = lastError;
        doc["enabled"] = enabled;
        return doc;
    }
};

// ================================
// 硬件统计信息定义
// ================================

/**
 * 硬件统计信息结构
 */
struct HardwareStatistics {
    uint32_t totalOperations;       // 总操作次数
    uint32_t successfulOperations;  // 成功操作次数
    uint32_t failedOperations;      // 失败操作次数
    uint32_t totalErrors;           // 总错误次数
    uint32_t recoveredErrors;       // 已恢复错误次数
    uint32_t uptime;                // 运行时间（秒）
    float averageResponseTime;      // 平均响应时间（毫秒）
    
    /**
     * 构造函数
     */
    HardwareStatistics() 
        : totalOperations(0)
        , successfulOperations(0)
        , failedOperations(0)
        , totalErrors(0)
        , recoveredErrors(0)
        , uptime(0)
        , averageResponseTime(0.0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalOperations"] = totalOperations;
        doc["successfulOperations"] = successfulOperations;
        doc["failedOperations"] = failedOperations;
        doc["totalErrors"] = totalErrors;
        doc["recoveredErrors"] = recoveredErrors;
        doc["uptime"] = uptime;
        doc["averageResponseTime"] = averageResponseTime;
        
        // 计算成功率
        if (totalOperations > 0) {
            doc["successRate"] = (float)successfulOperations / totalOperations * 100;
        }
        
        return doc;
    }
};

// ================================
// 硬件管理器类定义
// ================================

/**
 * 硬件管理器类 - 统一硬件组件管理
 * 
 * 职责：
 * 1. 硬件组件初始化和配置
 * 2. 硬件状态监控和管理
 * 3. 硬件错误检测和恢复
 * 4. 硬件性能统计和分析
 * 5. 硬件事件发布和通知
 */
class HardwareManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    HardwareManager(EventManager* eventMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~HardwareManager();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化硬件管理器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理硬件资源
     */
    void cleanup();
    
    /**
     * 硬件管理器主循环
     */
    void loop();
    
    // ================================
    // 硬件组件访问
    // ================================
    
    /**
     * 获取红外发射器
     * @return 红外发射器指针
     */
    IRTransmitter* getIRTransmitter() const { return irTransmitter; }
    
    /**
     * 获取红外接收器
     * @return 红外接收器指针
     */
    IRReceiver* getIRReceiver() const { return irReceiver; }
    
    /**
     * 获取状态LED控制器
     * @return 状态LED控制器指针
     */
    StatusLED* getStatusLED() const { return statusLED; }
    
    // ================================
    // 硬件状态管理
    // ================================
    
    /**
     * 获取硬件组件状态
     * @param type 硬件类型
     * @return 硬件状态
     */
    HardwareState getHardwareState(HardwareType type) const;
    
    /**
     * 设置硬件组件状态
     * @param type 硬件类型
     * @param state 新状态
     */
    void setHardwareState(HardwareType type, HardwareState state);
    
    /**
     * 检查硬件是否就绪
     * @param type 硬件类型
     * @return 是否就绪
     */
    bool isHardwareReady(HardwareType type) const;
    
    /**
     * 检查所有硬件是否就绪
     * @return 是否所有硬件都就绪
     */
    bool isAllHardwareReady() const;
    
    /**
     * 启用硬件组件
     * @param type 硬件类型
     * @return 是否启用成功
     */
    bool enableHardware(HardwareType type);
    
    /**
     * 禁用硬件组件
     * @param type 硬件类型
     * @return 是否禁用成功
     */
    bool disableHardware(HardwareType type);
    
    // ================================
    // 硬件控制接口
    // ================================
    
    /**
     * 重置硬件组件
     * @param type 硬件类型
     * @return 是否重置成功
     */
    bool resetHardware(HardwareType type);
    
    /**
     * 重置所有硬件
     * @return 是否重置成功
     */
    bool resetAllHardware();
    
    /**
     * 测试硬件组件
     * @param type 硬件类型
     * @return 测试结果
     */
    bool testHardware(HardwareType type);
    
    /**
     * 测试所有硬件
     * @return 测试结果JSON对象
     */
    JsonDocument testAllHardware();
    
    // ================================
    // 硬件信息查询
    // ================================
    
    /**
     * 获取硬件组件信息
     * @param type 硬件类型
     * @return 硬件组件信息
     */
    HardwareComponentInfo getHardwareInfo(HardwareType type) const;
    
    /**
     * 获取所有硬件信息
     * @return 硬件信息列表
     */
    std::vector<HardwareComponentInfo> getAllHardwareInfo() const;
    
    /**
     * 获取硬件状态摘要
     * @return 状态摘要JSON对象
     */
    JsonDocument getHardwareStatusSummary() const;
    
    /**
     * 获取硬件统计信息
     * @return 统计信息
     */
    HardwareStatistics getHardwareStatistics() const { return statistics; }
    
    // ================================
    // 错误处理和恢复
    // ================================
    
    /**
     * 处理硬件错误
     * @param type 硬件类型
     * @param error 错误信息
     */
    void handleHardwareError(HardwareType type, const String& error);
    
    /**
     * 尝试恢复硬件错误
     * @param type 硬件类型
     * @return 是否恢复成功
     */
    bool recoverHardwareError(HardwareType type);
    
    /**
     * 获取硬件错误计数
     * @param type 硬件类型
     * @return 错误计数
     */
    uint32_t getHardwareErrorCount(HardwareType type) const;
    
    /**
     * 清除硬件错误计数
     * @param type 硬件类型
     */
    void clearHardwareErrorCount(HardwareType type);
    
    // ================================
    // 性能监控
    // ================================
    
    /**
     * 更新硬件活动时间
     * @param type 硬件类型
     */
    void updateHardwareActivity(HardwareType type);
    
    /**
     * 记录硬件操作
     * @param type 硬件类型
     * @param success 是否成功
     * @param responseTime 响应时间（毫秒）
     */
    void recordHardwareOperation(HardwareType type, bool success, uint32_t responseTime);
    
    /**
     * 重置硬件统计
     */
    void resetHardwareStatistics();
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }
    
    /**
     * 发布硬件事件
     * @param type 事件类型
     * @param data 事件数据
     */
    void publishHardwareEvent(EventType type, const JsonDocument& data = JsonDocument());

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 事件管理器
    EventManager* eventManager;
    
    // 硬件组件
    IRTransmitter* irTransmitter;       // 红外发射器
    IRReceiver* irReceiver;             // 红外接收器
    StatusLED* statusLED;               // 状态LED
    
    // 硬件组件信息
    std::vector<HardwareComponentInfo> hardwareComponents;
    
    // 统计信息
    HardwareStatistics statistics;
    
    // 监控状态
    uint32_t lastHealthCheck;           // 上次健康检查时间
    uint32_t healthCheckInterval;       // 健康检查间隔
    uint32_t startTime;                 // 启动时间
    
    // 系统标志
    bool initialized;                   // 是否已初始化
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化硬件组件
     * @return 是否初始化成功
     */
    bool initHardwareComponents();
    
    /**
     * 初始化红外发射器
     * @return 是否初始化成功
     */
    bool initIRTransmitter();
    
    /**
     * 初始化红外接收器
     * @return 是否初始化成功
     */
    bool initIRReceiver();
    
    /**
     * 初始化状态LED
     * @return 是否初始化成功
     */
    bool initStatusLED();
    
    /**
     * 执行硬件健康检查
     */
    void performHealthCheck();
    
    /**
     * 检查单个硬件组件健康状态
     * @param type 硬件类型
     * @return 是否健康
     */
    bool checkHardwareHealth(HardwareType type);
    
    /**
     * 获取硬件组件索引
     * @param type 硬件类型
     * @return 组件索引
     */
    int getHardwareIndex(HardwareType type) const;
    
    /**
     * 更新硬件统计
     */
    void updateStatistics();
    
    /**
     * 获取硬件类型字符串
     * @param type 硬件类型
     * @return 类型字符串
     */
    String getHardwareTypeString(HardwareType type) const;
    
    /**
     * 获取硬件状态字符串
     * @param state 硬件状态
     * @return 状态字符串
     */
    String getHardwareStateString(HardwareState state) const;
    
    /**
     * 记录硬件错误
     * @param type 硬件类型
     * @param error 错误信息
     */
    void logHardwareError(HardwareType type, const String& error);
    
    /**
     * 发布硬件状态变化事件
     * @param type 硬件类型
     * @param oldState 旧状态
     * @param newState 新状态
     */
    void publishStateChangeEvent(HardwareType type, HardwareState oldState, HardwareState newState);
};

#endif // HARDWARE_MANAGER_H
