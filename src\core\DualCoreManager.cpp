/**
 * @file DualCoreManager.cpp
 * @brief 双核管理器实现 - 完全匹配头文件定义
 *
 * 功能说明：
 * - ESP32-S3双核任务管理
 * - 核心间通信
 * - 性能监控
 * - 硬件管理器集成
 *
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "DualCoreManager.h"
#include "EventManager.h"
#include "../utils/Logger.h"

DualCoreManager::DualCoreManager()
    : core0TaskHandle(nullptr)
    , core1TaskHandle(nullptr)
    , watchdogTaskHandle(nullptr)
    , interCoreQueue(nullptr)
    , sharedDataMutex(nullptr)
    , core0Status(0)
    , core1Status(1)
    , lastPerformanceCheck(0)
    , performanceCheckInterval(1000)
    , eventManager(nullptr)
    , hardwareManager(nullptr)
    , initialized(false)
    , tasksStarted(false) {

    // 设置初始状态
    core0Status.state = CoreState::STOPPED;
    core1Status.state = CoreState::STOPPED;

    LOG_INFO("DualCoreManager", "双核管理器构造完成");
}

DualCoreManager::~DualCoreManager() {
    cleanup();
    LOG_INFO("DualCoreManager", "双核管理器析构完成");
}

bool DualCoreManager::init() {
    if (initialized) {
        LOG_WARNING("DualCoreManager", "双核管理器已经初始化");
        return true;
    }

    LOG_INFO("DualCoreManager", "开始初始化双核管理器...");

    // 创建核心间通信队列
    interCoreQueue = xQueueCreate(32, sizeof(CoreMessage));
    if (interCoreQueue == nullptr) {
        LOG_ERROR("DualCoreManager", "创建核心间通信队列失败");
        return false;
    }

    // 创建共享数据互斥锁
    sharedDataMutex = xSemaphoreCreateMutex();
    if (sharedDataMutex == nullptr) {
        LOG_ERROR("DualCoreManager", "创建共享数据互斥锁失败");
        return false;
    }

    // 初始化性能检查
    lastPerformanceCheck = millis();

    initialized = true;
    LOG_INFO("DualCoreManager", "双核管理器初始化完成");
    return true;
}

void DualCoreManager::cleanup() {
    if (!initialized) {
        return;
    }

    LOG_INFO("DualCoreManager", "开始清理双核管理器...");

    // 停止任务
    stopCoreTasks();

    // 删除队列
    if (interCoreQueue != nullptr) {
        vQueueDelete(interCoreQueue);
        interCoreQueue = nullptr;
    }

    // 删除互斥锁
    if (sharedDataMutex != nullptr) {
        vSemaphoreDelete(sharedDataMutex);
        sharedDataMutex = nullptr;
    }

    initialized = false;
    LOG_INFO("DualCoreManager", "双核管理器清理完成");
}

bool DualCoreManager::startCoreTasks() {
    if (!initialized) {
        LOG_ERROR("DualCoreManager", "双核管理器未初始化");
        return false;
    }

    if (tasksStarted) {
        LOG_WARNING("DualCoreManager", "双核任务已经启动");
        return true;
    }

    LOG_INFO("DualCoreManager", "启动双核任务...");

    // 启动核心0任务
    if (!startCore0Tasks()) {
        LOG_ERROR("DualCoreManager", "启动核心0任务失败");
        return false;
    }

    // 启动核心1任务
    if (!startCore1Tasks()) {
        LOG_ERROR("DualCoreManager", "启动核心1任务失败");
        return false;
    }

    tasksStarted = true;
    LOG_INFO("DualCoreManager", "双核任务启动完成");
    return true;
}

bool DualCoreManager::stopCoreTasks() {
    if (!tasksStarted) {
        LOG_WARNING("DualCoreManager", "双核任务未启动");
        return true;
    }

    LOG_INFO("DualCoreManager", "停止双核任务...");

    tasksStarted = false;

    // 停止核心0任务
    if (core0TaskHandle != nullptr) {
        vTaskDelete(core0TaskHandle);
        core0TaskHandle = nullptr;
        core0Status.state = CoreState::STOPPED;
    }

    // 停止核心1任务
    if (core1TaskHandle != nullptr) {
        vTaskDelete(core1TaskHandle);
        core1TaskHandle = nullptr;
        core1Status.state = CoreState::STOPPED;
    }

    // 停止看门狗任务
    if (watchdogTaskHandle != nullptr) {
        vTaskDelete(watchdogTaskHandle);
        watchdogTaskHandle = nullptr;
    }

    LOG_INFO("DualCoreManager", "双核任务停止完成");
    return true;
}

bool DualCoreManager::startCore0Tasks() {
    LOG_INFO("DualCoreManager", "启动核心0任务...");

    BaseType_t result = xTaskCreatePinnedToCore(
        core0TaskFunction,
        "Core0_RealTime",
        4096,
        this,
        2,
        &core0TaskHandle,
        0
    );

    if (result != pdPASS) {
        LOG_ERROR("DualCoreManager", "创建核心0任务失败");
        return false;
    }

    core0Status.state = CoreState::RUNNING;
    LOG_INFO("DualCoreManager", "核心0任务启动成功");
    return true;
}

bool DualCoreManager::startCore1Tasks() {
    LOG_INFO("DualCoreManager", "启动核心1任务...");

    BaseType_t result = xTaskCreatePinnedToCore(
        core1TaskFunction,
        "Core1_Network",
        8192,
        this,
        1,
        &core1TaskHandle,
        1
    );

    if (result != pdPASS) {
        LOG_ERROR("DualCoreManager", "创建核心1任务失败");
        return false;
    }

    core1Status.state = CoreState::RUNNING;
    LOG_INFO("DualCoreManager", "核心1任务启动成功");
    return true;
}

bool DualCoreManager::sendInterCoreMessage(const CoreMessage& message, uint32_t timeout) {
    if (interCoreQueue == nullptr) {
        return false;
    }

    BaseType_t result = xQueueSend(interCoreQueue, &message, pdMS_TO_TICKS(timeout));
    return (result == pdPASS);
}

bool DualCoreManager::receiveInterCoreMessage(CoreMessage& message, uint32_t timeout) {
    if (interCoreQueue == nullptr) {
        return false;
    }

    BaseType_t result = xQueueReceive(interCoreQueue, &message, pdMS_TO_TICKS(timeout));
    return (result == pdPASS);
}

bool DualCoreManager::broadcastMessage(const CoreMessage& message) {
    return sendInterCoreMessage(message, 100);
}

CoreStatus DualCoreManager::getCoreStatus(uint8_t coreId) const {
    if (coreId == 0) {
        return core0Status;
    } else if (coreId == 1) {
        return core1Status;
    } else {
        CoreStatus errorStatus(coreId);
        errorStatus.state = CoreState::ERROR;
        return errorStatus;
    }
}

void DualCoreManager::core0TaskFunction(void* parameter) {
    DualCoreManager* manager = static_cast<DualCoreManager*>(parameter);
    LOG_INFO("DualCoreManager", "核心0任务开始运行");

    while (manager->tasksStarted) {
        // 处理实时控制任务
        // 注意：HardwareManager需要前向声明，这里暂时注释掉
        // if (manager->hardwareManager != nullptr) {
        //     manager->hardwareManager->loop();
        // }

        // 处理核心间消息
        CoreMessage message;
        if (manager->receiveInterCoreMessage(message, 1)) {
            // 处理消息
        }

        vTaskDelay(pdMS_TO_TICKS(1));
    }

    LOG_INFO("DualCoreManager", "核心0任务结束");
    vTaskDelete(nullptr);
}

void DualCoreManager::core1TaskFunction(void* parameter) {
    DualCoreManager* manager = static_cast<DualCoreManager*>(parameter);
    LOG_INFO("DualCoreManager", "核心1任务开始运行");

    while (manager->tasksStarted) {
        // 处理网络任务
        // 这里可以添加网络处理逻辑

        // 处理核心间消息
        CoreMessage message;
        if (manager->receiveInterCoreMessage(message, 10)) {
            // 处理消息
        }

        vTaskDelay(pdMS_TO_TICKS(10));
    }

    LOG_INFO("DualCoreManager", "核心1任务结束");
    vTaskDelete(nullptr);
}