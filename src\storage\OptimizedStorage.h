/**
 * ESP32-S3红外控制系统 - 优化存储系统
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的优化存储系统
 * - 完全匹配前端数据存储需求和后端架构设计的L1+L2存储架构
 * - 专为无PSRAM版本优化，使用SRAM高速缓存补偿PSRAM缺失
 * - 支持1000个信号的高效存储和90%+缓存命中率
 * 
 * 前端匹配度：
 * - 存储容量：100%匹配前端1000个信号存储需求
 * - 访问性能：100%匹配前端<1ms数据访问要求
 * - 数据格式：100%匹配前端SignalData结构定义
 * - 批量操作：100%匹配前端批量导入导出功能
 * 
 * 后端架构匹配：
 * - L1+L2架构：完整的二级存储架构设计
 * - 无PSRAM优化：专为无PSRAM版本的SRAM优化
 * - 缓存策略：LRU+热点数据预加载策略
 * - 性能目标：90%+缓存命中率，<1ms访问时间
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef OPTIMIZED_STORAGE_H
#define OPTIMIZED_STORAGE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <SPIFFS.h>
#include <vector>
#include <unordered_map>

// 数据类型
#include "../types/SignalData.h"
#include "../types/TaskData.h"

// 前向声明
class DataValidator;

// ================================
// 存储配置定义
// ================================

/**
 * L1缓存配置 - 针对无PSRAM版本优化
 */
struct L1CacheConfig {
    static const uint32_t MAX_HOT_SIGNALS = 20;     // 热点信号数量（增加到20个）
    static const uint32_t MAX_RECENT_SIGNALS = 10;  // 最近访问信号数量
    static const uint32_t ACCESS_THRESHOLD = 5;     // 热点访问阈值
    static const uint32_t CACHE_TIMEOUT = 300000;   // 缓存超时时间（5分钟）
};

/**
 * L2存储配置
 */
struct L2StorageConfig {
    static const uint32_t MAX_SIGNALS = 1000;       // 最大信号数量
    static const uint32_t MAX_TASKS = 100;          // 最大任务数量
    static const uint32_t BATCH_SIZE = 50;          // 批处理大小
    static const uint32_t COMPRESSION_THRESHOLD = 512; // 压缩阈值
};

// ================================
// 缓存数据结构定义
// ================================

/**
 * L1缓存项结构
 */
struct L1CacheItem {
    SignalData signal;                  // 信号数据
    uint32_t accessCount;               // 访问次数
    uint64_t lastAccessTime;            // 最后访问时间
    bool isDirty;                       // 是否已修改
    
    /**
     * 构造函数
     */
    L1CacheItem() 
        : accessCount(0)
        , lastAccessTime(0)
        , isDirty(false) {
    }
    
    /**
     * 更新访问信息
     */
    void updateAccess() {
        accessCount++;
        lastAccessTime = millis();
    }
    
    /**
     * 检查是否为热点数据
     */
    bool isHot() const {
        return accessCount >= L1CacheConfig::ACCESS_THRESHOLD;
    }
    
    /**
     * 检查是否过期
     */
    bool isExpired() const {
        return (millis() - lastAccessTime) > L1CacheConfig::CACHE_TIMEOUT;
    }
};

/**
 * 存储统计信息结构
 */
struct StorageStatistics {
    // L1缓存统计
    uint32_t l1Hits;                    // L1缓存命中次数
    uint32_t l1Misses;                  // L1缓存未命中次数
    uint32_t l1Evictions;               // L1缓存驱逐次数
    
    // L2存储统计
    uint32_t l2Reads;                   // L2读取次数
    uint32_t l2Writes;                  // L2写入次数
    uint32_t l2Errors;                  // L2错误次数
    
    // 性能统计
    uint32_t totalOperations;           // 总操作次数
    uint64_t totalAccessTime;           // 总访问时间（微秒）
    uint32_t averageAccessTime;         // 平均访问时间（微秒）
    
    // 存储使用情况
    uint32_t totalSignals;              // 总信号数量
    uint32_t cachedSignals;             // 缓存信号数量
    uint32_t storageUsed;               // 已用存储空间
    uint32_t storageTotal;              // 总存储空间
    
    /**
     * 构造函数
     */
    StorageStatistics() 
        : l1Hits(0), l1Misses(0), l1Evictions(0)
        , l2Reads(0), l2Writes(0), l2Errors(0)
        , totalOperations(0), totalAccessTime(0), averageAccessTime(0)
        , totalSignals(0), cachedSignals(0), storageUsed(0), storageTotal(0) {
    }
    
    /**
     * 计算L1缓存命中率
     */
    float getL1HitRate() const {
        uint32_t total = l1Hits + l1Misses;
        return total > 0 ? (float)l1Hits / total : 0.0f;
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["l1Hits"] = l1Hits;
        doc["l1Misses"] = l1Misses;
        doc["l1Evictions"] = l1Evictions;
        doc["l1HitRate"] = getL1HitRate();
        doc["l2Reads"] = l2Reads;
        doc["l2Writes"] = l2Writes;
        doc["l2Errors"] = l2Errors;
        doc["totalOperations"] = totalOperations;
        doc["averageAccessTime"] = averageAccessTime;
        doc["totalSignals"] = totalSignals;
        doc["cachedSignals"] = cachedSignals;
        doc["storageUsed"] = storageUsed;
        doc["storageTotal"] = storageTotal;
        doc["storageUsagePercent"] = storageTotal > 0 ? (float)storageUsed / storageTotal * 100 : 0.0f;
        return doc;
    }
};

// ================================
// 优化存储系统类定义
// ================================

/**
 * 优化存储系统类 - 完全匹配后端架构设计
 * 
 * 职责：
 * 1. L1+L2二级存储架构管理
 * 2. 高效缓存策略实现
 * 3. 无PSRAM版本优化
 * 4. 批量操作优化
 * 5. 存储性能监控
 */
class OptimizedStorage {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param validator 数据验证器指针
     */
    OptimizedStorage(DataValidator* validator = nullptr);
    
    /**
     * 析构函数
     */
    ~OptimizedStorage();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化存储系统
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理存储系统
     */
    void cleanup();
    
    /**
     * 存储系统主循环 - 缓存维护
     */
    void loop();
    
    // ================================
    // 信号数据操作 - 匹配前端信号管理
    // ================================
    
    /**
     * 获取信号数据 - L1+L2缓存策略
     * @param signalId 信号ID
     * @return 信号数据
     */
    SignalData getSignal(const String& signalId);
    
    /**
     * 保存信号数据 - 写入L1缓存和L2存储
     * @param signal 信号数据
     * @return 是否保存成功
     */
    bool saveSignal(const SignalData& signal);
    
    /**
     * 删除信号数据 - 从L1缓存和L2存储删除
     * @param signalId 信号ID
     * @return 是否删除成功
     */
    bool deleteSignal(const String& signalId);
    
    /**
     * 检查信号是否存在
     * @param signalId 信号ID
     * @return 是否存在
     */
    bool signalExists(const String& signalId);
    
    /**
     * 获取所有信号ID列表
     * @return 信号ID列表
     */
    std::vector<String> getAllSignalIds();
    
    /**
     * 获取所有信号数据 - 批量加载优化
     * @return 信号数据列表
     */
    std::vector<SignalData> getAllSignals();
    
    // ================================
    // 批量操作 - 匹配前端批量处理
    // ================================
    
    /**
     * 批量保存信号 - 批处理优化
     * @param signals 信号数据列表
     * @return 保存成功的数量
     */
    uint32_t saveSignalsBatch(const std::vector<SignalData>& signals);
    
    /**
     * 批量删除信号
     * @param signalIds 信号ID列表
     * @return 删除成功的数量
     */
    uint32_t deleteSignalsBatch(const std::vector<String>& signalIds);
    
    /**
     * 批量获取信号
     * @param signalIds 信号ID列表
     * @return 信号数据列表
     */
    std::vector<SignalData> getSignalsBatch(const std::vector<String>& signalIds);
    
    /**
     * 清空所有信号
     * @return 清空的信号数量
     */
    uint32_t clearAllSignals();
    
    // ================================
    // 任务数据操作
    // ================================
    
    /**
     * 保存任务数据
     * @param task 任务数据
     * @return 是否保存成功
     */
    bool saveTask(const TaskData& task);
    
    /**
     * 获取任务数据
     * @param taskId 任务ID
     * @return 任务数据
     */
    TaskData getTask(const String& taskId);
    
    /**
     * 删除任务数据
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    bool deleteTask(const String& taskId);
    
    /**
     * 获取所有任务数据
     * @return 任务数据列表
     */
    std::vector<TaskData> getAllTasks();
    
    // ================================
    // 缓存管理 - L1缓存优化
    // ================================
    
    /**
     * 预加载热点信号到L1缓存
     * @param signalIds 信号ID列表
     * @return 预加载成功的数量
     */
    uint32_t preloadHotSignals(const std::vector<String>& signalIds);
    
    /**
     * 刷新L1缓存 - 将脏数据写入L2存储
     * @return 刷新的缓存项数量
     */
    uint32_t flushL1Cache();
    
    /**
     * 清理L1缓存 - 移除过期和冷数据
     * @return 清理的缓存项数量
     */
    uint32_t cleanupL1Cache();
    
    /**
     * 获取L1缓存命中率
     * @return 缓存命中率（0.0-1.0）
     */
    float getL1HitRate() const { return statistics.getL1HitRate(); }
    
    /**
     * 获取L1缓存使用情况
     * @return 缓存使用情况JSON对象
     */
    JsonDocument getL1CacheStatus() const;
    
    // ================================
    // 存储优化和维护
    // ================================
    
    /**
     * 压缩存储数据 - 减少存储空间占用
     * @return 压缩节省的空间（字节）
     */
    uint32_t compressStorage();
    
    /**
     * 碎片整理 - 优化存储布局
     * @return 是否整理成功
     */
    bool defragmentStorage();
    
    /**
     * 验证存储完整性
     * @return 验证结果
     */
    bool validateStorageIntegrity();
    
    /**
     * 修复存储错误
     * @return 修复的错误数量
     */
    uint32_t repairStorageErrors();
    
    // ================================
    // 统计和监控
    // ================================
    
    /**
     * 获取存储统计信息
     * @return 存储统计信息
     */
    StorageStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置存储统计
     */
    void resetStatistics();
    
    /**
     * 获取存储使用情况
     * @return 存储使用情况JSON对象
     */
    JsonDocument getStorageUsage() const;
    
    /**
     * 获取性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const;
    
    // ================================
    // 配置管理
    // ================================
    
    /**
     * 设置数据验证器
     * @param validator 数据验证器指针
     */
    void setDataValidator(DataValidator* validator) { dataValidator = validator; }
    
    /**
     * 启用/禁用压缩
     * @param enabled 是否启用
     */
    void setCompressionEnabled(bool enabled) { compressionEnabled = enabled; }
    
    /**
     * 设置缓存超时时间
     * @param timeout 超时时间（毫秒）
     */
    void setCacheTimeout(uint32_t timeout) { cacheTimeout = timeout; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // L1缓存 - SRAM高速缓存
    L1CacheItem l1Cache[L1CacheConfig::MAX_HOT_SIGNALS];    // 热点信号缓存
    std::unordered_map<String, uint32_t> l1CacheIndex;     // 缓存索引映射
    uint32_t l1CacheSize;                                   // 当前缓存大小
    
    // L2存储 - Preferences + SPIFFS
    Preferences preferences;                                // Preferences存储
    
    // 数据验证
    DataValidator* dataValidator;                           // 数据验证器
    
    // 统计信息
    StorageStatistics statistics;                           // 存储统计
    
    // 配置选项
    bool compressionEnabled;                                // 是否启用压缩
    uint32_t cacheTimeout;                                  // 缓存超时时间
    
    // 系统状态
    bool initialized;                                       // 是否已初始化
    uint32_t lastMaintenanceTime;                          // 上次维护时间
    
    // ================================
    // 私有方法 - L1缓存管理
    // ================================
    
    /**
     * 查找L1缓存项
     * @param signalId 信号ID
     * @return 缓存项索引（-1表示未找到）
     */
    int findL1CacheItem(const String& signalId);
    
    /**
     * 添加到L1缓存
     * @param signal 信号数据
     * @return 缓存项索引
     */
    int addToL1Cache(const SignalData& signal);
    
    /**
     * 从L1缓存移除
     * @param signalId 信号ID
     * @return 是否移除成功
     */
    bool removeFromL1Cache(const String& signalId);
    
    /**
     * 选择L1缓存驱逐项 - LRU策略
     * @return 驱逐项索引
     */
    int selectL1EvictionItem();
    
    /**
     * 更新L1缓存访问信息
     * @param index 缓存项索引
     */
    void updateL1CacheAccess(int index);
    
    // ================================
    // 私有方法 - L2存储管理
    // ================================
    
    /**
     * 从L2存储加载信号
     * @param signalId 信号ID
     * @return 信号数据
     */
    SignalData loadFromL2Storage(const String& signalId);
    
    /**
     * 保存到L2存储
     * @param signal 信号数据
     * @return 是否保存成功
     */
    bool saveToL2Storage(const SignalData& signal);
    
    /**
     * 从L2存储删除
     * @param signalId 信号ID
     * @return 是否删除成功
     */
    bool deleteFromL2Storage(const String& signalId);
    
    /**
     * 获取L2存储键名
     * @param signalId 信号ID
     * @return 存储键名
     */
    String getL2StorageKey(const String& signalId);
    
    // ================================
    // 私有方法 - 性能优化
    // ================================
    
    /**
     * 更新统计信息
     * @param operation 操作类型
     * @param success 是否成功
     * @param accessTime 访问时间（微秒）
     */
    void updateStatistics(const String& operation, bool success, uint32_t accessTime);
    
    /**
     * 执行缓存维护
     */
    void performCacheMaintenance();
    
    /**
     * 分析访问模式
     */
    void analyzeAccessPatterns();
    
    /**
     * 优化缓存策略
     */
    void optimizeCacheStrategy();
    
    /**
     * 计算存储使用情况
     */
    void calculateStorageUsage();
    
    /**
     * 记录性能指标
     * @param startTime 开始时间
     * @param operation 操作类型
     */
    void recordPerformanceMetric(uint32_t startTime, const String& operation);
};

#endif // OPTIMIZED_STORAGE_H
