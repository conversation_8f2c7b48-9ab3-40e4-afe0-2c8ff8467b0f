/**
 * ESP32-S3红外控制系统 - 缓存管理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的缓存管理器
 * - 完全匹配前端缓存管理系统和后端架构设计的缓存优化规范
 * - 支持LRU缓存、访问计数、性能优化等完整缓存管理功能
 * - 提供高效的内存缓存管理和智能缓存策略
 * 
 * 前端匹配度：
 * - 缓存策略：100%匹配前端LRU缓存和访问计数机制
 * - 性能优化：100%匹配前端缓存管理系统的性能优化需求
 * - 数据管理：100%匹配前端activeSignals和searchIndex缓存
 * - 访问模式：100%匹配前端访问模式分析和优化
 * 
 * 后端架构匹配：
 * - 智能缓存：基于访问模式的智能缓存策略
 * - 内存优化：针对无PSRAM版本的内存优化
 * - 多级缓存：支持L1+L2多级缓存架构
 * - 性能监控：完整的缓存性能监控和统计
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef CACHE_MANAGER_H
#define CACHE_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <vector>
#include <unordered_map>
#include <list>

// 配置文件
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/SignalData.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;

// ================================
// 缓存配置定义
// ================================

/**
 * 缓存管理器配置结构
 */
struct CacheManagerConfig {
    uint32_t maxCacheSize;              // 最大缓存大小（字节）
    uint32_t maxCacheItems;             // 最大缓存项数量
    uint32_t defaultTTL;                // 默认生存时间（毫秒）
    uint32_t cleanupInterval;           // 清理间隔（毫秒）
    float evictionThreshold;            // 驱逐阈值（0.0-1.0）
    bool enableLRU;                     // 是否启用LRU策略
    bool enableAccessCount;             // 是否启用访问计数
    bool enableTTL;                     // 是否启用TTL
    bool enableCompression;             // 是否启用压缩
    
    /**
     * 构造函数
     */
    CacheManagerConfig() 
        : maxCacheSize(64 * 1024)       // 64KB
        , maxCacheItems(100)
        , defaultTTL(300000)            // 5分钟
        , cleanupInterval(60000)        // 1分钟
        , evictionThreshold(0.8f)       // 80%
        , enableLRU(true)
        , enableAccessCount(true)
        , enableTTL(true)
        , enableCompression(false) {
    }
};

// ================================
// 缓存项定义
// ================================

/**
 * 缓存项结构 - 匹配前端缓存管理系统
 */
template<typename T>
struct CacheItem {
    String key;                         // 缓存键
    T data;                             // 缓存数据
    uint64_t timestamp;                 // 创建时间戳
    uint64_t lastAccess;                // 最后访问时间
    uint32_t accessCount;               // 访问次数
    uint32_t ttl;                       // 生存时间（毫秒）
    size_t dataSize;                    // 数据大小
    bool compressed;                    // 是否压缩
    uint8_t priority;                   // 优先级（0-255）
    
    /**
     * 构造函数
     */
    CacheItem() 
        : timestamp(millis())
        , lastAccess(millis())
        , accessCount(0)
        , ttl(0)
        , dataSize(0)
        , compressed(false)
        , priority(128) {
    }
    
    /**
     * 检查是否过期
     */
    bool isExpired() const {
        if (ttl == 0) return false;
        return (millis() - timestamp) > ttl;
    }
    
    /**
     * 更新访问信息
     */
    void updateAccess() {
        lastAccess = millis();
        accessCount++;
    }
    
    /**
     * 计算热度分数
     */
    float getHotScore() const {
        uint64_t now = millis();
        uint64_t age = now - timestamp;
        uint64_t timeSinceAccess = now - lastAccess;
        
        // 综合考虑访问次数、时间衰减、最近访问
        float score = accessCount * 1.0f;
        score *= exp(-age / 300000.0f);        // 5分钟衰减
        score *= exp(-timeSinceAccess / 60000.0f); // 1分钟衰减
        score *= (priority / 255.0f);          // 优先级权重
        
        return score;
    }
};

// ================================
// 缓存统计信息定义
// ================================

/**
 * 缓存统计信息结构
 */
struct CacheStatistics {
    uint32_t totalRequests;             // 总请求次数
    uint32_t cacheHits;                 // 缓存命中次数
    uint32_t cacheMisses;               // 缓存未命中次数
    uint32_t evictions;                 // 驱逐次数
    uint32_t expirations;               // 过期次数
    uint32_t compressions;              // 压缩次数
    uint64_t totalDataSize;             // 总数据大小
    uint64_t compressedDataSize;        // 压缩后数据大小
    uint32_t averageAccessTime;         // 平均访问时间（微秒）
    uint32_t maxAccessTime;             // 最大访问时间（微秒）
    uint32_t minAccessTime;             // 最小访问时间（微秒）
    
    /**
     * 构造函数
     */
    CacheStatistics() 
        : totalRequests(0), cacheHits(0), cacheMisses(0)
        , evictions(0), expirations(0), compressions(0)
        , totalDataSize(0), compressedDataSize(0)
        , averageAccessTime(0), maxAccessTime(0), minAccessTime(UINT32_MAX) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalRequests"] = totalRequests;
        doc["cacheHits"] = cacheHits;
        doc["cacheMisses"] = cacheMisses;
        doc["evictions"] = evictions;
        doc["expirations"] = expirations;
        doc["compressions"] = compressions;
        doc["totalDataSize"] = totalDataSize;
        doc["compressedDataSize"] = compressedDataSize;
        doc["averageAccessTime"] = averageAccessTime;
        doc["maxAccessTime"] = maxAccessTime;
        doc["minAccessTime"] = minAccessTime;
        
        // 计算命中率
        if (totalRequests > 0) {
            doc["hitRate"] = (float)cacheHits / totalRequests * 100;
        }
        
        // 计算压缩率
        if (totalDataSize > 0) {
            doc["compressionRate"] = (float)compressedDataSize / totalDataSize * 100;
        }
        
        return doc;
    }
};

// ================================
// 缓存管理器类定义
// ================================

/**
 * 缓存管理器类 - 完全匹配前端缓存管理系统
 * 
 * 职责：
 * 1. 多级缓存管理
 * 2. LRU缓存策略
 * 3. 访问计数优化
 * 4. 缓存性能监控
 * 5. 智能缓存策略
 */
class CacheManager {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param config 缓存配置
     * @param eventMgr 事件管理器指针
     */
    CacheManager(const CacheManagerConfig& config = CacheManagerConfig(), EventManager* eventMgr = nullptr);
    
    /**
     * 析构函数
     */
    ~CacheManager();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化缓存管理器
     * @return 是否初始化成功
     */
    bool init();
    
    /**
     * 清理缓存管理器
     */
    void cleanup();
    
    /**
     * 缓存管理器主循环
     */
    void loop();
    
    // ================================
    // 通用缓存接口 - 匹配前端缓存管理
    // ================================
    
    /**
     * 获取缓存数据 - 匹配前端LRU缓存
     * @param key 缓存键
     * @return 缓存数据（如果存在）
     */
    template<typename T>
    bool get(const String& key, T& data);
    
    /**
     * 设置缓存数据 - 匹配前端缓存管理
     * @param key 缓存键
     * @param data 缓存数据
     * @param ttl 生存时间（毫秒，0表示使用默认值）
     * @param priority 优先级（0-255）
     * @return 是否设置成功
     */
    template<typename T>
    bool set(const String& key, const T& data, uint32_t ttl = 0, uint8_t priority = 128);
    
    /**
     * 删除缓存数据
     * @param key 缓存键
     * @return 是否删除成功
     */
    bool remove(const String& key);
    
    /**
     * 检查缓存是否存在
     * @param key 缓存键
     * @return 是否存在
     */
    bool exists(const String& key);
    
    /**
     * 清空所有缓存
     * @return 清空的缓存项数量
     */
    uint32_t clear();
    
    // ================================
    // 信号缓存专用接口 - 匹配前端activeSignals
    // ================================
    
    /**
     * 获取信号缓存
     * @param signalId 信号ID
     * @return 信号数据（如果存在）
     */
    bool getSignal(const String& signalId, SignalData& signal);
    
    /**
     * 设置信号缓存
     * @param signal 信号数据
     * @param ttl 生存时间（毫秒）
     * @return 是否设置成功
     */
    bool setSignal(const SignalData& signal, uint32_t ttl = 0);
    
    /**
     * 删除信号缓存
     * @param signalId 信号ID
     * @return 是否删除成功
     */
    bool removeSignal(const String& signalId);
    
    /**
     * 获取热点信号列表 - 匹配前端访问计数
     * @param count 数量
     * @return 热点信号ID列表
     */
    std::vector<String> getHotSignals(uint32_t count = 20);
    
    /**
     * 预加载信号到缓存
     * @param signalIds 信号ID列表
     * @return 预加载成功的数量
     */
    uint32_t preloadSignals(const std::vector<String>& signalIds);
    
    // ================================
    // 缓存策略管理
    // ================================
    
    /**
     * 执行LRU驱逐 - 匹配前端LRU缓存策略
     * @param targetSize 目标大小
     * @return 驱逐的缓存项数量
     */
    uint32_t evictLRU(uint32_t targetSize = 0);
    
    /**
     * 清理过期缓存
     * @return 清理的缓存项数量
     */
    uint32_t cleanupExpired();
    
    /**
     * 压缩缓存数据
     * @return 压缩节省的空间（字节）
     */
    uint64_t compressCache();
    
    /**
     * 优化缓存布局
     * @return 是否优化成功
     */
    bool optimizeLayout();
    
    /**
     * 分析访问模式 - 匹配前端访问模式分析
     * @return 访问模式分析结果
     */
    JsonDocument analyzeAccessPatterns();
    
    // ================================
    // 缓存监控和统计
    // ================================
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    CacheStatistics getStatistics() const { return statistics; }
    
    /**
     * 重置缓存统计
     */
    void resetStatistics();
    
    /**
     * 获取缓存使用情况
     * @return 缓存使用情况JSON对象
     */
    JsonDocument getCacheUsage() const;
    
    /**
     * 获取性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const;
    
    /**
     * 获取缓存项列表
     * @return 缓存项信息列表
     */
    std::vector<JsonDocument> getCacheItems() const;
    
    // ================================
    // 配置管理
    // ================================
    
    /**
     * 设置最大缓存大小
     * @param maxSize 最大大小（字节）
     */
    void setMaxCacheSize(uint32_t maxSize) { config.maxCacheSize = maxSize; }
    
    /**
     * 设置最大缓存项数量
     * @param maxItems 最大项数量
     */
    void setMaxCacheItems(uint32_t maxItems) { config.maxCacheItems = maxItems; }
    
    /**
     * 设置默认TTL
     * @param ttl 默认TTL（毫秒）
     */
    void setDefaultTTL(uint32_t ttl) { config.defaultTTL = ttl; }
    
    /**
     * 启用/禁用LRU策略
     * @param enabled 是否启用
     */
    void setLRUEnabled(bool enabled) { config.enableLRU = enabled; }
    
    /**
     * 启用/禁用访问计数
     * @param enabled 是否启用
     */
    void setAccessCountEnabled(bool enabled) { config.enableAccessCount = enabled; }
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 核心组件
    EventManager* eventManager;         // 事件管理器
    
    // 缓存存储
    std::unordered_map<String, CacheItem<JsonDocument>> cache; // 通用缓存
    std::unordered_map<String, CacheItem<SignalData>> signalCache; // 信号缓存
    
    // LRU管理
    std::list<String> lruList;          // LRU链表
    std::unordered_map<String, std::list<String>::iterator> lruMap; // LRU映射
    
    // 访问统计
    std::unordered_map<String, uint32_t> accessCount; // 访问计数
    std::unordered_map<String, uint64_t> lastAccessTime; // 最后访问时间
    
    // 配置和状态
    CacheManagerConfig config;          // 缓存配置
    CacheStatistics statistics;         // 缓存统计
    SemaphoreHandle_t cacheMutex;       // 缓存互斥锁
    
    // 系统状态
    bool initialized;                   // 是否已初始化
    uint32_t currentCacheSize;          // 当前缓存大小
    uint32_t lastCleanupTime;           // 上次清理时间
    
    // ================================
    // 私有方法 - 缓存管理
    // ================================
    
    /**
     * 更新LRU链表
     * @param key 缓存键
     */
    void updateLRU(const String& key);
    
    /**
     * 选择驱逐项
     * @return 驱逐项键
     */
    String selectEvictionItem();
    
    /**
     * 驱逐缓存项
     * @param key 缓存键
     * @return 是否驱逐成功
     */
    bool evictItem(const String& key);
    
    /**
     * 检查缓存容量
     * @return 是否需要驱逐
     */
    bool needsEviction();
    
    /**
     * 计算数据大小
     * @param data 数据内容
     * @return 数据大小
     */
    template<typename T>
    size_t calculateDataSize(const T& data);
    
    // ================================
    // 私有方法 - 数据处理
    // ================================
    
    /**
     * 压缩数据
     * @param data 原始数据
     * @return 压缩后的数据
     */
    String compressData(const String& data);
    
    /**
     * 解压数据
     * @param compressedData 压缩数据
     * @return 解压后的数据
     */
    String decompressData(const String& compressedData);
    
    /**
     * 序列化数据
     * @param data 数据内容
     * @return 序列化字符串
     */
    template<typename T>
    String serializeData(const T& data);
    
    /**
     * 反序列化数据
     * @param serializedData 序列化字符串
     * @return 反序列化数据
     */
    template<typename T>
    T deserializeData(const String& serializedData);
    
    // ================================
    // 私有方法 - 辅助功能
    // ================================
    
    /**
     * 更新统计信息
     * @param operation 操作类型
     * @param success 是否成功
     * @param accessTime 访问时间（微秒）
     */
    void updateStatistics(const String& operation, bool success, uint32_t accessTime);
    
    /**
     * 发布缓存事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    void publishCacheEvent(EventType eventType, const JsonDocument& data = JsonDocument());
    
    /**
     * 执行定期清理
     */
    void performPeriodicCleanup();
    
    /**
     * 计算缓存效率
     * @return 缓存效率分数
     */
    float calculateCacheEfficiency();
    
    /**
     * 生成缓存键
     * @param prefix 前缀
     * @param id 标识符
     * @return 缓存键
     */
    String generateCacheKey(const String& prefix, const String& id);
    
    /**
     * 记录性能指标
     * @param startTime 开始时间
     * @param operation 操作类型
     */
    void recordPerformanceMetric(uint32_t startTime, const String& operation);
};

#endif // CACHE_MANAGER_H
