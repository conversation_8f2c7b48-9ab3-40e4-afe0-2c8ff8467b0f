/**
 * @file ErrorHandler.cpp
 * @brief 错误处理器实现 - 匹配头文件定义
 *
 * 功能说明：
 * - 单例模式错误处理器
 * - 错误记录和统计
 * - 错误恢复策略
 * - 事件发布
 *
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "ErrorHandler.h"
#include "EventManager.h"
#include "../utils/Logger.h"

// 静态成员初始化
ErrorHandler* ErrorHandler::instance = nullptr;
SemaphoreHandle_t ErrorHandler::instanceMutex = nullptr;

ErrorHandler::ErrorHandler()
    : eventManager(nullptr)
    , errorMutex(nullptr)
    , initialized(false)
    , maxErrorRecords(1000) {

    // 初始化统计信息
    statistics = ErrorStatistics();

    // 初始化恢复策略数组
    memset(recoveryStrategies, 0, sizeof(recoveryStrategies));

    LOG_INFO("ErrorHandler", "错误处理器构造完成");
}

ErrorHandler::~ErrorHandler() {
    cleanup();
    LOG_INFO("ErrorHandler", "错误处理器析构完成");
}

ErrorHandler* ErrorHandler::getInstance() {
    if (instance == nullptr) {
        if (instanceMutex == nullptr) {
            instanceMutex = xSemaphoreCreateMutex();
        }

        if (xSemaphoreTake(instanceMutex, portMAX_DELAY) == pdTRUE) {
            if (instance == nullptr) {
                instance = new ErrorHandler();
            }
            xSemaphoreGive(instanceMutex);
        }
    }
    return instance;
}

void ErrorHandler::destroyInstance() {
    if (instanceMutex != nullptr && xSemaphoreTake(instanceMutex, portMAX_DELAY) == pdTRUE) {
        if (instance != nullptr) {
            delete instance;
            instance = nullptr;
        }
        xSemaphoreGive(instanceMutex);
        vSemaphoreDelete(instanceMutex);
        instanceMutex = nullptr;
    }
}

bool ErrorHandler::init(EventManager* eventMgr) {
    if (initialized) {
        LOG_WARNING("ErrorHandler", "错误处理器已经初始化");
        return true;
    }

    LOG_INFO("ErrorHandler", "开始初始化错误处理器...");

    // 设置事件管理器
    eventManager = eventMgr;

    // 创建错误互斥锁
    errorMutex = xSemaphoreCreateMutex();
    if (errorMutex == nullptr) {
        LOG_ERROR("ErrorHandler", "创建错误互斥锁失败");
        return false;
    }

    // 初始化错误记录存储
    errorRecords.reserve(maxErrorRecords);

    initialized = true;
    LOG_INFO("ErrorHandler", "错误处理器初始化完成");
    return true;
}

void ErrorHandler::cleanup() {
    if (!initialized) {
        return;
    }

    LOG_INFO("ErrorHandler", "开始清理错误处理器...");

    // 清理错误记录
    errorRecords.clear();

    // 清理错误队列
    while (!errorQueue.empty()) {
        errorQueue.pop();
    }

    // 删除互斥锁
    if (errorMutex != nullptr) {
        vSemaphoreDelete(errorMutex);
        errorMutex = nullptr;
    }

    initialized = false;
    LOG_INFO("ErrorHandler", "错误处理器清理完成");
}

String ErrorHandler::logError(ErrorType type, ErrorSeverity severity,
                             const String& component, const String& message,
                             const String& details, const String& context,
                             const JsonDocument& metadata) {

    // 创建错误记录
    ErrorRecord record;
    record.errorId = generateErrorId();
    record.type = type;
    record.severity = severity;
    record.component = component;
    record.message = message;
    record.details = details;
    record.context = context;
    record.timestamp = millis();
    record.recovered = false;

    // 添加到记录中
    if (errorMutex != nullptr && xSemaphoreTake(errorMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        errorRecords.push_back(record);

        // 限制记录数量
        if (errorRecords.size() > maxErrorRecords) {
            errorRecords.erase(errorRecords.begin());
        }

        xSemaphoreGive(errorMutex);
    }

    // 更新统计
    updateStatistics(record);

    // 发布事件
    publishErrorEvent(record);

    LOG_ERROR("ErrorHandler", "记录错误: %s - %s", component.c_str(), message.c_str());
    return record.errorId;
}

String ErrorHandler::logError(const String& component, const String& message, ErrorSeverity severity) {
    return logError(ErrorType::SYSTEM_ERROR, severity, component, message, "", "", JsonDocument());
}

String ErrorHandler::generateErrorId() {
    static uint32_t counter = 0;
    return "ERR_" + String(millis()) + "_" + String(++counter);
}

void ErrorHandler::updateStatistics(const ErrorRecord& record) {
    statistics.totalErrors++;

    // 按严重级别统计
    if (static_cast<int>(record.severity) < 5) {
        statistics.errorsBySeverity[static_cast<int>(record.severity)]++;
    }

    // 按类型统计
    if (static_cast<int>(record.type) < 10) {
        statistics.errorsByType[static_cast<int>(record.type)]++;
    }

    // 特殊计数
    switch (record.severity) {
        case ErrorSeverity::CRITICAL:
            statistics.criticalErrors++;
            break;
        case ErrorSeverity::FATAL:
            statistics.fatalErrors++;
            break;
        default:
            break;
    }

    // 恢复状态统计
    if (record.recovered) {
        statistics.recoveredErrors++;
    } else {
        statistics.unrecoveredErrors++;
    }

    statistics.lastErrorTime = record.timestamp;
}

void ErrorHandler::publishErrorEvent(const ErrorRecord& record) {
    if (eventManager == nullptr) {
        return;
    }

    JsonDocument eventData;
    eventData["errorId"] = record.errorId;
    eventData["type"] = static_cast<int>(record.type);
    eventData["severity"] = static_cast<int>(record.severity);
    eventData["component"] = record.component;
    eventData["message"] = record.message;
    eventData["timestamp"] = record.timestamp;

    eventManager->emitHighPriority(EventType::SYSTEM_ERROR, eventData, "ErrorHandler");
}