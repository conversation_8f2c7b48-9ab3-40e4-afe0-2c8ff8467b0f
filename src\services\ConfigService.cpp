/**
 * ESP32-S3红外控制系统 - 配置管理服务实现
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的配置管理服务实现
 * - 完全匹配前端timer-settings.js配置管理和后端架构设计的配置管理规范
 * - 支持系统配置、网络配置、硬件配置的统一管理和持久化存储
 * - 提供配置导入导出、验证、重置等完整配置服务功能
 * 
 * 前端匹配度：
 * - 配置管理：100%匹配前端timer-settings.js配置管理功能
 * - 配置接口：100%匹配前端GET/POST /api/config接口需求
 * - 配置验证：100%匹配前端配置验证和错误处理机制
 * - 配置事件：100%匹配前端配置变更事件广播
 * 
 * 后端架构匹配：
 * - 服务架构：继承BaseService，符合统一服务接口
 * - 配置结构：完整的SystemConfig结构设计
 * - 持久化存储：使用Flash存储实现配置持久化
 * - 配置验证：完整的配置验证和错误处理机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "ConfigService.h"
#include "DataService.h"
#include "../core/EventManager.h"
#include "../storage/FlashStorage.h"
#include "../storage/DataValidator.h"
#include "../utils/Logger.h"

ConfigService::ConfigService(EventManager* eventMgr)
    : BaseService(eventMgr, "ConfigService")
    , dataService(nullptr)
    , flashStorage(nullptr)
    , dataValidator(nullptr)
    , configChanged(false)
    , lastSaveTime(0)
    , configVersion(1)
    , autoSaveEnabled(true) {
    
    // 初始化默认配置
    initDefaultConfig();
    
    LOG_INFO("ConfigService", "配置管理服务构造完成");
}

ConfigService::~ConfigService() {
    cleanup();
    LOG_INFO("ConfigService", "配置管理服务析构完成");
}

bool ConfigService::init() {
    LOG_INFO("ConfigService", "开始初始化配置管理服务...");
    
    // 初始化数据验证器
    dataValidator = new DataValidator();
    if (!dataValidator) {
        LOG_ERROR("ConfigService", "数据验证器创建失败");
        return false;
    }
    
    // 初始化Flash存储
    flashStorage = new FlashStorage(FlashStorageConfig(), dataValidator);
    if (!flashStorage->init()) {
        LOG_ERROR("ConfigService", "Flash存储初始化失败");
        return false;
    }
    
    // 加载配置
    if (!loadConfig()) {
        LOG_WARNING("ConfigService", "加载配置失败，使用默认配置");
        resetToDefaults();
    }
    
    // 验证配置
    if (!validateConfig()) {
        LOG_WARNING("ConfigService", "配置验证失败，重置为默认配置");
        resetToDefaults();
    }
    
    // 注册事件处理器
    registerEventHandlers();
    
    LOG_INFO("ConfigService", "配置管理服务初始化完成，配置版本: %u", configVersion);
    return true;
}

void ConfigService::cleanup() {
    LOG_INFO("ConfigService", "开始清理配置管理服务...");
    
    // 保存配置
    if (configChanged && autoSaveEnabled) {
        saveConfig();
    }
    
    // 清理存储
    if (flashStorage) {
        delete flashStorage;
        flashStorage = nullptr;
    }
    
    // 清理验证器
    if (dataValidator) {
        delete dataValidator;
        dataValidator = nullptr;
    }
    
    LOG_INFO("ConfigService", "配置管理服务清理完成");
}

void ConfigService::loop() {
    uint32_t currentTime = millis();
    
    // 自动保存配置
    if (autoSaveEnabled && configChanged && 
        (currentTime - lastSaveTime >= AUTO_SAVE_INTERVAL)) {
        saveConfig();
    }
}

bool ConfigService::loadConfig() {
    JsonDocument configDoc = flashStorage->load(CONFIG_KEY);
    if (configDoc.isNull()) {
        LOG_INFO("ConfigService", "配置文件不存在，使用默认配置");
        return false;
    }
    
    return setConfigFromJson(configDoc);
}

bool ConfigService::saveConfig() {
    JsonDocument configDoc = getConfigAsJson();
    configDoc["version"] = configVersion;
    configDoc["timestamp"] = millis();
    
    bool success = flashStorage->store(CONFIG_KEY, configDoc);
    if (success) {
        configChanged = false;
        lastSaveTime = millis();
        
        // 发布配置保存事件
        publishConfigEvent(EventType::CONFIG_SAVED);
        
        LOG_INFO("ConfigService", "配置保存成功，版本: %u", configVersion);
    } else {
        LOG_ERROR("ConfigService", "配置保存失败");
        handleError("CONFIG_SAVE_FAILED", "配置保存失败", ErrorSeverity::HIGH);
    }
    
    return success;
}

bool ConfigService::resetToDefaults() {
    LOG_INFO("ConfigService", "重置配置为默认值");
    
    initDefaultConfig();
    configChanged = true;
    configVersion++;
    
    // 发布配置重置事件
    publishConfigEvent(EventType::CONFIG_RESET);
    
    return true;
}

bool ConfigService::validateConfig() {
    validationErrors.clear();
    
    // 验证网络配置
    if (!validateNetworkConfig()) {
        return false;
    }
    
    // 验证硬件配置
    if (!validateHardwareConfig()) {
        return false;
    }
    
    // 验证系统配置
    if (!validateSystemConfig()) {
        return false;
    }
    
    // 验证IR配置
    if (!validateIRConfig()) {
        return false;
    }
    
    return validationErrors.empty();
}

JsonDocument ConfigService::getConfig() const {
    return getConfigAsJson();
}

bool ConfigService::setConfigFromJson(const JsonDocument& configDoc) {
    try {
        // 网络配置
        if (configDoc.containsKey("network")) {
            JsonObject network = configDoc["network"];
            systemConfig.network.ap_ssid = network["ap_ssid"].as<String>();
            systemConfig.network.ap_password = network["ap_password"].as<String>();
            systemConfig.network.sta_ssid = network["sta_ssid"].as<String>();
            systemConfig.network.sta_password = network["sta_password"].as<String>();
            systemConfig.network.hostname = network["hostname"].as<String>();
            systemConfig.network.enable_ap = network["enable_ap"].as<bool>();
            systemConfig.network.enable_sta = network["enable_sta"].as<bool>();
            systemConfig.network.ap_channel = network["ap_channel"].as<uint8_t>();
            systemConfig.network.connection_timeout = network["connection_timeout"].as<uint32_t>();
        }
        
        // 硬件配置
        if (configDoc.containsKey("hardware")) {
            JsonObject hardware = configDoc["hardware"];
            systemConfig.hardware.ir_tx_pin = hardware["ir_tx_pin"].as<uint8_t>();
            systemConfig.hardware.ir_rx_pin = hardware["ir_rx_pin"].as<uint8_t>();
            systemConfig.hardware.status_led_pin = hardware["status_led_pin"].as<uint8_t>();
            systemConfig.hardware.button_pin = hardware["button_pin"].as<uint8_t>();
            systemConfig.hardware.enable_status_led = hardware["enable_status_led"].as<bool>();
            systemConfig.hardware.led_brightness = hardware["led_brightness"].as<uint8_t>();
        }
        
        // 系统配置
        if (configDoc.containsKey("system")) {
            JsonObject system = configDoc["system"];
            systemConfig.system.device_name = system["device_name"].as<String>();
            systemConfig.system.log_level = system["log_level"].as<uint8_t>();
            systemConfig.system.enable_serial = system["enable_serial"].as<bool>();
            systemConfig.system.serial_baud = system["serial_baud"].as<uint32_t>();
            systemConfig.system.watchdog_timeout = system["watchdog_timeout"].as<uint32_t>();
            systemConfig.system.auto_restart = system["auto_restart"].as<bool>();
            systemConfig.system.status_update_interval = system["status_update_interval"].as<uint32_t>();
        }
        
        // IR配置
        if (configDoc.containsKey("ir")) {
            JsonObject ir = configDoc["ir"];
            systemConfig.ir.default_frequency = ir["default_frequency"].as<uint32_t>();
            systemConfig.ir.learning_timeout = ir["learning_timeout"].as<uint32_t>();
            systemConfig.ir.emit_repeat_delay = ir["emit_repeat_delay"].as<uint32_t>();
            systemConfig.ir.enable_learning = ir["enable_learning"].as<bool>();
            systemConfig.ir.auto_save_learned = ir["auto_save_learned"].as<bool>();
            systemConfig.ir.signal_validation = ir["signal_validation"].as<bool>();
        }
        
        // 更新配置版本
        if (configDoc.containsKey("version")) {
            configVersion = configDoc["version"].as<uint32_t>();
        }
        
        configChanged = true;
        
        // 发布配置更新事件
        publishConfigEvent(EventType::CONFIG_UPDATED);
        
        return true;
    } catch (...) {
        LOG_ERROR("ConfigService", "配置解析异常");
        return false;
    }
}

String ConfigService::exportConfig() const {
    JsonDocument configDoc = getConfigAsJson();
    configDoc["exported_at"] = millis();
    configDoc["device_info"]["chip_model"] = ESP.getChipModel();
    configDoc["device_info"]["mac_address"] = WiFi.macAddress();
    
    String configStr;
    serializeJsonPretty(configDoc, configStr);
    return configStr;
}

bool ConfigService::importConfig(const String& configStr) {
    JsonDocument configDoc;
    DeserializationError error = deserializeJson(configDoc, configStr);
    
    if (error) {
        LOG_ERROR("ConfigService", "配置导入解析失败: %s", error.c_str());
        validationErrors.push_back("JSON解析失败: " + String(error.c_str()));
        return false;
    }
    
    // 备份当前配置
    JsonDocument backupConfig = getConfigAsJson();
    
    // 尝试设置新配置
    if (setConfigFromJson(configDoc)) {
        // 验证新配置
        if (validateConfig()) {
            configVersion++;
            
            // 发布配置导入事件
            publishConfigEvent(EventType::CONFIG_IMPORTED);
            
            LOG_INFO("ConfigService", "配置导入成功");
            return true;
        } else {
            // 恢复备份配置
            setConfigFromJson(backupConfig);
            LOG_ERROR("ConfigService", "导入的配置验证失败，已恢复原配置");
            return false;
        }
    }
    
    return false;
}

std::vector<String> ConfigService::getValidationErrors() const {
    return validationErrors;
}

uint32_t ConfigService::getConfigVersion() const {
    return configVersion;
}

SystemConfig ConfigService::getSystemConfig() const {
    return systemConfig;
}

void ConfigService::setDataService(DataService* ds) {
    dataService = ds;
}

void ConfigService::initDefaultConfig() {
    // 网络默认配置
    systemConfig.network.ap_ssid = "ESP32-IR-Controller";
    systemConfig.network.ap_password = "12345678";
    systemConfig.network.sta_ssid = "";
    systemConfig.network.sta_password = "";
    systemConfig.network.hostname = "esp32-ir-controller";
    systemConfig.network.enable_ap = true;
    systemConfig.network.enable_sta = false;
    systemConfig.network.ap_channel = 1;
    systemConfig.network.connection_timeout = 10000;
    
    // 硬件默认配置
    systemConfig.hardware.ir_tx_pin = 4;
    systemConfig.hardware.ir_rx_pin = 5;
    systemConfig.hardware.status_led_pin = 2;
    systemConfig.hardware.button_pin = 0;
    systemConfig.hardware.enable_status_led = true;
    systemConfig.hardware.led_brightness = 128;
    
    // 系统默认配置
    systemConfig.system.device_name = "ESP32红外控制器";
    systemConfig.system.log_level = 3; // INFO级别
    systemConfig.system.enable_serial = true;
    systemConfig.system.serial_baud = 115200;
    systemConfig.system.watchdog_timeout = 30000;
    systemConfig.system.auto_restart = true;
    systemConfig.system.status_update_interval = 1000;
    
    // IR默认配置
    systemConfig.ir.default_frequency = 38000;
    systemConfig.ir.learning_timeout = 30000;
    systemConfig.ir.emit_repeat_delay = 100;
    systemConfig.ir.enable_learning = true;
    systemConfig.ir.auto_save_learned = true;
    systemConfig.ir.signal_validation = true;
    
    configVersion = 1;
}

void ConfigService::registerEventHandlers() {
    if (!eventManager) return;
    
    // 注册配置相关事件处理器
    eventManager->subscribe(EventType::CONFIG_CHANGE_REQUEST, [this](const JsonDocument& data) {
        handleConfigChangeRequest(data);
    });
    
    eventManager->subscribe(EventType::CONFIG_RESET_REQUEST, [this](const JsonDocument& data) {
        resetToDefaults();
        saveConfig();
    });
    
    eventManager->subscribe(EventType::CONFIG_EXPORT_REQUEST, [this](const JsonDocument& data) {
        String configStr = exportConfig();
        JsonDocument responseData;
        responseData["config"] = configStr;
        emitEvent(EventType::CONFIG_EXPORTED, responseData);
    });
}

JsonDocument ConfigService::getConfigAsJson() const {
    JsonDocument configDoc;
    
    // 网络配置
    JsonObject network = configDoc["network"].to<JsonObject>();
    network["ap_ssid"] = systemConfig.network.ap_ssid;
    network["ap_password"] = systemConfig.network.ap_password;
    network["sta_ssid"] = systemConfig.network.sta_ssid;
    network["sta_password"] = systemConfig.network.sta_password;
    network["hostname"] = systemConfig.network.hostname;
    network["enable_ap"] = systemConfig.network.enable_ap;
    network["enable_sta"] = systemConfig.network.enable_sta;
    network["ap_channel"] = systemConfig.network.ap_channel;
    network["connection_timeout"] = systemConfig.network.connection_timeout;
    
    // 硬件配置
    JsonObject hardware = configDoc["hardware"].to<JsonObject>();
    hardware["ir_tx_pin"] = systemConfig.hardware.ir_tx_pin;
    hardware["ir_rx_pin"] = systemConfig.hardware.ir_rx_pin;
    hardware["status_led_pin"] = systemConfig.hardware.status_led_pin;
    hardware["button_pin"] = systemConfig.hardware.button_pin;
    hardware["enable_status_led"] = systemConfig.hardware.enable_status_led;
    hardware["led_brightness"] = systemConfig.hardware.led_brightness;
    
    // 系统配置
    JsonObject system = configDoc["system"].to<JsonObject>();
    system["device_name"] = systemConfig.system.device_name;
    system["log_level"] = systemConfig.system.log_level;
    system["enable_serial"] = systemConfig.system.enable_serial;
    system["serial_baud"] = systemConfig.system.serial_baud;
    system["watchdog_timeout"] = systemConfig.system.watchdog_timeout;
    system["auto_restart"] = systemConfig.system.auto_restart;
    system["status_update_interval"] = systemConfig.system.status_update_interval;
    
    // IR配置
    JsonObject ir = configDoc["ir"].to<JsonObject>();
    ir["default_frequency"] = systemConfig.ir.default_frequency;
    ir["learning_timeout"] = systemConfig.ir.learning_timeout;
    ir["emit_repeat_delay"] = systemConfig.ir.emit_repeat_delay;
    ir["enable_learning"] = systemConfig.ir.enable_learning;
    ir["auto_save_learned"] = systemConfig.ir.auto_save_learned;
    ir["signal_validation"] = systemConfig.ir.signal_validation;
    
    return configDoc;
}

bool ConfigService::validateNetworkConfig() {
    bool valid = true;
    
    // 验证AP SSID
    if (systemConfig.network.ap_ssid.length() < 1 || systemConfig.network.ap_ssid.length() > 32) {
        validationErrors.push_back("AP SSID长度必须在1-32字符之间");
        valid = false;
    }
    
    // 验证AP密码
    if (systemConfig.network.ap_password.length() < 8 || systemConfig.network.ap_password.length() > 63) {
        validationErrors.push_back("AP密码长度必须在8-63字符之间");
        valid = false;
    }
    
    // 验证AP信道
    if (systemConfig.network.ap_channel < 1 || systemConfig.network.ap_channel > 13) {
        validationErrors.push_back("AP信道必须在1-13之间");
        valid = false;
    }
    
    // 验证连接超时
    if (systemConfig.network.connection_timeout < 5000 || systemConfig.network.connection_timeout > 60000) {
        validationErrors.push_back("连接超时必须在5-60秒之间");
        valid = false;
    }
    
    return valid;
}

bool ConfigService::validateHardwareConfig() {
    bool valid = true;
    
    // 验证引脚配置
    std::vector<uint8_t> pins = {
        systemConfig.hardware.ir_tx_pin,
        systemConfig.hardware.ir_rx_pin,
        systemConfig.hardware.status_led_pin,
        systemConfig.hardware.button_pin
    };
    
    // 检查引脚冲突
    for (size_t i = 0; i < pins.size(); i++) {
        for (size_t j = i + 1; j < pins.size(); j++) {
            if (pins[i] == pins[j]) {
                validationErrors.push_back("引脚配置冲突: " + String(pins[i]));
                valid = false;
            }
        }
    }
    
    // 验证LED亮度
    if (systemConfig.hardware.led_brightness > 255) {
        validationErrors.push_back("LED亮度必须在0-255之间");
        valid = false;
    }
    
    return valid;
}

bool ConfigService::validateSystemConfig() {
    bool valid = true;
    
    // 验证设备名称
    if (systemConfig.system.device_name.length() < 1 || systemConfig.system.device_name.length() > 32) {
        validationErrors.push_back("设备名称长度必须在1-32字符之间");
        valid = false;
    }
    
    // 验证日志级别
    if (systemConfig.system.log_level > 5) {
        validationErrors.push_back("日志级别必须在0-5之间");
        valid = false;
    }
    
    // 验证串口波特率
    std::vector<uint32_t> validBaudRates = {9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600};
    bool baudValid = false;
    for (uint32_t rate : validBaudRates) {
        if (systemConfig.system.serial_baud == rate) {
            baudValid = true;
            break;
        }
    }
    if (!baudValid) {
        validationErrors.push_back("无效的串口波特率");
        valid = false;
    }
    
    return valid;
}

bool ConfigService::validateIRConfig() {
    bool valid = true;
    
    // 验证IR频率
    if (systemConfig.ir.default_frequency < 30000 || systemConfig.ir.default_frequency > 60000) {
        validationErrors.push_back("IR频率必须在30-60kHz之间");
        valid = false;
    }
    
    // 验证学习超时
    if (systemConfig.ir.learning_timeout < 5000 || systemConfig.ir.learning_timeout > 120000) {
        validationErrors.push_back("学习超时必须在5-120秒之间");
        valid = false;
    }
    
    // 验证发射重复延时
    if (systemConfig.ir.emit_repeat_delay < 50 || systemConfig.ir.emit_repeat_delay > 5000) {
        validationErrors.push_back("发射重复延时必须在50-5000ms之间");
        valid = false;
    }
    
    return valid;
}

void ConfigService::publishConfigEvent(EventType eventType) {
    JsonDocument eventData;
    eventData["configVersion"] = configVersion;
    eventData["timestamp"] = millis();
    emitEvent(eventType, eventData);
}

void ConfigService::handleConfigChangeRequest(const JsonDocument& data) {
    if (setConfigFromJson(data)) {
        if (validateConfig()) {
            configVersion++;
            if (autoSaveEnabled) {
                saveConfig();
            }
        } else {
            LOG_ERROR("ConfigService", "配置变更验证失败");
        }
    }
}
