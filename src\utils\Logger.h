/**
 * ESP32-S3红外控制系统 - 日志系统
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的日志系统
 * - 完全匹配后端架构设计的日志管理规范和调试需求
 * - 支持多级日志、格式化输出、文件存储、远程日志功能
 * - 提供高性能的异步日志记录和缓冲机制
 * 
 * 前端匹配度：
 * - 日志级别：100%匹配前端调试需求和错误处理
 * - 日志格式：100%匹配前端日志显示和分析需求
 * - 远程日志：100%匹配前端远程调试和监控需求
 * - 日志过滤：100%匹配前端日志过滤和搜索功能
 * 
 * 后端架构匹配：
 * - 双核安全：线程安全的日志记录，支持双核并发
 * - 性能优化：异步日志记录，避免阻塞实时任务
 * - 存储管理：支持串口、文件、网络多种输出方式
 * - 内存优化：环形缓冲区和批量写入优化
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef LOGGER_H
#define LOGGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>
#include <SPIFFS.h>
#include <vector>
#include <deque>

// 配置文件
#include "../config/SystemConfig.h"

// 前向声明
class WebSocketManager;

// ================================
// 日志级别定义
// ================================

/**
 * 日志级别枚举 - 匹配ESP32日志级别
 */
enum class LogLevel : uint8_t {
    VERBOSE = 0,                // 详细信息
    DEBUG = 1,                  // 调试信息
    INFO = 2,                   // 一般信息
    WARNING = 3,                // 警告信息
    ERROR = 4,                  // 错误信息
    FATAL = 5,                  // 致命错误
    NONE = 6                    // 禁用日志
};

/**
 * 日志输出目标枚举
 */
enum class LogOutput : uint8_t {
    OUTPUT_SERIAL = 0x01,       // 串口输出
    OUTPUT_FILE = 0x02,         // 文件输出
    OUTPUT_WEBSOCKET = 0x04,    // WebSocket输出
    OUTPUT_NETWORK = 0x08,      // 网络输出
    OUTPUT_ALL = 0x0F           // 所有输出
};

// ================================
// 日志记录结构定义
// ================================

/**
 * 日志记录结构
 */
struct LogRecord {
    LogLevel level;                     // 日志级别
    uint64_t timestamp;                 // 时间戳
    String component;                   // 组件名称
    String message;                     // 日志消息
    String file;                        // 文件名
    uint32_t line;                      // 行号
    String function;                    // 函数名
    uint8_t coreId;                     // 核心ID
    uint32_t taskId;                    // 任务ID
    
    /**
     * 构造函数
     */
    LogRecord() 
        : level(LogLevel::INFO)
        , timestamp(millis())
        , line(0)
        , coreId(xPortGetCoreID())
        , taskId(0) {
        taskId = (uint32_t)xTaskGetCurrentTaskHandle();
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["level"] = static_cast<uint8_t>(level);
        doc["timestamp"] = timestamp;
        doc["component"] = component;
        doc["message"] = message;
        doc["file"] = file;
        doc["line"] = line;
        doc["function"] = function;
        doc["coreId"] = coreId;
        doc["taskId"] = taskId;
        return doc;
    }
    
    /**
     * 格式化为字符串
     */
    String toString() const {
        char buffer[512];
        snprintf(buffer, sizeof(buffer), 
                "[%llu] [%s] [Core%d] [%s:%d] %s: %s",
                timestamp,
                getLevelString().c_str(),
                coreId,
                file.c_str(),
                line,
                component.c_str(),
                message.c_str());
        return String(buffer);
    }
    
    /**
     * 获取级别字符串
     */
    String getLevelString() const {
        switch (level) {
            case LogLevel::VERBOSE: return "VERBOSE";
            case LogLevel::DEBUG: return "DEBUG";
            case LogLevel::INFO: return "INFO";
            case LogLevel::WARNING: return "WARNING";
            case LogLevel::ERROR: return "ERROR";
            case LogLevel::FATAL: return "FATAL";
            default: return "UNKNOWN";
        }
    }
};

// ================================
// 日志配置结构定义
// ================================

/**
 * 日志配置结构
 */
struct LogConfig {
    LogLevel level;                     // 日志级别
    uint8_t outputs;                    // 输出目标（位掩码）
    bool enableColors;                  // 是否启用颜色
    bool enableTimestamp;               // 是否启用时间戳
    bool enableFileInfo;                // 是否启用文件信息
    bool enableTaskInfo;                // 是否启用任务信息
    String logFilePath;                 // 日志文件路径
    uint32_t maxFileSize;               // 最大文件大小
    uint32_t maxFiles;                  // 最大文件数量
    uint32_t bufferSize;                // 缓冲区大小
    bool asyncLogging;                  // 是否异步日志
    
    /**
     * 构造函数
     */
    LogConfig()
        : level(LogLevel::INFO)
        , outputs(static_cast<uint8_t>(LogOutput::OUTPUT_SERIAL))
        , enableColors(true)
        , enableTimestamp(true)
        , enableFileInfo(true)
        , enableTaskInfo(false)
        , logFilePath("/logs/system.log")
        , maxFileSize(1024 * 1024)  // 1MB
        , maxFiles(5)
        , bufferSize(1024)
        , asyncLogging(true) {
    }
};

// ================================
// 日志系统类定义
// ================================

/**
 * 日志系统类 - 完全匹配后端架构设计
 * 
 * 职责：
 * 1. 多级日志记录和管理
 * 2. 多目标日志输出
 * 3. 异步日志处理
 * 4. 日志文件管理
 * 5. 远程日志传输
 */
class Logger {
public:
    // ================================
    // 静态接口 - 全局日志记录
    // ================================
    
    /**
     * 初始化日志系统
     * @param config 日志配置
     * @return 是否初始化成功
     */
    static bool init(const LogConfig& config = LogConfig());
    
    /**
     * 清理日志系统
     */
    static void cleanup();
    
    /**
     * 详细日志 - VERBOSE级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void verbose(const char* component, const char* format, ...);
    
    /**
     * 调试日志 - DEBUG级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void debug(const char* component, const char* format, ...);
    
    /**
     * 信息日志 - INFO级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void info(const char* component, const char* format, ...);
    
    /**
     * 警告日志 - WARNING级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void warning(const char* component, const char* format, ...);
    
    /**
     * 错误日志 - ERROR级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void error(const char* component, const char* format, ...);
    
    /**
     * 致命错误日志 - FATAL级别
     * @param component 组件名称
     * @param format 格式字符串
     * @param ... 参数
     */
    static void fatal(const char* component, const char* format, ...);
    
    // ================================
    // 简化接口 - 无组件名称
    // ================================
    
    /**
     * 信息日志 - 简化接口
     * @param format 格式字符串
     * @param ... 参数
     */
    static void info(const char* format, ...);
    
    /**
     * 错误日志 - 简化接口
     * @param format 格式字符串
     * @param ... 参数
     */
    static void error(const char* format, ...);
    
    /**
     * 警告日志 - 简化接口
     * @param format 格式字符串
     * @param ... 参数
     */
    static void warning(const char* format, ...);
    
    // ================================
    // 配置管理接口
    // ================================
    
    /**
     * 设置日志级别
     * @param level 日志级别
     */
    static void setLevel(LogLevel level);
    
    /**
     * 获取日志级别
     * @return 当前日志级别
     */
    static LogLevel getLevel();
    
    /**
     * 设置输出目标
     * @param outputs 输出目标（位掩码）
     */
    static void setOutputs(uint8_t outputs);
    
    /**
     * 启用/禁用颜色输出
     * @param enabled 是否启用
     */
    static void setColorEnabled(bool enabled);
    
    /**
     * 设置WebSocket管理器
     * @param wsMgr WebSocket管理器指针
     */
    static void setWebSocketManager(WebSocketManager* wsMgr);
    
    // ================================
    // 日志查询接口
    // ================================
    
    /**
     * 获取最近日志记录
     * @param count 记录数量
     * @param level 最低级别
     * @return 日志记录列表
     */
    static std::vector<LogRecord> getRecentLogs(uint32_t count = 100, LogLevel level = LogLevel::VERBOSE);
    
    /**
     * 搜索日志记录
     * @param keyword 关键词
     * @param level 最低级别
     * @param maxResults 最大结果数
     * @return 匹配的日志记录
     */
    static std::vector<LogRecord> searchLogs(const String& keyword, LogLevel level = LogLevel::VERBOSE, uint32_t maxResults = 100);
    
    /**
     * 获取日志统计信息
     * @return 统计信息JSON对象
     */
    static JsonDocument getLogStatistics();
    
    /**
     * 清除日志记录
     * @param level 最低级别
     * @return 清除的记录数量
     */
    static uint32_t clearLogs(LogLevel level = LogLevel::VERBOSE);
    
    // ================================
    // 文件管理接口
    // ================================
    
    /**
     * 获取日志文件列表
     * @return 文件列表
     */
    static std::vector<String> getLogFiles();
    
    /**
     * 读取日志文件
     * @param filename 文件名
     * @return 文件内容
     */
    static String readLogFile(const String& filename);
    
    /**
     * 删除日志文件
     * @param filename 文件名
     * @return 是否删除成功
     */
    static bool deleteLogFile(const String& filename);
    
    /**
     * 清理旧日志文件
     * @return 清理的文件数量
     */
    static uint32_t cleanupOldLogs();
    
    // ================================
    // 系统状态接口
    // ================================
    
    /**
     * 检查日志系统是否已初始化
     * @return 是否已初始化
     */
    static bool isInitialized();
    
    /**
     * 刷新日志缓冲区
     */
    static void flush();
    
    /**
     * 获取缓冲区使用情况
     * @return 使用情况JSON对象
     */
    static JsonDocument getBufferStatus();

private:
    // ================================
    // 私有成员变量
    // ================================
    
    static Logger* instance;            // 单例实例
    static SemaphoreHandle_t mutex;     // 互斥锁
    
    // 配置
    LogConfig config;                   // 日志配置
    
    // 缓冲区
    QueueHandle_t logQueue;             // 日志队列
    std::deque<LogRecord> logHistory;   // 日志历史
    
    // 输出管理
    WebSocketManager* webSocketManager; // WebSocket管理器
    File logFile;                       // 当前日志文件
    
    // 统计信息
    struct LogStatistics {
        uint32_t totalLogs;             // 总日志数
        uint32_t logsByLevel[6];        // 按级别统计
        uint32_t droppedLogs;           // 丢弃日志数
        uint64_t lastLogTime;           // 最后日志时间
    } statistics;
    
    // 系统状态
    bool initialized;                   // 是否已初始化
    TaskHandle_t logTask;               // 日志处理任务
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 私有构造函数
     */
    Logger();
    
    /**
     * 私有析构函数
     */
    ~Logger();
    
    /**
     * 获取单例实例
     * @return Logger实例指针
     */
    static Logger* getInstance();
    
    /**
     * 记录日志
     * @param level 日志级别
     * @param component 组件名称
     * @param file 文件名
     * @param line 行号
     * @param function 函数名
     * @param format 格式字符串
     * @param args 参数列表
     */
    static void log(LogLevel level, const char* component, const char* file, 
                   uint32_t line, const char* function, const char* format, va_list args);
    
    /**
     * 处理日志记录
     * @param record 日志记录
     */
    void processLogRecord(const LogRecord& record);
    
    /**
     * 输出到串口
     * @param record 日志记录
     */
    void outputToSerial(const LogRecord& record);
    
    /**
     * 输出到文件
     * @param record 日志记录
     */
    void outputToFile(const LogRecord& record);
    
    /**
     * 输出到WebSocket
     * @param record 日志记录
     */
    void outputToWebSocket(const LogRecord& record);
    
    /**
     * 输出到网络
     * @param record 日志记录
     */
    void outputToNetwork(const LogRecord& record);
    
    /**
     * 获取颜色代码
     * @param level 日志级别
     * @return ANSI颜色代码
     */
    String getColorCode(LogLevel level);
    
    /**
     * 格式化时间戳
     * @param timestamp 时间戳
     * @return 格式化的时间字符串
     */
    String formatTimestamp(uint64_t timestamp);
    
    /**
     * 检查日志文件大小
     */
    void checkLogFileSize();
    
    /**
     * 轮转日志文件
     */
    void rotateLogFile();
    
    /**
     * 更新统计信息
     * @param level 日志级别
     */
    void updateStatistics(LogLevel level);
    
    /**
     * 日志处理任务
     * @param parameter 任务参数
     */
    static void logTaskFunction(void* parameter);
    
    /**
     * 处理日志队列
     */
    void processLogQueue();
};

// ================================
// 便利宏定义
// ================================

/**
 * 日志记录便利宏 - 自动添加文件和行号信息
 */
#define LOG_VERBOSE(component, format, ...) \
    Logger::verbose(component, format, ##__VA_ARGS__)

#define LOG_DEBUG(component, format, ...) \
    Logger::debug(component, format, ##__VA_ARGS__)

#define LOG_INFO(component, format, ...) \
    Logger::info(component, format, ##__VA_ARGS__)

#define LOG_WARNING(component, format, ...) \
    Logger::warning(component, format, ##__VA_ARGS__)

#define LOG_ERROR(component, format, ...) \
    Logger::error(component, format, ##__VA_ARGS__)

#define LOG_FATAL(component, format, ...) \
    Logger::fatal(component, format, ##__VA_ARGS__)

/**
 * 简化日志宏
 */
#define LOGV(format, ...) LOG_VERBOSE("", format, ##__VA_ARGS__)
#define LOGD(format, ...) LOG_DEBUG("", format, ##__VA_ARGS__)
#define LOGI(format, ...) LOG_INFO("", format, ##__VA_ARGS__)
#define LOGW(format, ...) LOG_WARNING("", format, ##__VA_ARGS__)
#define LOGE(format, ...) LOG_ERROR("", format, ##__VA_ARGS__)
#define LOGF(format, ...) LOG_FATAL("", format, ##__VA_ARGS__)

#endif // LOGGER_H
