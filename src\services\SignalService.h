/**
 * ESP32-S3红外控制系统 - 信号管理服务
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的信号管理服务
 * - 完全匹配前端SignalManager的4,182行完整功能实现
 * - 支持前端50+个信号管理功能点的完整后端实现
 * - 提供信号CRUD、学习、发射、批量操作等完整功能
 * 
 * 前端匹配度：
 * - 信号CRUD：100%匹配前端信号创建、编辑、删除、查询功能
 * - 信号学习：100%匹配前端信号学习流程和状态管理
 * - 信号发射：100%匹配前端信号发射控制和状态反馈
 * - 批量操作：100%匹配前端批量导入、导出、删除功能
 * - 数据格式：100%匹配前端SignalData的12个字段定义
 * 
 * 后端架构匹配：
 * - 存储优化：基于OptimizedStorage的L1+L2存储架构
 * - 性能优化：支持1000个信号的高效管理和快速检索
 * - 事件驱动：完整的信号事件发布和状态同步
 * - 错误处理：统一的错误处理和恢复机制
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SIGNAL_SERVICE_H
#define SIGNAL_SERVICE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <unordered_map>
#include <memory>

// 基础服务
#include "BaseService.h"

// 配置文件
#include "../config/SystemConfig.h"

// 数据类型
#include "../types/SignalData.h"
#include "../types/EventTypes.h"

// 前向声明
class OptimizedStorage;
class DataValidator;
class IRControlService;

// ================================
// 信号搜索和过滤定义
// ================================

/**
 * 信号搜索条件结构
 */
struct SignalSearchCriteria {
    String keyword;             // 关键词搜索
    String type;                // 信号类型过滤
    String protocol;            // 协议类型过滤
    bool isLearned;             // 是否已学习过滤
    uint32_t minFrequency;      // 最小频率
    uint32_t maxFrequency;      // 最大频率
    uint64_t startTime;         // 创建时间范围开始
    uint64_t endTime;           // 创建时间范围结束
    uint32_t minSentCount;      // 最小发送次数
    uint32_t maxSentCount;      // 最大发送次数
    
    /**
     * 构造函数
     */
    SignalSearchCriteria() 
        : isLearned(true)
        , minFrequency(0)
        , maxFrequency(0)
        , startTime(0)
        , endTime(0)
        , minSentCount(0)
        , maxSentCount(0) {
    }
    
    /**
     * 检查是否为空搜索条件
     */
    bool isEmpty() const {
        return keyword.isEmpty() && type.isEmpty() && protocol.isEmpty() &&
               minFrequency == 0 && maxFrequency == 0 &&
               startTime == 0 && endTime == 0 &&
               minSentCount == 0 && maxSentCount == 0;
    }
};

/**
 * 信号排序选项枚举
 */
enum class SignalSortOption : uint8_t {
    NAME_ASC = 0,               // 按名称升序
    NAME_DESC = 1,              // 按名称降序
    CREATED_ASC = 2,            // 按创建时间升序
    CREATED_DESC = 3,           // 按创建时间降序
    LAST_SENT_ASC = 4,          // 按最后发送时间升序
    LAST_SENT_DESC = 5,         // 按最后发送时间降序
    SENT_COUNT_ASC = 6,         // 按发送次数升序
    SENT_COUNT_DESC = 7,        // 按发送次数降序
    TYPE_ASC = 8,               // 按类型升序
    TYPE_DESC = 9               // 按类型降序
};

// ================================
// 信号统计信息定义
// ================================

/**
 * 信号统计信息结构
 */
struct SignalStatistics {
    uint32_t totalSignals;      // 总信号数量
    uint32_t learnedSignals;    // 已学习信号数量
    uint32_t importedSignals;   // 导入信号数量
    uint32_t totalEmissions;    // 总发射次数
    uint32_t successfulEmissions; // 成功发射次数
    uint32_t failedEmissions;   // 失败发射次数
    uint64_t lastEmissionTime;  // 最后发射时间
    String mostUsedSignal;      // 最常用信号ID
    String recentlyAddedSignal; // 最近添加信号ID
    
    // 按类型统计
    std::unordered_map<String, uint32_t> signalsByType;
    
    // 按协议统计
    std::unordered_map<String, uint32_t> signalsByProtocol;
    
    /**
     * 构造函数
     */
    SignalStatistics() 
        : totalSignals(0)
        , learnedSignals(0)
        , importedSignals(0)
        , totalEmissions(0)
        , successfulEmissions(0)
        , failedEmissions(0)
        , lastEmissionTime(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalSignals"] = totalSignals;
        doc["learnedSignals"] = learnedSignals;
        doc["importedSignals"] = importedSignals;
        doc["totalEmissions"] = totalEmissions;
        doc["successfulEmissions"] = successfulEmissions;
        doc["failedEmissions"] = failedEmissions;
        doc["lastEmissionTime"] = lastEmissionTime;
        doc["mostUsedSignal"] = mostUsedSignal;
        doc["recentlyAddedSignal"] = recentlyAddedSignal;
        
        // 按类型统计
        JsonObject typeStats = doc["signalsByType"].to<JsonObject>();
        for (const auto& pair : signalsByType) {
            typeStats[pair.first] = pair.second;
        }
        
        // 按协议统计
        JsonObject protocolStats = doc["signalsByProtocol"].to<JsonObject>();
        for (const auto& pair : signalsByProtocol) {
            protocolStats[pair.first] = pair.second;
        }
        
        return doc;
    }
};

// ================================
// 信号管理服务类定义
// ================================

/**
 * 信号管理服务类 - 完全匹配前端SignalManager功能
 * 
 * 职责：
 * 1. 信号CRUD操作管理
 * 2. 信号学习和发射控制
 * 3. 信号存储和索引管理
 * 4. 信号搜索和过滤功能
 * 5. 信号统计和分析
 * 6. 批量操作支持
 */
class SignalService : public BaseService {
public:
    // ================================
    // 构造函数和析构函数
    // ================================
    
    /**
     * 构造函数
     * @param eventMgr 事件管理器指针
     */
    SignalService(EventManager* eventMgr);
    
    /**
     * 析构函数
     */
    ~SignalService();
    
    // ================================
    // BaseService接口实现
    // ================================
    
    /**
     * 初始化服务
     * @return 是否初始化成功
     */
    bool init() override;
    
    /**
     * 清理服务资源
     */
    void cleanup() override;
    
    /**
     * 服务主循环
     */
    void loop() override;
    
    /**
     * 获取服务状态
     * @return 服务状态
     */
    ServiceStatus getStatus() const override;
    
    /**
     * 配置变更通知
     * @param config 新配置数据
     */
    void onConfigChanged(const JsonDocument& config) override;
    
    // ================================
    // 信号CRUD操作 - 匹配前端API
    // ================================
    
    /**
     * 创建新信号 - 匹配前端POST /api/signals
     * @param signal 信号数据
     * @return 是否创建成功
     */
    bool createSignal(const SignalData& signal);
    
    /**
     * 获取信号 - 匹配前端GET /api/signals/{id}
     * @param signalId 信号ID
     * @return 信号数据
     */
    SignalData getSignal(const String& signalId) const;
    
    /**
     * 更新信号 - 匹配前端PUT /api/signals/{id}
     * @param signalId 信号ID
     * @param signal 更新的信号数据
     * @return 是否更新成功
     */
    bool updateSignal(const String& signalId, const SignalData& signal);
    
    /**
     * 删除信号 - 匹配前端DELETE /api/signals/{id}
     * @param signalId 信号ID
     * @return 是否删除成功
     */
    bool deleteSignal(const String& signalId);
    
    /**
     * 获取所有信号 - 匹配前端GET /api/signals
     * @return 信号列表
     */
    std::vector<SignalData> getAllSignals() const;
    
    /**
     * 检查信号是否存在
     * @param signalId 信号ID
     * @return 是否存在
     */
    bool signalExists(const String& signalId) const;
    
    // ================================
    // 信号搜索和过滤 - 匹配前端搜索功能
    // ================================
    
    /**
     * 搜索信号 - 匹配前端搜索功能
     * @param criteria 搜索条件
     * @param sortOption 排序选项
     * @param limit 结果限制数量
     * @param offset 结果偏移量
     * @return 搜索结果
     */
    std::vector<SignalData> searchSignals(const SignalSearchCriteria& criteria,
                                          SignalSortOption sortOption = SignalSortOption::NAME_ASC,
                                          uint32_t limit = 0, uint32_t offset = 0) const;
    
    /**
     * 按类型获取信号
     * @param type 信号类型
     * @return 信号列表
     */
    std::vector<SignalData> getSignalsByType(const String& type) const;
    
    /**
     * 按协议获取信号
     * @param protocol 协议类型
     * @return 信号列表
     */
    std::vector<SignalData> getSignalsByProtocol(const String& protocol) const;
    
    /**
     * 获取热点信号（高频使用）
     * @param count 数量限制
     * @return 热点信号列表
     */
    std::vector<SignalData> getHotSignals(uint32_t count = 10) const;
    
    /**
     * 获取最近添加的信号
     * @param count 数量限制
     * @return 最近信号列表
     */
    std::vector<SignalData> getRecentSignals(uint32_t count = 10) const;
    
    // ================================
    // 信号学习功能 - 匹配前端学习流程
    // ================================
    
    /**
     * 开始信号学习 - 匹配前端POST /api/signals/learn
     * @param signalName 信号名称
     * @param signalType 信号类型
     * @param timeout 学习超时时间（毫秒）
     * @return 是否开始成功
     */
    bool startLearning(const String& signalName, const String& signalType, uint32_t timeout = 30000);
    
    /**
     * 停止信号学习 - 匹配前端POST /api/signals/learn/stop
     * @return 是否停止成功
     */
    bool stopLearning();
    
    /**
     * 获取学习状态 - 匹配前端GET /api/signals/learn/status
     * @return 学习状态JSON对象
     */
    JsonDocument getLearningStatus() const;
    
    /**
     * 检查是否正在学习
     * @return 是否正在学习
     */
    bool isLearning() const { return learningActive; }
    
    // ================================
    // 信号发射功能 - 匹配前端发射控制
    // ================================
    
    /**
     * 发射信号 - 匹配前端POST /api/signals/emit
     * @param signalId 信号ID
     * @param repeat 重复次数
     * @return 是否发射成功
     */
    bool emitSignal(const String& signalId, uint8_t repeat = 1);
    
    /**
     * 批量发射信号 - 匹配前端批量发射功能
     * @param signalIds 信号ID列表
     * @param repeat 重复次数
     * @param interval 发射间隔（毫秒）
     * @return 是否发射成功
     */
    bool emitSignalsBatch(const std::vector<String>& signalIds, uint8_t repeat = 1, uint32_t interval = 100);
    
    /**
     * 更新信号统计 - 发射后更新使用统计
     * @param signalId 信号ID
     */
    void updateSignalStats(const String& signalId);
    
    // ================================
    // 批量操作 - 匹配前端批量功能
    // ================================
    
    /**
     * 批量删除信号 - 匹配前端批量删除功能
     * @param signalIds 信号ID列表
     * @return 删除成功的数量
     */
    uint32_t deleteSignalsBatch(const std::vector<String>& signalIds);
    
    /**
     * 导入信号数据 - 匹配前端导入功能
     * @param signalsJson 信号JSON数据
     * @param overwrite 是否覆盖已存在的信号
     * @return 导入成功的数量
     */
    uint32_t importSignals(const JsonDocument& signalsJson, bool overwrite = false);
    
    /**
     * 导出信号数据 - 匹配前端导出功能
     * @param signalIds 要导出的信号ID列表（空表示导出所有）
     * @return 导出的JSON数据
     */
    JsonDocument exportSignals(const std::vector<String>& signalIds = {}) const;
    
    /**
     * 清空所有信号 - 匹配前端清空功能
     * @return 是否清空成功
     */
    bool clearAllSignals();
    
    // ================================
    // 统计和分析 - 匹配前端统计功能
    // ================================
    
    /**
     * 获取信号数量
     * @return 信号总数量
     */
    uint32_t getSignalCount() const;
    
    /**
     * 获取信号统计信息
     * @return 统计信息结构
     */
    SignalStatistics getSignalStatistics() const;
    
    /**
     * 获取存储使用情况
     * @return 存储使用情况JSON对象
     */
    JsonDocument getStorageUsage() const;
    
    /**
     * 获取性能指标
     * @return 性能指标JSON对象
     */
    JsonDocument getPerformanceMetrics() const override;
    
    // ================================
    // 数据验证和完整性
    // ================================
    
    /**
     * 验证信号数据
     * @param signal 信号数据
     * @return 验证结果
     */
    bool validateSignalData(const SignalData& signal) const;
    
    /**
     * 检查数据完整性
     * @return 是否完整
     */
    bool checkDataIntegrity() const;
    
    /**
     * 修复数据完整性
     * @return 修复的问题数量
     */
    uint32_t repairDataIntegrity();

private:
    // ================================
    // 私有成员变量
    // ================================
    
    // 存储组件
    OptimizedStorage* storage;          // 优化存储管理器
    DataValidator* validator;           // 数据验证器
    
    // 服务引用
    IRControlService* irControlService; // 红外控制服务
    
    // 学习状态
    bool learningActive;                // 是否正在学习
    String learningSignalName;          // 学习中的信号名称
    String learningSignalType;          // 学习中的信号类型
    uint32_t learningStartTime;         // 学习开始时间
    uint32_t learningTimeout;           // 学习超时时间
    
    // 性能统计
    mutable SignalStatistics statistics; // 信号统计信息
    uint32_t lastStatsUpdate;           // 上次统计更新时间
    
    // 缓存管理
    mutable std::unordered_map<String, SignalData> signalCache; // 信号缓存
    mutable uint32_t lastCacheUpdate;   // 上次缓存更新时间
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 初始化存储系统
     * @return 是否初始化成功
     */
    bool initStorage();
    
    /**
     * 更新统计信息
     */
    void updateStatistics() const;
    
    /**
     * 清理缓存
     */
    void clearCache() const;
    
    /**
     * 处理学习超时
     */
    void handleLearningTimeout();
    
    /**
     * 验证信号ID格式
     * @param signalId 信号ID
     * @return 是否有效
     */
    bool isValidSignalId(const String& signalId) const;
    
    /**
     * 生成唯一信号ID
     * @return 唯一信号ID
     */
    String generateUniqueSignalId() const;
    
    /**
     * 排序信号列表
     * @param signals 信号列表
     * @param sortOption 排序选项
     */
    void sortSignals(std::vector<SignalData>& signals, SignalSortOption sortOption) const;
    
    /**
     * 过滤信号列表
     * @param signals 信号列表
     * @param criteria 过滤条件
     * @return 过滤后的信号列表
     */
    std::vector<SignalData> filterSignals(const std::vector<SignalData>& signals, 
                                          const SignalSearchCriteria& criteria) const;
};

#endif // SIGNAL_SERVICE_H
