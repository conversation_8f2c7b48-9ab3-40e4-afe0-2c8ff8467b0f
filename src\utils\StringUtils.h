/**
 * ESP32-S3红外控制系统 - 字符串工具类
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的字符串工具类
 * - 完全匹配前端字符串处理需求和后端架构设计的文本处理规范
 * - 支持字符串格式化、验证、解析、转换等完整字符串处理功能
 * - 提供高效的字符串操作和内存优化的字符串处理算法
 * 
 * 前端匹配度：
 * - 格式化功能：100%匹配前端formatTime、formatFileSize等格式化需求
 * - 验证功能：100%匹配前端validateSignalFormat、validateType等验证需求
 * - 解析功能：100%匹配前端JSON.parse、数据标准化等解析需求
 * - ID生成：100%匹配前端generateId统一格式ID生成需求
 * 
 * 后端架构匹配：
 * - 内存优化：针对ESP32-S3内存限制的字符串处理优化
 * - 性能优化：高效的字符串操作算法和缓存机制
 * - 编码支持：完整的UTF-8、Base64、Hex等编码支持
 * - 安全处理：防止缓冲区溢出的安全字符串操作
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef STRING_UTILS_H
#define STRING_UTILS_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <regex>

// 配置文件
#include "../config/SystemConfig.h"

// ================================
// 字符串常量定义
// ================================

/**
 * 字符串常量
 */
namespace StringConstants {
    const char* const EMPTY_STRING = "";
    const char* const SPACE = " ";
    const char* const NEWLINE = "\n";
    const char* const CARRIAGE_RETURN = "\r";
    const char* const TAB = "\t";
    const char* const COMMA = ",";
    const char* const SEMICOLON = ";";
    const char* const COLON = ":";
    const char* const UNDERSCORE = "_";
    const char* const HYPHEN = "-";
    const char* const DOT = ".";
    const char* const SLASH = "/";
    const char* const BACKSLASH = "\\";
}

// ================================
// 编码类型定义
// ================================

/**
 * 编码类型枚举
 */
enum class EncodingType : uint8_t {
    UTF8 = 0,                   // UTF-8编码
    ASCII = 1,                  // ASCII编码
    BASE64 = 2,                 // Base64编码
    HEX = 3,                    // 十六进制编码
    URL = 4,                    // URL编码
    JSON = 5,                   // JSON编码
    HTML = 6                    // HTML编码
};

/**
 * 大小写转换类型
 */
enum class CaseType : uint8_t {
    LOWER = 0,                  // 小写
    UPPER = 1,                  // 大写
    TITLE = 2,                  // 标题格式
    CAMEL = 3,                  // 驼峰格式
    SNAKE = 4,                  // 蛇形格式
    KEBAB = 5                   // 短横线格式
};

// ================================
// 字符串工具类定义
// ================================

/**
 * 字符串工具类 - 完全匹配前端字符串处理需求
 * 
 * 职责：
 * 1. 字符串格式化和模板处理
 * 2. 字符串验证和解析
 * 3. 字符串编码和解码
 * 4. 字符串搜索和替换
 * 5. 高效的字符串操作
 */
class StringUtils {
public:
    // ================================
    // 字符串基础操作 - 匹配前端字符串处理
    // ================================
    
    /**
     * 去除字符串两端空白字符 - 匹配前端trim()
     * @param str 输入字符串
     * @param chars 要去除的字符集（默认为空白字符）
     * @return 去除空白后的字符串
     */
    static String trim(const String& str, const String& chars = " \t\n\r");
    
    /**
     * 去除字符串左端空白字符
     * @param str 输入字符串
     * @param chars 要去除的字符集
     * @return 去除空白后的字符串
     */
    static String trimLeft(const String& str, const String& chars = " \t\n\r");
    
    /**
     * 去除字符串右端空白字符
     * @param str 输入字符串
     * @param chars 要去除的字符集
     * @return 去除空白后的字符串
     */
    static String trimRight(const String& str, const String& chars = " \t\n\r");
    
    /**
     * 分割字符串 - 匹配前端split()
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @param maxSplits 最大分割次数（0表示无限制）
     * @return 分割后的字符串数组
     */
    static std::vector<String> split(const String& str, const String& delimiter, uint32_t maxSplits = 0);
    
    /**
     * 连接字符串数组
     * @param strings 字符串数组
     * @param delimiter 连接符
     * @return 连接后的字符串
     */
    static String join(const std::vector<String>& strings, const String& delimiter);
    
    /**
     * 替换字符串
     * @param str 输入字符串
     * @param search 搜索字符串
     * @param replace 替换字符串
     * @param replaceAll 是否替换所有匹配项
     * @return 替换后的字符串
     */
    static String replace(const String& str, const String& search, const String& replace, bool replaceAll = true);
    
    // ================================
    // 字符串格式化 - 匹配前端格式化功能
    // ================================
    
    /**
     * 格式化字符串 - 匹配前端格式化需求
     * @param format 格式字符串
     * @param ... 参数
     * @return 格式化后的字符串
     */
    static String format(const char* format, ...);
    
    /**
     * 格式化文件大小 - 匹配前端formatFileSize()
     * @param bytes 字节数
     * @param precision 小数位数
     * @return 格式化的文件大小字符串
     */
    static String formatFileSize(uint64_t bytes, uint8_t precision = 2);
    
    /**
     * 格式化数字
     * @param number 数字
     * @param precision 小数位数
     * @param thousandsSeparator 千位分隔符
     * @return 格式化的数字字符串
     */
    static String formatNumber(double number, uint8_t precision = 2, const String& thousandsSeparator = ",");
    
    /**
     * 格式化百分比
     * @param value 数值
     * @param precision 小数位数
     * @return 格式化的百分比字符串
     */
    static String formatPercentage(double value, uint8_t precision = 1);
    
    /**
     * 填充字符串
     * @param str 输入字符串
     * @param length 目标长度
     * @param padChar 填充字符
     * @param padLeft 是否左填充
     * @return 填充后的字符串
     */
    static String pad(const String& str, uint32_t length, char padChar = ' ', bool padLeft = true);
    
    // ================================
    // 字符串验证 - 匹配前端验证功能
    // ================================
    
    /**
     * 检查字符串是否为空 - 匹配前端验证需求
     * @param str 输入字符串
     * @param trimFirst 是否先去除空白字符
     * @return 是否为空
     */
    static bool isEmpty(const String& str, bool trimFirst = true);
    
    /**
     * 检查字符串是否为数字
     * @param str 输入字符串
     * @param allowFloat 是否允许浮点数
     * @return 是否为数字
     */
    static bool isNumeric(const String& str, bool allowFloat = true);
    
    /**
     * 检查字符串是否为有效的邮箱地址
     * @param str 输入字符串
     * @return 是否为有效邮箱
     */
    static bool isValidEmail(const String& str);
    
    /**
     * 检查字符串是否为有效的URL
     * @param str 输入字符串
     * @return 是否为有效URL
     */
    static bool isValidURL(const String& str);
    
    /**
     * 检查字符串是否为有效的IP地址
     * @param str 输入字符串
     * @param allowIPv6 是否允许IPv6
     * @return 是否为有效IP地址
     */
    static bool isValidIP(const String& str, bool allowIPv6 = false);
    
    /**
     * 检查字符串是否为有效的MAC地址
     * @param str 输入字符串
     * @return 是否为有效MAC地址
     */
    static bool isValidMAC(const String& str);
    
    /**
     * 验证信号格式 - 匹配前端validateSignalFormat()
     * @param signalId 信号ID
     * @return 是否为有效信号格式
     */
    static bool validateSignalFormat(const String& signalId);
    
    // ================================
    // 字符串转换 - 匹配前端类型转换
    // ================================
    
    /**
     * 转换大小写
     * @param str 输入字符串
     * @param caseType 大小写类型
     * @return 转换后的字符串
     */
    static String convertCase(const String& str, CaseType caseType);
    
    /**
     * 字符串转整数
     * @param str 输入字符串
     * @param defaultValue 默认值
     * @return 整数值
     */
    static int32_t toInt(const String& str, int32_t defaultValue = 0);
    
    /**
     * 字符串转长整数
     * @param str 输入字符串
     * @param defaultValue 默认值
     * @return 长整数值
     */
    static int64_t toLong(const String& str, int64_t defaultValue = 0);
    
    /**
     * 字符串转浮点数
     * @param str 输入字符串
     * @param defaultValue 默认值
     * @return 浮点数值
     */
    static double toDouble(const String& str, double defaultValue = 0.0);
    
    /**
     * 字符串转布尔值
     * @param str 输入字符串
     * @param defaultValue 默认值
     * @return 布尔值
     */
    static bool toBool(const String& str, bool defaultValue = false);
    
    // ================================
    // 字符串编码解码
    // ================================
    
    /**
     * 编码字符串
     * @param str 输入字符串
     * @param encoding 编码类型
     * @return 编码后的字符串
     */
    static String encode(const String& str, EncodingType encoding);
    
    /**
     * 解码字符串
     * @param str 输入字符串
     * @param encoding 编码类型
     * @return 解码后的字符串
     */
    static String decode(const String& str, EncodingType encoding);
    
    /**
     * Base64编码
     * @param data 输入数据
     * @param length 数据长度
     * @return Base64编码字符串
     */
    static String base64Encode(const uint8_t* data, size_t length);
    
    /**
     * Base64解码
     * @param str Base64字符串
     * @return 解码后的数据
     */
    static std::vector<uint8_t> base64Decode(const String& str);
    
    /**
     * 十六进制编码
     * @param data 输入数据
     * @param length 数据长度
     * @param uppercase 是否使用大写
     * @return 十六进制字符串
     */
    static String hexEncode(const uint8_t* data, size_t length, bool uppercase = false);
    
    /**
     * 十六进制解码
     * @param str 十六进制字符串
     * @return 解码后的数据
     */
    static std::vector<uint8_t> hexDecode(const String& str);
    
    // ================================
    // ID生成 - 匹配前端generateId()
    // ================================
    
    /**
     * 生成唯一ID - 匹配前端generateId()统一格式
     * @param prefix 前缀
     * @param useTimestamp 是否使用时间戳
     * @return 生成的ID
     */
    static String generateId(const String& prefix = "id", bool useTimestamp = true);
    
    /**
     * 生成UUID
     * @param version UUID版本（4表示随机UUID）
     * @return UUID字符串
     */
    static String generateUUID(uint8_t version = 4);
    
    /**
     * 生成随机字符串
     * @param length 字符串长度
     * @param charset 字符集
     * @return 随机字符串
     */
    static String generateRandomString(uint32_t length, const String& charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");
    
    // ================================
    // 字符串搜索和匹配
    // ================================
    
    /**
     * 检查字符串是否包含子字符串
     * @param str 输入字符串
     * @param substring 子字符串
     * @param ignoreCase 是否忽略大小写
     * @return 是否包含
     */
    static bool contains(const String& str, const String& substring, bool ignoreCase = false);
    
    /**
     * 检查字符串是否以指定字符串开头
     * @param str 输入字符串
     * @param prefix 前缀
     * @param ignoreCase 是否忽略大小写
     * @return 是否以指定字符串开头
     */
    static bool startsWith(const String& str, const String& prefix, bool ignoreCase = false);
    
    /**
     * 检查字符串是否以指定字符串结尾
     * @param str 输入字符串
     * @param suffix 后缀
     * @param ignoreCase 是否忽略大小写
     * @return 是否以指定字符串结尾
     */
    static bool endsWith(const String& str, const String& suffix, bool ignoreCase = false);
    
    /**
     * 查找子字符串位置
     * @param str 输入字符串
     * @param substring 子字符串
     * @param startPos 开始位置
     * @param ignoreCase 是否忽略大小写
     * @return 子字符串位置（-1表示未找到）
     */
    static int indexOf(const String& str, const String& substring, uint32_t startPos = 0, bool ignoreCase = false);
    
    /**
     * 从右侧查找子字符串位置
     * @param str 输入字符串
     * @param substring 子字符串
     * @param ignoreCase 是否忽略大小写
     * @return 子字符串位置（-1表示未找到）
     */
    static int lastIndexOf(const String& str, const String& substring, bool ignoreCase = false);
    
    /**
     * 正则表达式匹配
     * @param str 输入字符串
     * @param pattern 正则表达式模式
     * @param ignoreCase 是否忽略大小写
     * @return 是否匹配
     */
    static bool regexMatch(const String& str, const String& pattern, bool ignoreCase = false);
    
    /**
     * 正则表达式查找
     * @param str 输入字符串
     * @param pattern 正则表达式模式
     * @param ignoreCase 是否忽略大小写
     * @return 匹配结果列表
     */
    static std::vector<String> regexFind(const String& str, const String& pattern, bool ignoreCase = false);
    
    // ================================
    // 字符串比较和排序
    // ================================
    
    /**
     * 比较字符串
     * @param str1 字符串1
     * @param str2 字符串2
     * @param ignoreCase 是否忽略大小写
     * @return 比较结果（-1: str1<str2, 0: str1==str2, 1: str1>str2）
     */
    static int compare(const String& str1, const String& str2, bool ignoreCase = false);
    
    /**
     * 计算字符串相似度
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度（0.0-1.0）
     */
    static double similarity(const String& str1, const String& str2);
    
    /**
     * 计算编辑距离
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 编辑距离
     */
    static uint32_t editDistance(const String& str1, const String& str2);
    
    // ================================
    // 字符串安全和清理
    // ================================
    
    /**
     * 清理字符串中的危险字符
     * @param str 输入字符串
     * @param allowedChars 允许的字符集
     * @return 清理后的字符串
     */
    static String sanitize(const String& str, const String& allowedChars = "");
    
    /**
     * 转义特殊字符
     * @param str 输入字符串
     * @param escapeType 转义类型
     * @return 转义后的字符串
     */
    static String escape(const String& str, EncodingType escapeType = EncodingType::JSON);
    
    /**
     * 反转义特殊字符
     * @param str 输入字符串
     * @param escapeType 转义类型
     * @return 反转义后的字符串
     */
    static String unescape(const String& str, EncodingType escapeType = EncodingType::JSON);
    
    /**
     * 计算字符串哈希值
     * @param str 输入字符串
     * @return 哈希值
     */
    static uint32_t hash(const String& str);
    
    /**
     * 计算字符串MD5
     * @param str 输入字符串
     * @return MD5字符串
     */
    static String md5(const String& str);

private:
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 检查字符是否为空白字符
     * @param c 字符
     * @return 是否为空白字符
     */
    static bool isWhitespace(char c);
    
    /**
     * 检查字符是否为数字
     * @param c 字符
     * @return 是否为数字
     */
    static bool isDigit(char c);
    
    /**
     * 检查字符是否为字母
     * @param c 字符
     * @return 是否为字母
     */
    static bool isAlpha(char c);
    
    /**
     * 检查字符是否为字母或数字
     * @param c 字符
     * @return 是否为字母或数字
     */
    static bool isAlphaNumeric(char c);
    
    /**
     * 转换字符为小写
     * @param c 字符
     * @return 小写字符
     */
    static char toLowerCase(char c);
    
    /**
     * 转换字符为大写
     * @param c 字符
     * @return 大写字符
     */
    static char toUpperCase(char c);
    
    /**
     * 获取随机数
     * @param min 最小值
     * @param max 最大值
     * @return 随机数
     */
    static uint32_t getRandom(uint32_t min, uint32_t max);
};

#endif // STRING_UTILS_H
