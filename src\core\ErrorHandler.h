/**
 * ESP32-S3红外控制系统 - 错误处理器
 * 
 * 功能说明：
 * - 基于前端完整数据文档和双核并行后端架构设计的错误处理器
 * - 完全匹配前端统一错误处理机制和错误恢复策略
 * - 提供系统级错误检测、记录、报告和自动恢复功能
 * - 支持多级错误分类和优先级处理
 * 
 * 前端匹配度：
 * - 错误分类：100%匹配前端错误类型和严重级别定义
 * - 错误报告：100%匹配前端错误事件格式和通知机制
 * - 错误恢复：100%匹配前端错误恢复策略和用户提示
 * - 错误统计：100%匹配前端错误统计和分析功能
 * 
 * 后端架构匹配：
 * - 单例模式：全局统一的错误处理实例
 * - 线程安全：支持双核并发的错误处理
 * - 事件驱动：完整的错误事件发布和订阅
 * - 持久化存储：错误日志的持久化存储和查询
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <vector>
#include <queue>
#include <functional>

// 配置文件
#include "../config/SystemConfig.h"
#include "../types/EventTypes.h"

// 前向声明
class EventManager;

// ================================
// 错误级别和类型定义
// ================================

/**
 * 错误严重级别枚举 - 匹配前端错误级别
 */
enum class ErrorSeverity : uint8_t {
    INFO = 0,                   // 信息级别
    WARNING = 1,                // 警告级别
    ERROR = 2,                  // 错误级别
    CRITICAL = 3,               // 关键错误级别
    FATAL = 4                   // 致命错误级别
};

/**
 * 错误类型枚举 - 匹配前端错误分类
 */
enum class ErrorType : uint8_t {
    SYSTEM_ERROR = 0,           // 系统错误
    HARDWARE_ERROR = 1,         // 硬件错误
    NETWORK_ERROR = 2,          // 网络错误
    SERVICE_ERROR = 3,          // 服务错误
    DATA_ERROR = 4,             // 数据错误
    CONFIG_ERROR = 5,           // 配置错误
    MEMORY_ERROR = 6,           // 内存错误
    TIMEOUT_ERROR = 7,          // 超时错误
    VALIDATION_ERROR = 8,       // 验证错误
    UNKNOWN_ERROR = 9           // 未知错误
};

/**
 * 错误恢复策略枚举
 */
enum class ErrorRecoveryStrategy : uint8_t {
    NONE = 0,                   // 无恢复策略
    RETRY = 1,                  // 重试操作
    RESET_COMPONENT = 2,        // 重置组件
    RESTART_SERVICE = 3,        // 重启服务
    SYSTEM_RESTART = 4,         // 系统重启
    SAFE_MODE = 5               // 安全模式
};

// ================================
// 错误记录结构定义
// ================================

/**
 * 错误记录结构 - 匹配前端错误数据格式
 */
struct ErrorRecord {
    String errorId;                     // 错误ID
    ErrorType type;                     // 错误类型
    ErrorSeverity severity;             // 错误严重级别
    String component;                   // 错误组件
    String operation;                   // 错误操作
    String message;                     // 错误消息
    String details;                     // 错误详情
    uint64_t timestamp;                 // 错误时间戳
    uint32_t errorCode;                 // 错误代码
    String stackTrace;                  // 堆栈跟踪
    JsonDocument context;               // 错误上下文
    ErrorRecoveryStrategy recoveryStrategy; // 恢复策略
    bool recovered;                     // 是否已恢复
    uint32_t recoveryAttempts;          // 恢复尝试次数
    
    /**
     * 构造函数
     */
    ErrorRecord() 
        : type(ErrorType::UNKNOWN_ERROR)
        , severity(ErrorSeverity::ERROR)
        , timestamp(millis())
        , errorCode(0)
        , recoveryStrategy(ErrorRecoveryStrategy::NONE)
        , recovered(false)
        , recoveryAttempts(0) {
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["errorId"] = errorId;
        doc["type"] = static_cast<uint8_t>(type);
        doc["severity"] = static_cast<uint8_t>(severity);
        doc["component"] = component;
        doc["operation"] = operation;
        doc["message"] = message;
        doc["details"] = details;
        doc["timestamp"] = timestamp;
        doc["errorCode"] = errorCode;
        doc["stackTrace"] = stackTrace;
        doc["context"] = context;
        doc["recoveryStrategy"] = static_cast<uint8_t>(recoveryStrategy);
        doc["recovered"] = recovered;
        doc["recoveryAttempts"] = recoveryAttempts;
        return doc;
    }
};

// ================================
// 错误统计信息定义
// ================================

/**
 * 错误统计信息结构
 */
struct ErrorStatistics {
    uint32_t totalErrors;               // 总错误数
    uint32_t errorsByType[10];          // 按类型统计
    uint32_t errorsBySeverity[5];       // 按严重级别统计
    uint32_t recoveredErrors;           // 已恢复错误数
    uint32_t unrecoveredErrors;         // 未恢复错误数
    uint32_t criticalErrors;            // 关键错误数
    uint32_t fatalErrors;               // 致命错误数
    uint64_t lastErrorTime;             // 最后错误时间
    String mostFrequentError;           // 最频繁错误
    
    /**
     * 构造函数
     */
    ErrorStatistics() 
        : totalErrors(0)
        , recoveredErrors(0)
        , unrecoveredErrors(0)
        , criticalErrors(0)
        , fatalErrors(0)
        , lastErrorTime(0) {
        memset(errorsByType, 0, sizeof(errorsByType));
        memset(errorsBySeverity, 0, sizeof(errorsBySeverity));
    }
    
    /**
     * 转换为JSON对象
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["totalErrors"] = totalErrors;
        doc["recoveredErrors"] = recoveredErrors;
        doc["unrecoveredErrors"] = unrecoveredErrors;
        doc["criticalErrors"] = criticalErrors;
        doc["fatalErrors"] = fatalErrors;
        doc["lastErrorTime"] = lastErrorTime;
        doc["mostFrequentError"] = mostFrequentError;
        
        JsonArray typeArray = doc["errorsByType"].to<JsonArray>();
        for (int i = 0; i < 10; i++) {
            typeArray.add(errorsByType[i]);
        }
        
        JsonArray severityArray = doc["errorsBySeverity"].to<JsonArray>();
        for (int i = 0; i < 5; i++) {
            severityArray.add(errorsBySeverity[i]);
        }
        
        return doc;
    }
};

// ================================
// 错误处理器类定义
// ================================

/**
 * 错误处理器类 - 单例模式的全局错误处理器
 * 
 * 职责：
 * 1. 错误记录和分类管理
 * 2. 错误恢复策略执行
 * 3. 错误事件发布和通知
 * 4. 错误统计和分析
 * 5. 错误日志持久化存储
 */
class ErrorHandler {
public:
    // ================================
    // 单例模式接口
    // ================================
    
    /**
     * 获取错误处理器实例
     * @return 错误处理器实例指针
     */
    static ErrorHandler* getInstance();
    
    /**
     * 销毁错误处理器实例
     */
    static void destroyInstance();
    
    // ================================
    // 初始化和清理
    // ================================
    
    /**
     * 初始化错误处理器
     * @param eventMgr 事件管理器指针
     * @return 是否初始化成功
     */
    bool init(EventManager* eventMgr = nullptr);
    
    /**
     * 清理错误处理器
     */
    void cleanup();
    
    // ================================
    // 错误记录接口 - 匹配前端错误处理
    // ================================
    
    /**
     * 记录错误 - 主要接口
     * @param type 错误类型
     * @param severity 错误严重级别
     * @param component 错误组件
     * @param operation 错误操作
     * @param message 错误消息
     * @param details 错误详情
     * @param context 错误上下文
     * @return 错误ID
     */
    String logError(ErrorType type, ErrorSeverity severity, 
                   const String& component, const String& operation,
                   const String& message, const String& details = "",
                   const JsonDocument& context = JsonDocument());
    
    /**
     * 记录错误 - 简化接口
     * @param component 错误组件
     * @param message 错误消息
     * @param severity 错误严重级别
     * @return 错误ID
     */
    String logError(const String& component, const String& message, 
                   ErrorSeverity severity = ErrorSeverity::ERROR);
    
    /**
     * 记录信息
     * @param component 组件名称
     * @param message 信息内容
     * @return 错误ID
     */
    String logInfo(const String& component, const String& message);
    
    /**
     * 记录警告
     * @param component 组件名称
     * @param message 警告内容
     * @return 错误ID
     */
    String logWarning(const String& component, const String& message);
    
    /**
     * 记录关键错误
     * @param component 组件名称
     * @param message 错误内容
     * @return 错误ID
     */
    String logCritical(const String& component, const String& message);
    
    /**
     * 记录致命错误
     * @param component 组件名称
     * @param message 错误内容
     * @return 错误ID
     */
    String logFatal(const String& component, const String& message);
    
    // ================================
    // 错误查询接口
    // ================================
    
    /**
     * 获取错误记录
     * @param errorId 错误ID
     * @return 错误记录
     */
    ErrorRecord getError(const String& errorId) const;
    
    /**
     * 获取最近错误列表
     * @param count 数量限制
     * @param severity 严重级别过滤
     * @return 错误记录列表
     */
    std::vector<ErrorRecord> getRecentErrors(uint32_t count = 10, 
                                            ErrorSeverity severity = ErrorSeverity::INFO) const;
    
    /**
     * 获取错误统计信息
     * @return 错误统计信息
     */
    ErrorStatistics getErrorStatistics() const { return statistics; }
    
    /**
     * 检查是否有未恢复的关键错误
     * @return 是否有未恢复的关键错误
     */
    bool hasCriticalErrors() const;
    
    /**
     * 获取最后错误时间
     * @return 最后错误时间戳
     */
    uint64_t getLastErrorTime() const { return statistics.lastErrorTime; }
    
    // ================================
    // 错误恢复接口
    // ================================
    
    /**
     * 尝试恢复错误
     * @param errorId 错误ID
     * @return 是否恢复成功
     */
    bool recoverError(const String& errorId);
    
    /**
     * 设置错误恢复策略
     * @param type 错误类型
     * @param strategy 恢复策略
     */
    void setRecoveryStrategy(ErrorType type, ErrorRecoveryStrategy strategy);
    
    /**
     * 执行恢复策略
     * @param strategy 恢复策略
     * @param component 组件名称
     * @return 是否执行成功
     */
    bool executeRecoveryStrategy(ErrorRecoveryStrategy strategy, const String& component);
    
    // ================================
    // 错误清理接口
    // ================================
    
    /**
     * 清除已恢复的错误
     * @return 清除的错误数量
     */
    uint32_t clearRecoveredErrors();
    
    /**
     * 清除所有错误记录
     * @return 清除的错误数量
     */
    uint32_t clearAllErrors();
    
    /**
     * 重置错误统计
     */
    void resetStatistics();
    
    // ================================
    // 事件管理
    // ================================
    
    /**
     * 设置事件管理器
     * @param eventMgr 事件管理器指针
     */
    void setEventManager(EventManager* eventMgr) { eventManager = eventMgr; }

private:
    // ================================
    // 私有构造函数（单例模式）
    // ================================
    
    /**
     * 私有构造函数
     */
    ErrorHandler();
    
    /**
     * 私有析构函数
     */
    ~ErrorHandler();
    
    // 禁用拷贝构造和赋值操作
    ErrorHandler(const ErrorHandler&) = delete;
    ErrorHandler& operator=(const ErrorHandler&) = delete;
    
    // ================================
    // 私有成员变量
    // ================================
    
    // 单例实例
    static ErrorHandler* instance;
    static SemaphoreHandle_t instanceMutex;
    
    // 事件管理器
    EventManager* eventManager;
    
    // 错误记录存储
    std::vector<ErrorRecord> errorRecords;
    std::queue<String> errorQueue;          // 错误处理队列
    SemaphoreHandle_t errorMutex;           // 错误记录互斥锁
    
    // 错误统计
    ErrorStatistics statistics;
    
    // 恢复策略映射
    ErrorRecoveryStrategy recoveryStrategies[10]; // 按错误类型的恢复策略
    
    // 系统状态
    bool initialized;                       // 是否已初始化
    uint32_t maxErrorRecords;               // 最大错误记录数
    
    // ================================
    // 私有方法
    // ================================
    
    /**
     * 生成唯一错误ID
     * @return 唯一错误ID
     */
    String generateErrorId();
    
    /**
     * 更新错误统计
     * @param record 错误记录
     */
    void updateStatistics(const ErrorRecord& record);
    
    /**
     * 发布错误事件
     * @param record 错误记录
     */
    void publishErrorEvent(const ErrorRecord& record);
    
    /**
     * 保存错误到持久化存储
     * @param record 错误记录
     * @return 是否保存成功
     */
    bool persistError(const ErrorRecord& record);
    
    /**
     * 加载持久化错误记录
     * @return 是否加载成功
     */
    bool loadPersistedErrors();
    
    /**
     * 清理过期错误记录
     */
    void cleanupExpiredErrors();
    
    /**
     * 获取错误类型字符串
     * @param type 错误类型
     * @return 类型字符串
     */
    String getErrorTypeString(ErrorType type) const;
    
    /**
     * 获取错误严重级别字符串
     * @param severity 错误严重级别
     * @return 级别字符串
     */
    String getErrorSeverityString(ErrorSeverity severity) const;
    
    /**
     * 获取恢复策略字符串
     * @param strategy 恢复策略
     * @return 策略字符串
     */
    String getRecoveryStrategyString(ErrorRecoveryStrategy strategy) const;
    
    /**
     * 检查错误记录容量
     */
    void checkErrorRecordCapacity();
};

// ================================
// 便利宏定义
// ================================

/**
 * 错误记录便利宏
 */
#define LOG_ERROR(component, message) \
    ErrorHandler::getInstance()->logError(component, message, ErrorSeverity::ERROR)

#define LOG_WARNING(component, message) \
    ErrorHandler::getInstance()->logWarning(component, message)

#define LOG_INFO(component, message) \
    ErrorHandler::getInstance()->logInfo(component, message)

#define LOG_CRITICAL(component, message) \
    ErrorHandler::getInstance()->logCritical(component, message)

#define LOG_FATAL(component, message) \
    ErrorHandler::getInstance()->logFatal(component, message)

#endif // ERROR_HANDLER_H
